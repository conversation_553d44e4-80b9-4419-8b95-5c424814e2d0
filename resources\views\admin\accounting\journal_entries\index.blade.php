@extends('layouts.admin')

@section('title', 'Journal des écritures comptables')

@section('content')
{{-- Header glassy --}}
<div class="journal-glass-header">
    <div class="header-left">
        <span class="glass-icon"><i class="fas fa-book-open"></i></span>
        <h1>Journal des écritures</h1>
    </div>
    <div class="header-actions">
        <a href="{{ route('admin.accounting.journal_entries.create') }}" class="glass-btn glass-add" title="Nouvelle écriture">
            <i class="fas fa-plus"></i>
        </a>
        <button class="glass-btn glass-dark" id="toggle-darkmode" title="Mode sombre">
            <i class="fas fa-moon"></i>
        </button>
    </div>
</div>

{{-- Résumés glassy --}}
<div class="glass-summary-row">
    <div class="glass-summary glass-green">
        <div class="summary-label">Total Débit</div>
        <div class="summary-value">{{ number_format($entries->sum(fn($e) => $e->lines->sum('debit')), 2, ',', ' ') }} FCFA</div>
    </div>
    <div class="glass-summary glass-red">
        <div class="summary-label">Total Crédit</div>
        <div class="summary-value">{{ number_format($entries->sum(fn($e) => $e->lines->sum('credit')), 2, ',', ' ') }} FCFA</div>
    </div>
</div>

{{-- Tableau glassy --}}
<div class="glass-table-container">
    <table class="glass-table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Compte</th>
                <th>Description</th>
                <th>Débit</th>
                <th>Crédit</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($entries as $entry)
            <tr>
                <td>{{ $entry->date }}</td>
                <td>{{ $entry->account->code ?? '' }}</td>
                <td>{{ $entry->description }}</td>
                <td>{{ number_format($entry->lines->sum('debit'), 2, ',', ' ') }} FCFA</td>
                <td>{{ number_format($entry->lines->sum('credit'), 2, ',', ' ') }} FCFA</td>
                <td>
                    <a href="{{ route('admin.accounting.journal_entries.edit', $entry->id) }}" class="glass-btn glass-edit" title="Modifier"><i class="fas fa-pen"></i></a>
                    <form action="{{ route('admin.accounting.journal_entries.destroy', $entry->id) }}" method="POST" style="display:inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="glass-btn glass-delete" title="Supprimer"><i class="fas fa-trash"></i></button>
                    </form>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@endsection

@push('styles')
<style>
body {
    background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
}
body.dark {
    background: linear-gradient(135deg, #232526 0%, #0f2027 100%);
}
.journal-glass-header {
    display: flex; justify-content: space-between; align-items: center;
    padding: 2rem 3rem; margin: 2rem auto 1rem auto; border-radius: 2rem;
    backdrop-filter: blur(12px); background: rgba(255,255,255,0.25);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.15);
    border: 1px solid rgba(255,255,255,0.18);
    max-width: 1100px;
}
body.dark .journal-glass-header {
    background: rgba(30,34,53,0.35);
    color: #eee;
}
.glass-icon {
    font-size: 2.5rem; margin-right: 1rem; color: #6366f1;
}
.header-left { display: flex; align-items: center; }
.header-actions { display: flex; gap: 1rem; }
.glass-btn {
    border: none; border-radius: 50%; padding: 1rem; background: rgba(255,255,255,0.3);
    box-shadow: 0 2px 8px #6366f155; color: #6366f1; font-size: 1.2rem;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s;
    cursor: pointer;
}
.glass-btn:hover { background: #6366f1; color: #fff; }
.glass-add { background: linear-gradient(135deg, #34d399 0%, #6366f1 100%); color: #fff; }
.glass-add:hover { box-shadow: 0 4px 16px #34d39955; }
.glass-dark { background: linear-gradient(135deg, #818cf8 0%, #0ea5e9 100%); color: #fff; }
.glass-summary-row { display: flex; gap: 2rem; justify-content: center; margin-bottom: 2rem; }
.glass-summary {
    min-width: 220px; padding: 2rem; border-radius: 2rem;
    backdrop-filter: blur(10px); background: rgba(255,255,255,0.25);
    box-shadow: 0 4px 24px #6366f144; text-align: center;
    font-weight: 600; font-size: 1.1rem;
}
.glass-green { border-left: 6px solid #34d399; }
.glass-red { border-left: 6px solid #ef4444; }
.summary-value { font-size: 2rem; margin-top: 0.5rem; }
.glass-table-container {
    max-width: 1100px; margin: auto; border-radius: 2rem;
    overflow: hidden; box-shadow: 0 8px 32px 0 rgba(31,38,135,0.15);
    backdrop-filter: blur(8px); background: rgba(255,255,255,0.22);
}
body.dark .glass-table-container, body.dark .glass-summary {
    background: rgba(30,34,53,0.55);
    color: #eee;
}
.glass-table {
    width: 100%; border-collapse: separate; border-spacing: 0;
}
.glass-table th, .glass-table td {
    padding: 1.2rem 1rem; text-align: left;
    backdrop-filter: blur(2px);
}
.glass-table thead th {
    background: rgba(99,102,241,0.12); color: #6366f1; font-weight: bold;
    position: sticky; top: 0; z-index: 2; border-bottom: 2px solid #6366f1;
}
.glass-table tbody tr {
    transition: background 0.2s, box-shadow 0.2s;
    animation: fadeInLine 0.7s;
}
.glass-table tbody tr:hover {
    background: rgba(99,102,241,0.08);
    box-shadow: 0 2px 8px #6366f122;
}
.glass-edit { color: #10b981; }
.glass-edit:hover { background: #10b981; color: #fff; }
.glass-delete { color: #ef4444; }
.glass-delete:hover { background: #ef4444; color: #fff; }
@keyframes fadeInLine {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
@endpush

@push('scripts')
<script>
document.getElementById('toggle-darkmode').onclick = function() {
    document.body.classList.toggle('dark');
    localStorage.setItem('darkmode', document.body.classList.contains('dark'));
};
window.onload = function() {
    if(localStorage.getItem('darkmode') === 'true') document.body.classList.add('dark');
};
</script>
@endpush
