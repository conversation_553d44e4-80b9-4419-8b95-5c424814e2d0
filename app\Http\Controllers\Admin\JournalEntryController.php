<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\Account;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class JournalEntryController extends Controller
{
    // Affiche la liste des écritures comptables
    public function index(Request $request)
    {
        $query = JournalEntry::with(['lines.account', 'user']);

        // Filtres avancés
        if ($request->filled('date_from')) {
            $query->where('date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('date', '<=', $request->date_to);
        }
        if ($request->filled('reference')) {
            $query->where('reference', 'like', '%' . $request->reference . '%');
        }
        if ($request->filled('description')) {
            $query->where('description', 'like', '%' . $request->description . '%');
        }
        if ($request->filled('account_id')) {
            $query->whereHas('lines', function($q) use ($request) {
                $q->where('account_id', $request->account_id);
            });
        }

        $entries = $query->orderByDesc('date')->paginate(20);
        $accounts = \App\Models\Account::orderBy('code')->get();
        return view('admin.accounting.journal_entries.index', compact('entries', 'accounts'));
    }

    // Affiche le formulaire de création d'une écriture
    public function create()
    {
        $accounts = Account::orderBy('code')->get();
        return view('admin.accounting.journal_entries.create', compact('accounts'));
    }

    // Enregistre une nouvelle écriture comptable
    public function store(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'reference' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'lines' => 'required|array|min:2',
            'lines.*.account_id' => 'required|exists:accounts,id',
            'lines.*.debit' => 'nullable|numeric|min:0',
            'lines.*.credit' => 'nullable|numeric|min:0',
            'lines.*.label' => 'nullable|string|max:255',
            'lines.*.attachment' => 'nullable|file|max:2048',
        ]);

        // Validation de la partie double
        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($request->lines as $line) {
            $totalDebit += $line['debit'] ?? 0;
            $totalCredit += $line['credit'] ?? 0;
        }
        if (round($totalDebit, 2) !== round($totalCredit, 2)) {
            return back()->withErrors(['lines' => 'La somme des débits doit être égale à la somme des crédits (partie double).'])->withInput();
        }

        DB::transaction(function () use ($request) {
            $entry = JournalEntry::create([
                'date' => $request->date,
                'reference' => $request->reference,
                'description' => $request->description,
                'created_by' => Auth::id(),
            ]);

            foreach ($request->lines as $line) {
                $attachmentPath = null;
                if (isset($line['attachment'])) {
                    $attachmentPath = $line['attachment']->store('attachments', 'public');
                }
                JournalEntryLine::create([
                    'journal_entry_id' => $entry->id,
                    'account_id' => $line['account_id'],
                    'debit' => $line['debit'] ?? 0,
                    'credit' => $line['credit'] ?? 0,
                    'label' => $line['label'] ?? null,
                    'attachment' => $attachmentPath,
                ]);
            }
        });

        return redirect()->route('admin.accounting.journal_entries.index')->with('success', 'Écriture comptable enregistrée.');
    }

    // Affiche le détail d'une écriture
    public function show($id)
    {
        $entry = JournalEntry::with('lines.account', 'user')->findOrFail($id);
        return view('admin.accounting.journal_entries.show', compact('entry'));
    }

    // Affiche le formulaire d'édition
    public function edit($id)
    {
        $entry = JournalEntry::with('lines')->findOrFail($id);
        $accounts = Account::orderBy('code')->get();
        return view('admin.accounting.journal_entries.edit', compact('entry', 'accounts'));
    }

    // Met à jour une écriture
    public function update(Request $request, $id)
    {
        // (Similaire à store, à adapter si besoin)
    }

    // Supprime une écriture
    public function destroy($id)
    {
        $entry = JournalEntry::findOrFail($id);
        $entry->delete();
        return redirect()->route('admin.accounting.journal_entries.index')->with('success', 'Écriture supprimée.');
    }
}
