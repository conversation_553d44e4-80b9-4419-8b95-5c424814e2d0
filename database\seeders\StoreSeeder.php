<?php

namespace Database\Seeders;

use App\Models\Store;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class StoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer quelques boutiques de test
        $stores = [
            [
                'name' => 'Boutique Centrale',
                'slug' => Str::slug('Boutique Centrale'),
                'address' => 'Avenue de la République, Centre-ville',
                'phone' => '+237 123 456 789',
                'email' => '<EMAIL>',
                'is_active' => true,
                'opening_hours' => json_encode([
                    'monday' => ['08:00-18:00'],
                    'tuesday' => ['08:00-18:00'],
                    'wednesday' => ['08:00-18:00'],
                    'thursday' => ['08:00-18:00'],
                    'friday' => ['08:00-18:00'],
                    'saturday' => ['09:00-15:00'],
                    'sunday' => []
                ]),
                'description' => 'Boutique principale ZIAD'
            ],
            [
                'name' => 'ZIAD Akwa',
                'slug' => Str::slug('ZIAD Akwa'),
                'address' => 'Quartier Akwa, Rue des Palmiers',
                'phone' => '+237 234 567 890',
                'email' => '<EMAIL>',
                'is_active' => true,
                'opening_hours' => json_encode([
                    'monday' => ['08:00-18:00'],
                    'tuesday' => ['08:00-18:00'],
                    'wednesday' => ['08:00-18:00'],
                    'thursday' => ['08:00-18:00'],
                    'friday' => ['08:00-18:00'],
                    'saturday' => ['09:00-15:00'],
                    'sunday' => []
                ]),
                'description' => 'Succursale Akwa'
            ],
            [
                'name' => 'ZIAD Bonabéri',
                'slug' => Str::slug('ZIAD Bonabéri'),
                'address' => 'Bonabéri, Route principale',
                'phone' => '+237 345 678 901',
                'email' => '<EMAIL>',
                'is_active' => true,
                'opening_hours' => json_encode([
                    'monday' => ['08:00-18:00'],
                    'tuesday' => ['08:00-18:00'],
                    'wednesday' => ['08:00-18:00'],
                    'thursday' => ['08:00-18:00'],
                    'friday' => ['08:00-18:00'],
                    'saturday' => ['09:00-15:00'],
                    'sunday' => []
                ]),
                'description' => 'Succursale Bonabéri'
            ]
        ];

        // Créer un manager pour chaque boutique
        foreach ($stores as $storeData) {
            // Créer un manager
            $manager = User::factory()->create([
                'role' => 'user',
                'email' => 'manager.' . strtolower(explode('@', $storeData['email'])[0]) . '@ziad.com'
            ]);

            // Créer la boutique avec son manager
            $store = Store::create(array_merge($storeData, ['manager_id' => $manager->id]));

            // Créer quelques employés pour la boutique
            $cashiers = User::factory(2)->create(['role' => 'user']);
            $billers = User::factory(2)->create(['role' => 'user']);

            // Attacher les employés à la boutique
            foreach ($cashiers as $cashier) {
                $store->staff()->attach($cashier->id, ['role' => 'user']);
            }
            foreach ($billers as $biller) {
                $store->staff()->attach($biller->id, ['role' => 'user']);
            }
        }
    }
}
