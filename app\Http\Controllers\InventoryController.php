<?php

namespace App\Http\Controllers;

use App\Models\Stock;
use App\Models\Product;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    public function index()
    {
        $stocks = Stock::with(['product', 'shop'])
            ->orderBy('quantity')
            ->paginate(15);

        return view('inventory.index', compact('stocks'));
    }

    public function create()
    {
        $products = Product::all();
        $shops = Shop::all();
        return view('inventory.create', compact('products', 'shops'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'shop_id' => 'required|exists:shops,id',
            'quantity' => 'required|numeric|min:0',
            'min_quantity' => 'required|numeric|min:0',
            'max_quantity' => 'nullable|numeric|min:0',
            'reorder_point' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'unit' => 'required|string|max:50'
        ]);

        Stock::create($validated);

        return redirect()->route('inventory.index')
            ->with('success', 'Stock ajouté avec succès');
    }

    public function show(Stock $inventory)
    {
        $inventory->load(['product', 'shop']);
        return view('inventory.show', compact('inventory'));
    }

    public function edit(Stock $inventory)
    {
        $products = Product::all();
        $shops = Shop::all();
        return view('inventory.edit', compact('inventory', 'products', 'shops'));
    }

    public function update(Request $request, Stock $inventory)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'shop_id' => 'required|exists:shops,id',
            'quantity' => 'required|numeric|min:0',
            'min_quantity' => 'required|numeric|min:0',
            'max_quantity' => 'nullable|numeric|min:0',
            'reorder_point' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'unit' => 'required|string|max:50'
        ]);

        $inventory->update($validated);

        return redirect()->route('inventory.index')
            ->with('success', 'Stock mis à jour avec succès');
    }

    public function destroy(Stock $inventory)
    {
        $inventory->delete();
        return redirect()->route('inventory.index')
            ->with('success', 'Stock supprimé avec succès');
    }

    public function adjustQuantity(Request $request, Stock $inventory)
    {
        $validated = $request->validate([
            'adjustment' => 'required|numeric',
            'notes' => 'required|string|max:255'
        ]);

        DB::transaction(function () use ($inventory, $validated) {
            $inventory->quantity += $validated['adjustment'];
            $inventory->notes = $validated['notes'];
            $inventory->save();
        });

        return redirect()->back()->with('success', 'Quantité ajustée avec succès');
    }
}
