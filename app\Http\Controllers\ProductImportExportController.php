<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Stock;
use App\Models\Shop;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ProductsImport;
use App\Exports\ProductsExport;
use App\Exports\StocksExport;
use App\Exports\StockMovementsExport;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class ProductImportExportController extends Controller
{
    public function importForm()
    {
        $categories = Category::orderBy('name')->get();
        return view('products.import', compact('categories'));
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls|max:10240', // max 10MB
            'category_id' => 'required|exists:categories,id'
        ]);

        try {
            DB::beginTransaction();

            $import = new ProductsImport($request->category_id);
            Excel::import($import, $request->file('file'));

            // Récupérer les résultats de l'import
            $results = $import->getResults();

            DB::commit();

            return redirect()
                ->route('products.index')
                ->with('success', sprintf(
                    'Import terminé avec succès. %d produits importés, %d erreurs.',
                    $results['success'],
                    $results['errors']
                ))
                ->with('import_errors', $results['error_messages']);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de l\'import : ' . $e->getMessage());
        }
    }

    public function exportCatalogue(Request $request)
    {
        try {
            // Générer le nom du fichier avec la date
            $filename = 'catalogue_produits_' . now()->format('Y-m-d_His') . '.xlsx';

            // Filtrer les produits selon les paramètres
            $query = Product::query();

            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            return Excel::download(
                new ProductsExport($query),
                $filename
            );

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de l\'export : ' . $e->getMessage());
        }
    }

    public function exportStocks(Request $request)
    {
        try {
            // Générer le nom du fichier avec la date
            $filename = 'stocks_' . now()->format('Y-m-d_His') . '.xlsx';

            // Filtrer les stocks selon les paramètres
            $query = Stock::with(['product', 'shop']);

            if ($request->has('shop_id')) {
                $query->where('shop_id', $request->shop_id);
            }

            if ($request->has('stock_status')) {
                switch ($request->stock_status) {
                    case 'low':
                        $query->whereColumn('quantity', '<=', 'min_stock');
                        break;
                    case 'out':
                        $query->where('quantity', 0);
                        break;
                    case 'available':
                        $query->where('quantity', '>', 0);
                        break;
                }
            }

            return Excel::download(
                new StocksExport($query),
                $filename
            );

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de l\'export : ' . $e->getMessage());
        }
    }

    public function exportMovements(Request $request)
    {
        try {
            // Générer le nom du fichier avec la date
            $filename = 'mouvements_stock_' . now()->format('Y-m-d_His') . '.xlsx';

            // Filtrer les mouvements selon les paramètres
            $query = StockMovement::with(['stock.product', 'stock.shop', 'user']);

            if ($request->has('shop_id')) {
                $query->whereHas('stock', function ($q) use ($request) {
                    $q->where('shop_id', $request->shop_id);
                });
            }

            if ($request->has('date_start')) {
                $query->whereDate('created_at', '>=', $request->date_start);
            }

            if ($request->has('date_end')) {
                $query->whereDate('created_at', '<=', $request->date_end);
            }

            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            return Excel::download(
                new StockMovementsExport($query),
                $filename
            );

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de l\'export : ' . $e->getMessage());
        }
    }

    public function downloadTemplate()
    {
        $path = storage_path('app/templates/import_produits_template.xlsx');
        return response()->download($path, 'modele_import_produits.xlsx');
    }
}
