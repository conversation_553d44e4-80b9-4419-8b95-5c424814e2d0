<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use App\Models\Product;
use App\Models\Stock;
use App\Models\Supply;
use App\Models\SupplyRequest;
use App\Notifications\LowStockNotification;
use App\Notifications\SupplyValidationNotification;
use App\Notifications\SupplyReceivedNotification;
use App\Notifications\SupplyRequestValidationNotification;
use App\Notifications\SupplyRequestProcessedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;

class SupplyNotificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $admin;
    private $mainShop;
    private $annexShop;
    private $product;

    protected function setUp(): void
    {
        parent::setUp();

        Notification::fake();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->mainShop = Shop::factory()->create(['is_main' => true]);
        $this->annexShop = Shop::factory()->create(['is_main' => false]);
        $this->product = Product::factory()->create();
    }

    /** @test */
    public function it_sends_low_stock_notification()
    {
        $manager = User::factory()->create(['role' => 'manager']);
        $this->annexShop->update(['manager_id' => $manager->id]);

        $stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->annexShop->id,
            'quantity' => 5,
            'min_stock' => 10
        ]);

        // Simuler une vente qui déclenche la notification
        $stock->decrement('quantity', 2);

        Notification::assertSentTo(
            $manager,
            LowStockNotification::class,
            function ($notification) use ($stock) {
                return $notification->stock->id === $stock->id;
            }
        );
    }

    /** @test */
    public function it_sends_supply_validation_notification()
    {
        $this->actingAs($this->admin);

        $supply = Supply::factory()->create([
            'shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);

        $this->put(route('supplies.validate', $supply));

        Notification::assertSentTo(
            $this->mainShop->manager,
            SupplyValidationNotification::class,
            function ($notification) use ($supply) {
                return $notification->supply->id === $supply->id;
            }
        );
    }

    /** @test */
    public function it_sends_supply_received_notification()
    {
        $this->actingAs($this->admin);

        $supply = Supply::factory()->create([
            'shop_id' => $this->mainShop->id,
            'status' => 'validated',
            'validated_by' => $this->admin->id
        ]);

        $this->put(route('supplies.receive', $supply), [
            'items' => [
                [
                    'id' => $supply->items->first()->id,
                    'received_quantity' => 10
                ]
            ]
        ]);

        Notification::assertSentTo(
            $supply->validator,
            SupplyReceivedNotification::class,
            function ($notification) use ($supply) {
                return $notification->supply->id === $supply->id;
            }
        );
    }

    /** @test */
    public function it_sends_supply_request_validation_notification()
    {
        $this->actingAs($this->admin);

        $supplyRequest = SupplyRequest::factory()->create([
            'from_shop_id' => $this->annexShop->id,
            'to_shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);

        $this->put(route('supply-requests.validate', $supplyRequest));

        Notification::assertSentTo(
            $this->annexShop->manager,
            SupplyRequestValidationNotification::class,
            function ($notification) use ($supplyRequest) {
                return $notification->supplyRequest->id === $supplyRequest->id;
            }
        );
    }

    /** @test */
    public function it_sends_supply_request_processed_notification()
    {
        $this->actingAs($this->admin);

        $supplyRequest = SupplyRequest::factory()->create([
            'from_shop_id' => $this->annexShop->id,
            'to_shop_id' => $this->mainShop->id,
            'status' => 'validated',
            'validated_by' => $this->admin->id
        ]);

        $this->put(route('supply-requests.process', $supplyRequest), [
            'items' => [
                [
                    'id' => $supplyRequest->items->first()->id,
                    'approved_quantity' => 10
                ]
            ]
        ]);

        Notification::assertSentTo(
            $this->annexShop->manager,
            SupplyRequestProcessedNotification::class,
            function ($notification) use ($supplyRequest) {
                return $notification->supplyRequest->id === $supplyRequest->id;
            }
        );
    }

    /** @test */
    public function it_sends_multiple_notifications_for_critical_stock_levels()
    {
        $manager = User::factory()->create(['role' => 'manager']);
        $admin = User::factory()->create(['role' => 'admin']);
        
        $this->annexShop->update(['manager_id' => $manager->id]);

        $stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->annexShop->id,
            'quantity' => 3,
            'min_stock' => 10,
            'critical_stock' => 5
        ]);

        // Simuler une vente qui déclenche les notifications
        $stock->decrement('quantity', 2);

        Notification::assertSentTo(
            [$manager, $admin],
            LowStockNotification::class
        );
    }

    /** @test */
    public function it_sends_partial_reception_notification()
    {
        $this->actingAs($this->admin);

        $supply = Supply::factory()->create([
            'shop_id' => $this->mainShop->id,
            'status' => 'validated',
            'validated_by' => $this->admin->id
        ]);

        $this->put(route('supplies.receive', $supply), [
            'items' => [
                [
                    'id' => $supply->items->first()->id,
                    'received_quantity' => 5,
                    'notes' => 'Réception partielle'
                ]
            ]
        ]);

        Notification::assertSentTo(
            $supply->validator,
            SupplyReceivedNotification::class,
            function ($notification) use ($supply) {
                return $notification->supply->status === 'partially_received';
            }
        );
    }

    /** @test */
    public function it_sends_notifications_in_correct_order()
    {
        $this->actingAs($this->admin);

        $supplyRequest = SupplyRequest::factory()->create([
            'from_shop_id' => $this->annexShop->id,
            'to_shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);

        // 1. Validation
        $this->put(route('supply-requests.validate', $supplyRequest));

        // 2. Traitement
        $this->put(route('supply-requests.process', $supplyRequest), [
            'items' => [
                [
                    'id' => $supplyRequest->items->first()->id,
                    'approved_quantity' => 10
                ]
            ]
        ]);

        Notification::assertSentInOrder([
            [SupplyRequestValidationNotification::class],
            [SupplyRequestProcessedNotification::class]
        ]);
    }
}
