@extends('layouts.admin')

@section('content')
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
        <div class="flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-white">Modifier l'Utilisateur</h2>
            <span class="px-3 py-1 text-sm rounded-full 
                @if($user->is_active) 
                    bg-green-100 text-green-800
                @else 
                    bg-red-100 text-red-800
                @endif">
                {{ $user->is_active ? 'Actif' : 'Inactif' }}
            </span>
        </div>
    </div>

    <form action="{{ route('users.update', $user) }}" method="POST" enctype="multipart/form-data" class="p-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Colonne gauche -->
            <div class="space-y-6">
                <!-- Photo de profil -->
                <div class="flex flex-col space-y-2">
                    <label class="text-sm font-medium text-gray-700">
                        Photo de profil
                    </label>
                    <div class="flex items-center space-x-4">
                        <div class="relative h-32 w-32">
                            <img id="preview" 
                                 src="{{ $user->profile_photo_url ?? asset('images/default-avatar.png') }}" 
                                 class="h-32 w-32 rounded-lg object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-200">
                                <label for="profile_photo" class="cursor-pointer">
                                    <i class="fas fa-camera text-white text-2xl"></i>
                                </label>
                            </div>
                        </div>
                        <input type="file" id="profile_photo" name="profile_photo" class="hidden" 
                               accept="image/*" onchange="previewImage(this)">
                        <div class="text-sm text-gray-500">
                            <p>Formats acceptés : JPG, PNG</p>
                            <p>Taille maximale : 1MB</p>
                            @if($user->profile_photo)
                                <button type="button" onclick="removePhoto()" 
                                        class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-trash-alt mr-1"></i>
                                    Supprimer la photo
                                </button>
                            @endif
                        </div>
                    </div>
                    @error('profile_photo')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Informations de base -->
                <div class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Nom complet</label>
                        <input type="text" name="name" id="name" 
                               value="{{ old('name', $user->name) }}"
                               class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                        @error('name')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" id="email" 
                               value="{{ old('email', $user->email) }}"
                               class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                        @error('email')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                        <input type="tel" name="phone" id="phone" 
                               value="{{ old('phone', $user->phone) }}"
                               class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                        @error('phone')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Colonne droite -->
            <div class="space-y-6">
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700">Rôle</label>
                    <select name="role" id="role"
                            class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">Sélectionnez un rôle</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->name }}" 
                                {{ old('role', $user->roles->first()->name ?? '') == $role->name ? 'selected' : '' }}>
                                {{ ucfirst($role->name) }}
                            </option>
                        @endforeach
                    </select>
                    @error('role')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="shop_id" class="block text-sm font-medium text-gray-700">Boutique</label>
                    <select name="shop_id" id="shop_id"
                            class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">Sélectionnez une boutique</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}" 
                                {{ old('shop_id', $user->shop_id) == $shop->id ? 'selected' : '' }}>
                                {{ $shop->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('shop_id')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700">Adresse</label>
                    <textarea name="address" id="address" rows="3"
                              class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">{{ old('address', $user->address) }}</textarea>
                    @error('address')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Mot de passe -->
                <div class="space-y-4">
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">
                            Nouveau mot de passe
                            <span class="text-gray-500">(laisser vide pour ne pas modifier)</span>
                        </label>
                        <div class="relative">
                            <input type="password" name="password" id="password"
                                   class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                            <button type="button" onclick="togglePassword('password')"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-eye text-gray-400"></i>
                            </button>
                        </div>
                        @error('password')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                            Confirmer le nouveau mot de passe
                        </label>
                        <div class="relative">
                            <input type="password" name="password_confirmation" id="password_confirmation"
                                   class="mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200">
                            <button type="button" onclick="togglePassword('password_confirmation')"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-eye text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statut -->
                <div class="flex items-center space-x-3">
                    <label class="text-sm font-medium text-gray-700">Statut du compte</label>
                    <button type="button" onclick="toggleStatus()"
                            class="px-4 py-2 rounded-md text-sm font-medium {{ $user->is_active ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-green-100 text-green-700 hover:bg-green-200' }}">
                        <i class="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }} mr-2"></i>
                        {{ $user->is_active ? 'Désactiver' : 'Activer' }} le compte
                    </button>
                </div>
            </div>
        </div>

        <!-- Informations supplémentaires -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div>
                    <span class="font-medium">Créé le :</span>
                    <span>{{ $user->created_at->format('d/m/Y H:i') }}</span>
                </div>
                <div>
                    <span class="font-medium">Dernière modification :</span>
                    <span>{{ $user->updated_at->format('d/m/Y H:i') }}</span>
                </div>
                <div>
                    <span class="font-medium">Dernière connexion :</span>
                    <span>{{ $user->last_login_at ? $user->last_login_at->format('d/m/Y H:i') : 'Jamais' }}</span>
                </div>
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="mt-6 flex justify-end space-x-3">
            <a href="{{ route('users.index') }}"
               class="inline-flex items-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-times mr-2"></i>
                Annuler
            </a>
            <button type="submit"
                    class="inline-flex items-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-save mr-2"></i>
                Enregistrer les modifications
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview').src = e.target.result;
            }
            reader.readAsDataURL(input.files[0]);
        }
    }

    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = field.nextElementSibling.querySelector('i');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    function removePhoto() {
        document.getElementById('preview').src = '{{ asset('images/default-avatar.png') }}';
        // Ajouter un champ caché pour indiquer la suppression de la photo
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'remove_photo';
        input.value = '1';
        document.querySelector('form').appendChild(input);
    }

    function toggleStatus() {
        Swal.fire({
            title: '{{ $user->is_active ? 'Désactiver' : 'Activer' }} le compte?',
            text: "{{ $user->is_active ? 'L\'utilisateur ne pourra plus se connecter.' : 'L\'utilisateur pourra à nouveau se connecter.' }}",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '{{ $user->is_active ? '#d33' : '#3085d6' }}',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Oui, {{ $user->is_active ? 'désactiver' : 'activer' }}!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = "{{ route('users.toggle-status', $user) }}";
            }
        });
    }
</script>
@endpush
@endsection
