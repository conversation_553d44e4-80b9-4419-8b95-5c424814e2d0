<?php

namespace App\Http\Controllers;

use App\Models\Stock;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StockRequest;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StocksExport;
use App\Imports\StocksImport;

class StockController extends Controller
{
    public function index()
    {
        $stocks = Stock::with(['product', 'shop'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('stock.index', compact('stocks'));
    }

    public function create()
    {
        $products = Product::all();
        return view('stock.create', compact('products'));
    }

    public function store(StockRequest $request)
    {
        DB::beginTransaction();
        try {
            $stock = Stock::create($request->validated());
            
            // Mettre à jour le stock du produit
            $product = Product::findOrFail($request->product_id);
            if ($stock->type === 'supply') {
                $product->increment('quantity', $request->quantity);
            } else {
                $product->decrement('quantity', $request->quantity);
            }

            DB::commit();
            return redirect()->route('stock.index')->with('success', 'Stock mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour du stock');
        }
    }

    public function show(Stock $stock)
    {
        $stock->load(['product', 'shop']);
        return view('stock.show', compact('stock'));
    }

    public function edit(Stock $stock)
    {
        $products = Product::all();
        return view('stock.edit', compact('stock', 'products'));
    }

    public function update(StockRequest $request, Stock $stock)
    {
        DB::beginTransaction();
        try {
            // Annuler l'ancien mouvement de stock
            $product = Product::findOrFail($stock->product_id);
            if ($stock->type === 'supply') {
                $product->decrement('quantity', $stock->quantity);
            } else {
                $product->increment('quantity', $stock->quantity);
            }

            // Appliquer le nouveau mouvement
            $stock->update($request->validated());
            if ($stock->type === 'supply') {
                $product->increment('quantity', $request->quantity);
            } else {
                $product->decrement('quantity', $request->quantity);
            }

            DB::commit();
            return redirect()->route('stock.index')->with('success', 'Stock mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour du stock');
        }
    }

    public function destroy(Stock $stock)
    {
        DB::beginTransaction();
        try {
            // Annuler le mouvement de stock
            $product = Product::findOrFail($stock->product_id);
            if ($stock->type === 'supply') {
                $product->decrement('quantity', $stock->quantity);
            } else {
                $product->increment('quantity', $stock->quantity);
            }

            $stock->delete();

            DB::commit();
            return redirect()->route('stock.index')->with('success', 'Mouvement de stock supprimé avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Une erreur est survenue lors de la suppression du mouvement');
        }
    }

    public function export()
    {
        return Excel::download(new StocksExport, 'stocks.xlsx');
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls'
        ]);

        try {
            Excel::import(new StocksImport, $request->file('file'));
            return back()->with('success', 'Import réussi');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de l\'import : ' . $e->getMessage());
        }
    }
}
