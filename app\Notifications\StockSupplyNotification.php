<?php

namespace App\Notifications;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StockSupplyNotification extends Notification
{
    use Queueable;

    public $supply;

    public function __construct(Stock $supply)
    {
        $this->supply = $supply;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Nouvel approvisionnement de stock')
            ->line('Un nouvel approvisionnement de stock a été créé.')
            ->line("Produit: {$this->supply->product->name}")
            ->line("Quantité: {$this->supply->quantity}")
            ->line("Statut: {$this->supply->status}")
            ->line('Merci de votre attention !');
    }
}
