<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\Expense;
use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:accountant']);
    }

    public function index()
    {
        $today = Carbon::today();
        $startOfMonth = Carbon::now()->startOfMonth();

        // Statistiques financières
        $stats = [
            'monthly_revenue' => Sale::whereMonth('created_at', now()->month)
                                ->sum('total_amount'),
            'monthly_expenses' => Expense::whereMonth('date', now()->month)
                                ->sum('amount'),
            'pending_checks' => Payment::where('payment_method', 'check')
                                ->where('status', 'pending')
                                ->count(),
            'pending_invoices' => Supply::where('payment_status', 'pending')
                                ->count()
        ];

        // Chèques en attente
        $pendingChecks = Payment::with(['sale.customer'])
            ->where('payment_method', 'check')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Factures fournisseurs en attente
        $pendingInvoices = Supply::with('supplier')
            ->where('payment_status', 'pending')
            ->orderBy('due_date')
            ->take(5)
            ->get();

        // Revenus par mode de paiement (pour graphique)
        $revenueByMethod = Payment::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->select('payment_method', DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get();

        // Dépenses par catégorie (pour graphique)
        $expensesByCategory = Expense::whereMonth('date', now()->month)
            ->select('category', DB::raw('SUM(amount) as total'))
            ->groupBy('category')
            ->get();

        // Évolution des revenus sur les 12 derniers mois
        $revenueHistory = Sale::select(
            DB::raw('YEAR(created_at) as year'),
            DB::raw('MONTH(created_at) as month'),
            DB::raw('SUM(total_amount) as total')
        )
            ->where('created_at', '>=', now()->subMonths(11))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return view('accountant.dashboard', compact(
            'stats',
            'pendingChecks',
            'pendingInvoices',
            'revenueByMethod',
            'expensesByCategory',
            'revenueHistory'
        ));
    }
}
