@extends('layouts.admin')

@section('content')
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- En-tête -->
    <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
        <div class="flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-white">Détails de l'Utilisateur</h2>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 text-sm rounded-full 
                    @if($user->is_active) 
                        bg-green-100 text-green-800
                    @else 
                        bg-red-100 text-red-800
                    @endif">
                    {{ $user->is_active ? 'Actif' : 'Inactif' }}
                </span>
                @foreach($user->roles as $role)
                    <span class="px-3 py-1 text-sm rounded-full
                        @if($role->name === 'admin')
                            bg-red-100 text-red-800
                        @elseif($role->name === 'manager')
                            bg-blue-100 text-blue-800
                        @elseif($role->name === 'cashier')
                            bg-green-100 text-green-800
                        @elseif($role->name === 'storekeeper')
                            bg-yellow-100 text-yellow-800
                        @elseif($role->name === 'accountant')
                            bg-purple-100 text-purple-800
                        @else
                            bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst($role->name) }}
                    </span>
                @endforeach
            </div>
        </div>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Colonne de gauche : Photo et informations de base -->
            <div class="md:col-span-1">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex flex-col items-center">
                        <img src="{{ $user->profile_photo_url }}" 
                             alt="{{ $user->name }}"
                             class="h-32 w-32 rounded-full object-cover shadow-lg">
                        
                        <h3 class="mt-4 text-xl font-medium text-gray-900">{{ $user->name }}</h3>
                        <p class="text-gray-500">{{ $user->email }}</p>
                        
                        <div class="mt-4 flex space-x-2">
                            @can('edit users')
                            <a href="{{ route('users.edit', $user) }}"
                               class="inline-flex items-center px-3 py-1 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                <i class="fas fa-edit mr-2"></i>
                                Modifier
                            </a>
                            @endcan
                            
                            @if(auth()->user()->id !== $user->id)
                                @can('delete users')
                                <button onclick="confirmDelete('delete-form-{{ $user->id }}')"
                                        class="inline-flex items-center px-3 py-1 rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                    <i class="fas fa-trash-alt mr-2"></i>
                                    Supprimer
                                </button>
                                <form id="delete-form-{{ $user->id }}" 
                                      action="{{ route('users.destroy', $user) }}" 
                                      method="POST" 
                                      class="hidden">
                                    @csrf
                                    @method('DELETE')
                                </form>
                                @endcan
                            @endif
                        </div>
                    </div>

                    <div class="mt-6 pt-4">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-phone text-gray-400 w-5"></i>
                                <span class="ml-2 text-gray-600">{{ $user->phone }}</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-map-marker-alt text-gray-400 w-5 mt-1"></i>
                                <span class="ml-2 text-gray-600">{{ $user->address }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-store text-gray-400 w-5"></i>
                                <span class="ml-2 text-gray-600">{{ $user->shop->name ?? 'Non assigné' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Colonne centrale : Activité récente -->
            <div class="md:col-span-2">
                <!-- Statistiques -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                                <i class="fas fa-shopping-cart text-white"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Ventes totales</p>
                                <p class="text-lg font-semibold text-gray-900">{{ $user->sales_count ?? 0 }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                                <i class="fas fa-money-bill-wave text-white"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">CA Généré</p>
                                <p class="text-lg font-semibold text-gray-900">
                                    {{ number_format($user->total_sales ?? 0, 0, ',', ' ') }} FCFA
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                                <i class="fas fa-users text-white"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Clients servis</p>
                                <p class="text-lg font-semibold text-gray-900">{{ $user->customers_count ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activité récente -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 bg-gray-50">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Activité récente
                        </h3>
                    </div>
                    <div>
                        <ul role="list" class="divide-y divide-gray-200">
                            @forelse($user->activities ?? [] as $activity)
                                <li class="px-4 py-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            <span class="inline-flex items-center justify-center h-8 w-8 rounded-full 
                                                @if($activity->type === 'sale')
                                                    bg-green-100
                                                @elseif($activity->type === 'login')
                                                    bg-blue-100
                                                @elseif($activity->type === 'inventory')
                                                    bg-yellow-100
                                                @else
                                                    bg-gray-100
                                                @endif">
                                                <i class="fas 
                                                    @if($activity->type === 'sale')
                                                        fa-shopping-cart text-green-600
                                                    @elseif($activity->type === 'login')
                                                        fa-sign-in-alt text-blue-600
                                                    @elseif($activity->type === 'inventory')
                                                        fa-clipboard-list text-yellow-600
                                                    @else
                                                        fa-clock text-gray-600
                                                    @endif">
                                                </i>
                                            </span>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900">
                                                {{ $activity->description }}
                                            </p>
                                            <p class="text-sm text-gray-500">
                                                {{ $activity->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                </li>
                            @empty
                                <li class="px-4 py-4 text-center text-gray-500">
                                    Aucune activité récente
                                </li>
                            @endforelse
                        </ul>
                    </div>
                </div>

                <!-- Informations supplémentaires -->
                <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 bg-gray-50">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Informations supplémentaires
                        </h3>
                    </div>
                    <div>
                        <dl>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Date de création</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    {{ $user->created_at->format('d/m/Y H:i') }}
                                </dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Dernière modification</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    {{ $user->updated_at->format('d/m/Y H:i') }}
                                </dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Dernière connexion</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    {{ $user->last_login_at ? $user->last_login_at->format('d/m/Y H:i') : 'Jamais' }}
                                </dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Permissions</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($user->getAllPermissions() as $permission)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $permission->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function confirmDelete(formId) {
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Cette action est irréversible!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById(formId).submit();
            }
        });
    }
</script>
@endpush
@endsection
