<?php
namespace App\Http\Controllers\Admin\Accounting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Exports\ExpenseExport;
use Maatwebsite\Excel\Facades\Excel;
use PDF;

class ExpenseController extends Controller
{
    // Liste des dépenses avec recherche et filtrage
    public function index(Request $request)
    {
        $query = $this->filteredExpenses($request)->with('category', 'user');
        $expenses = $query->paginate(20);
        $categories = ExpenseCategory::orderBy('name')->get();
        return view('admin.accounting.expenses.index', compact('expenses', 'categories'));
    }

    // Formulaire de création
    public function create()
    {
        $categories = ExpenseCategory::orderBy('name')->get();
        return view('admin.accounting.expenses.create', compact('categories'));
    }

    // Enregistrement d'une dépense
    public function store(Request $request)
    {
        $data = $request->validate([
            'description' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'category_id' => 'nullable|exists:expense_categories,id',
            'date' => 'required|date',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
        ]);
        $data['user_id'] = Auth::id();
        if ($request->hasFile('attachment')) {
            $data['attachment'] = $request->file('attachment')->store('expenses');
        }
        Expense::create($data);
        return redirect()->route('admin.accounting.expenses.index')->with('success', 'Dépense ajoutée avec succès.');
    }

    // Formulaire d'édition
    public function edit(Expense $expense)
    {
        $categories = ExpenseCategory::orderBy('name')->get();
        return view('admin.accounting.expenses.edit', compact('expense', 'categories'));
    }

    // Mise à jour
    public function update(Request $request, Expense $expense)
    {
        $data = $request->validate([
            'description' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'category_id' => 'nullable|exists:expense_categories,id',
            'date' => 'required|date',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
        ]);
        if ($request->hasFile('attachment')) {
            if ($expense->attachment) Storage::delete($expense->attachment);
            $data['attachment'] = $request->file('attachment')->store('expenses');
        }
        $expense->update($data);
        return redirect()->route('admin.accounting.expenses.index')->with('success', 'Dépense modifiée avec succès.');
    }

    // Suppression
    public function destroy(Expense $expense)
    {
        if ($expense->attachment) Storage::delete($expense->attachment);
        $expense->delete();
        return redirect()->route('admin.accounting.expenses.index')->with('success', 'Dépense supprimée.');
    }

    // Création rapide de catégorie (AJAX)
    public function quickAddCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
        ]);
        $cat = ExpenseCategory::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);
        return response()->json($cat);
    }

    // Statistiques pour la vue
    public function stats(Request $request)
    {
        $query = Expense::query();
        if ($request->filled('from')) $query->where('date', '>=', $request->from);
        if ($request->filled('to')) $query->where('date', '<=', $request->to);
        if ($request->filled('category_id')) $query->where('category_id', $request->category_id);
        if ($request->filled('q')) $query->where('description', 'like', '%'.$request->q.'%');
        $total = $query->sum('amount');
        // Top catégories
        $topCategories = Expense::selectRaw('expense_categories.name, SUM(expenses.amount) as total')
            ->join('expense_categories', 'expenses.category_id', '=', 'expense_categories.id')
            ->when($request->filled('from'), fn($q) => $q->where('expenses.date', '>=', $request->from))
            ->when($request->filled('to'), fn($q) => $q->where('expenses.date', '<=', $request->to))
            ->when($request->filled('category_id'), fn($q) => $q->where('expenses.category_id', $request->category_id))
            ->when($request->filled('q'), fn($q) => $q->where('expenses.description', 'like', '%'.$request->q.'%'))
            ->groupBy('expense_categories.name')
            ->orderByDesc('total')
            ->limit(3)
            ->get();
        // Evolution par mois
        $evolution = Expense::selectRaw('DATE_FORMAT(date, "%Y-%m") as month, SUM(amount) as total')
            ->when($request->filled('from'), fn($q) => $q->where('date', '>=', $request->from))
            ->when($request->filled('to'), fn($q) => $q->where('date', '<=', $request->to))
            ->when($request->filled('category_id'), fn($q) => $q->where('category_id', $request->category_id))
            ->when($request->filled('q'), fn($q) => $q->where('description', 'like', '%'.$request->q.'%'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();
        return response()->json([
            'total' => $total,
            'topCategories' => $topCategories,
            'evolution' => $evolution,
        ]);
    }

    // Export Excel
    public function exportExcel(Request $request)
    {
        $expenses = $this->filteredExpenses($request)->with('category')->get();
        return Excel::download(new ExpenseExport($expenses), 'depenses.xlsx');
    }

    // Export PDF
    public function exportPdf(Request $request)
    {
        $expenses = $this->filteredExpenses($request)->with('category')->get();
        $pdf = PDF::loadView('admin.accounting.expenses.export_pdf', compact('expenses'));
        return $pdf->download('depenses.pdf');
    }

    // Filtrage commun (DRY)
    protected function filteredExpenses(Request $request)
    {
        $query = Expense::query();
        if ($request->filled('from')) $query->where('date', '>=', $request->from);
        if ($request->filled('to')) $query->where('date', '<=', $request->to);
        if ($request->filled('category_id')) $query->where('category_id', $request->category_id);
        if ($request->filled('q')) $query->where('description', 'like', '%'.$request->q.'%');
        return $query->orderByDesc('date');
    }
}
