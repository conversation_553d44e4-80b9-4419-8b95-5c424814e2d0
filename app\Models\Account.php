<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\JournalEntryLine;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'code', 'name', 'type', 'parent_id', 'description'
    ];

    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    public function journalEntryLines()
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    // Total débit sur une période
    public function totalDebit($from = null, $to = null)
    {
        $query = $this->journalEntryLines();
        if ($from) $query->whereDate('created_at', '>=', $from);
        if ($to) $query->whereDate('created_at', '<=', $to);
        return $query->sum('debit');
    }

    // Total crédit sur une période
    public function totalCredit($from = null, $to = null)
    {
        $query = $this->journalEntryLines();
        if ($from) $query->whereDate('created_at', '>=', $from);
        if ($to) $query->whereDate('created_at', '<=', $to);
        return $query->sum('credit');
    }

    // Solde sur une période (débit - crédit)
    public function balance($from = null, $to = null)
    {
        return $this->totalDebit($from, $to) - $this->totalCredit($from, $to);
    }
}
