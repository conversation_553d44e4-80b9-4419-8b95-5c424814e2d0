<?php

namespace App\Http\Controllers;

use App\Models\Recovery;
use App\Models\Sale;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class RecoveryController extends Controller
{
    public function index()
    {
        $recoveries = Recovery::with(['sale', 'shop', 'user'])
            ->latest()
            ->paginate(10);

        $overdueCount = Recovery::where('status', '!=', 'paid')
            ->whereDate('due_date', '<', now())
            ->count();

        return view('recoveries.index', compact('recoveries', 'overdueCount'));
    }

    public function create()
    {
        $sales = Sale::where('status', '!=', 'paid')->get();
        $shops = Shop::all();
        return view('recoveries.create', compact('sales', 'shops'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'shop_id' => 'required|exists:shops,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,card,transfer,check',
            'due_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:255',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['status'] = 'pending';
        $validated['remaining_amount'] = $validated['amount'];
        $validated['reference_number'] = 'REC-' . Str::random(8);

        $recovery = Recovery::create($validated);

        return redirect()->route('recoveries.index')
            ->with('success', 'Recouvrement créé avec succès');
    }

    public function show(Recovery $recovery)
    {
        return view('recoveries.show', compact('recovery'));
    }

    public function edit(Recovery $recovery)
    {
        $sales = Sale::where('status', '!=', 'paid')->get();
        $shops = Shop::all();
        return view('recoveries.edit', compact('recovery', 'sales', 'shops'));
    }

    public function update(Request $request, Recovery $recovery)
    {
        $validated = $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'shop_id' => 'required|exists:shops,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,card,transfer,check',
            'status' => 'required|in:pending,partial,paid',
            'due_date' => 'required|date',
            'payment_date' => 'nullable|date',
            'notes' => 'nullable|string|max:255',
        ]);

        if ($validated['status'] === 'paid' && !$validated['payment_date']) {
            $validated['payment_date'] = now();
            $validated['remaining_amount'] = 0;
        } elseif ($validated['status'] === 'partial') {
            $validated['remaining_amount'] = $request->input('remaining_amount', $recovery->remaining_amount);
        }

        $recovery->update($validated);

        return redirect()->route('recoveries.index')
            ->with('success', 'Recouvrement mis à jour avec succès');
    }

    public function destroy(Recovery $recovery)
    {
        $recovery->delete();

        return redirect()->route('recoveries.index')
            ->with('success', 'Recouvrement supprimé avec succès');
    }

    public function dashboard()
    {
        $stats = [
            'total_pending' => Recovery::where('status', '!=', 'paid')->sum('remaining_amount'),
            'overdue_count' => Recovery::where('status', '!=', 'paid')
                ->whereDate('due_date', '<', now())
                ->count(),
            'this_month_recovered' => Recovery::whereMonth('payment_date', now()->month)
                ->whereYear('payment_date', now()->year)
                ->sum('amount'),
            'critical_recoveries' => Recovery::where('status', '!=', 'paid')
                ->whereDate('due_date', '<', now()->subDays(30))
                ->with(['sale', 'shop'])
                ->get()
        ];

        return view('recoveries.dashboard', compact('stats'));
    }
}
