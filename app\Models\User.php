<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use App\Observers\UserObserver;
use App\Events\UserLoggedIn;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'avatar_path',
        'shop_id',
        'role',
        'status',
        'is_active',
        'last_seen_at',
        'email_verified_at',
        'phone',
        'address',
        'language',
        'timezone'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_seen_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean'
    ];

    /**
     * Get the shop that the user belongs to
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get the sales for the user
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the stock movements for the user
     */
    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Get the orders for the user
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Check if the user is online
     */
    public function isOnline()
    {
        return $this->last_seen_at && $this->last_seen_at->gt(now()->subMinutes(5));
    }

    /**
     * Trigger the login event for notifications
     */
    public function login()
    {
        event(new UserLoggedIn($this));
    }

    /**
     * Get the avatar URL for the user
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar_path) {
            // Si avatar_path commence par 'upload', afficher via /upload/ (public)
            $normalized = str_replace('\\', '/', $this->avatar_path);
            if (strpos($normalized, 'upload/') === 0) {
                return asset($normalized);
            }
            // Sinon, fallback Laravel Storage
            return asset('storage/' . $normalized);
        }
        
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF&size=150';
    }
}
