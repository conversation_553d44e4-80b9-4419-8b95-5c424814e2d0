<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>ZIAD Sarl - Connexion</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --secondary: #60a5fa;
            --accent: #1e3a8a;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --background: #ffffff;
            --card-bg: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.08);
            --border-color: #e5e7eb;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--background);
            font-family: 'Montserrat', sans-serif;
            color: var(--text-dark);
            position: relative;
            overflow-x: hidden;
            padding: 20px;
        }
        
        .background-pattern {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.04;
            background-image: 
                radial-gradient(var(--primary-dark) 2px, transparent 2px),
                radial-gradient(var(--primary) 2px, transparent 2px);
            background-size: 50px 50px;
            background-position: 0 0, 25px 25px;
            pointer-events: none;
        }
        
        .accent-shape {
            position: fixed;
            z-index: 0;
            pointer-events: none;
        }
        
        .accent-shape-1 {
            top: -150px;
            right: -150px;
            width: 400px;
            height: 400px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            opacity: 0.1;
        }
        
        .accent-shape-2 {
            bottom: -150px;
            left: -150px;
            width: 350px;
            height: 350px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent), var(--primary-dark));
            opacity: 0.1;
        }
        
        .container {
            position: relative;
            width: 100%;
            max-width: 420px;
            z-index: 2;
        }
        
        .login-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px var(--shadow-color);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
            animation: fade-in 0.8s ease forwards;
        }
        
        @keyframes fade-in {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .logo-ziad {
            width: 160px;
            height: 160px;
            object-fit: contain;
        }
        
        .title {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        
        .title p {
            font-size: 14px;
            color: var(--text-light);
            font-weight: 400;
        }
        
        .input-group {
            position: relative;
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-dark);
        }
        
        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-icon {
            position: absolute;
            left: 15px;
            color: var(--primary);
            z-index: 1;
        }
        
        .input-group input {
            width: 100%;
            padding: 14px 14px 14px 45px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background: #f9fafb;
            color: var(--text-dark);
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
            background: #ffffff;
        }
        
        .input-group input::placeholder {
            color: #a0aec0;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 13px;
        }
        
        .remember {
            display: flex;
            align-items: center;
        }
        
        .remember input[type="checkbox"] {
            appearance: none;
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
            background: #f9fafb;
        }
        
        .remember input[type="checkbox"]:checked {
            background: #2563eb;
            border-color: #2563eb;
        }
        
        .remember input[type="checkbox"]:checked::before {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }
        
        .remember span {
            color: var(--text-light);
        }
        
        .forgot-password {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }
        
        .forgot-password:hover {
            color: #1e3a8a;
            text-decoration: underline;
        }
        
        .login-button {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 10px;
            background: var(--primary);
            color: white;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
        }
        
        .login-button:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
        }
        
        .login-button:active {
            transform: translateY(0);
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            color: var(--text-light);
            font-size: 12px;
        }
        
        .footer .highlight {
            color: var(--primary);
            font-weight: 600;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: var(--text-light);
            font-size: 12px;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }
        
        .divider::before {
            margin-right: 10px;
        }
        
        .divider::after {
            margin-left: 10px;
        }
        
        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f9fafb;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .close-button:hover {
            background: #f1f1f1;
            color: var(--text-dark);
        }
        
        @media (max-width: 500px) {
            .login-card {
                padding: 30px 20px;
                border-radius: 15px;
            }
            
            .logo-ziad {
                width: 100px;
                height: 100px;
            }
            
            .title h1 {
                font-size: 20px;
            }
            
            .input-group input {
                padding: 12px 12px 12px 40px;
            }
            
            .login-button {
                padding: 12px;
            }
            
            .remember-forgot {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="background-pattern"></div>
    <div class="accent-shape accent-shape-1"></div>
    <div class="accent-shape accent-shape-2"></div>
    
    <div class="container">
        <div class="login-card">
            <span class="close-button">&times;</span>
            
            <div class="logo-container">
                <img src="{{ asset('img/logo_ziad_ok.png') }}" alt="Logo ZIAD SARL" class="logo-ziad">
            </div>
            
            <div class="title">
                <h1>Bienvenue</h1>
                <p>Connectez-vous à votre espace personnel</p>
            </div>
            
            <form method="POST" action="/">
                @csrf
                <div class="input-group">
                    <label for="email">Adresse e-mail</label>
                    <div class="input-wrapper">
                        <span class="input-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg>
                        </span>
                        <input id="email" type="email" name="email" required autofocus placeholder="Entrez votre adresse e-mail">
                    </div>
                </div>
                
                <div class="input-group">
                    <label for="password">Mot de passe</label>
                    <div class="input-wrapper">
                        <span class="input-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5 8a5 5 0 0110 0v2a2 2 0 01-2 2v1a2 2 0 01-2 2v1a2 2 0 01-2 2v1a2 2 0 01-2 2v1a2 2 0 01-2 2V8z" clip-rule="evenodd" /></svg>
                        </span>
                        <input id="password" type="password" name="password" required placeholder="Entrez votre mot de passe">
                    </div>
                </div>
                
                <div class="remember-forgot">
                    <div class="remember">
                        <input type="checkbox" id="remember" name="remember">
                        <span>Se souvenir de moi</span>
                    </div>
                    <a href="{{ route('password.request') }}" class="forgot-password">Mot de passe oublié ?</a>
                </div>
                
                <button type="submit" class="login-button">Se connecter</button>
            </form>
            
            <div class="divider">Connexion sécurisée</div>
            
            <div class="footer">
                <p>Copyright &copy; 2025 <span class="highlight">ZIAD SARL</span>. Développé par <span class="highlight">MOMK-Solutions</span></p>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.close-button').addEventListener('click', function() {
            const card = document.querySelector('.login-card');
            card.style.transform = 'scale(0.95)';
            card.style.opacity = '0';
            setTimeout(() => {
                console.log('Close clicked');
            }, 300);
        });
    </script>
</body>
</html>
