@extends('layouts.admin')

@section('title', 'Plan Comptable')

@section('content')
<div class="container mx-auto py-8">
    <!-- En-tête moderne -->
    <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4">
        <div>
            <h1 class="text-4xl font-bold text-blue-800 flex items-center">
                <i class="fas fa-sitemap animate-pulse mr-3"></i> Plan Comptable
            </h1>
            <div class="text-sm text-gray-500 mt-1">
                <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded-full mr-2">{{ $accounts->count() }} comptes</span>
                <span class="bg-green-100 text-green-700 px-2 py-1 rounded-full">Mis à jour le {{ now()->format('d/m/Y H:i') }}</span>
            </div>
        </div>
        <form method="GET" class="flex gap-2 items-center w-full md:w-auto">
            <input type="text" name="search" placeholder="🔍 Rechercher..." value="{{ request('search') }}" class="form-control rounded-lg w-full md:w-48">
            <select name="type" class="form-control rounded-lg">
                <option value="">Tous types</option>
                @foreach($types as $type)
                    <option value="{{ $type }}" {{ request('type')==$type ? 'selected' : '' }}>{{ ucfirst($type) }}</option>
                @endforeach
            </select>
            <button class="btn btn-primary"><i class="fas fa-search"></i></button>
        </form>
        <a href="{{ route('admin.accounting.accounts.create') }}" class="fixed md:static bottom-8 right-8 z-50 bg-gradient-to-r from-green-400 to-green-600 text-white px-6 py-3 rounded-full shadow-2xl hover:from-green-500 hover:to-green-700 transition-all text-lg font-bold flex items-center animate-bounce">
            <i class="fas fa-plus mr-2"></i> Nouveau compte
        </a>
    </div>

    <!-- Affichage spécial des banques -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center"><i class="fas fa-university text-indigo-500 mr-2"></i> Banques Africaines</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($accounts->where('parent_id', $accounts->where('code', '1000')->first()?->id) as $bank)
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 border-l-4 border-blue-500 shadow-lg rounded-xl p-5 flex flex-col hover:shadow-2xl transition-all">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-university text-2xl text-blue-700 mr-2"></i>
                        <span class="font-bold text-lg text-blue-900">{{ $bank->name }}</span>
                    </div>
                    <div class="text-gray-600 text-sm mb-2">{{ $bank->description }}</div>
                    <div class="flex flex-wrap gap-2 mt-2">
                        @foreach($accounts->where('parent_id', $bank->id) as $sub)
                            <span class="inline-block bg-white border border-blue-200 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold shadow-sm">
                                <i class="fas fa-credit-card mr-1"></i> {{ $sub->name }}
                            </span>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Arbre graphique des comptes -->
    @if(count($tree))
        <div class="bg-white shadow-xl rounded-xl p-6">
            <div class="overflow-x-auto">
                <ul class="account-tree">
                    @foreach($tree as $account)
                        @include('admin.accounting.accounts.partials.account_tree_node', ['account' => $account, 'level' => 0])
                    @endforeach
                </ul>
            </div>
        </div>
    @else
        <div class="text-center text-gray-400 py-12">
            <i class="fas fa-folder-open text-5xl mb-4"></i>
            <p class="text-lg">Aucun compte trouvé. <a href="{{ route('admin.accounting.accounts.create') }}" class="text-green-600 hover:underline font-bold">Créer le premier compte</a></p>
        </div>
    @endif
</div>

@push('styles')
<style>
.account-tree {
    list-style: none;
    padding-left: 0;
}
.account-tree li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}
.account-tree li:before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0.8rem;
    width: 1rem;
    height: 2px;
    background: #cbd5e1;
}
.account-tree li:first-child:before {
    display: none;
}
.account-tree .account-node {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background 0.2s;
}
.account-tree .account-node:hover {
    background: #f1f5f9;
}
.account-tree .account-actions {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}
.account-tree .account-type {
    font-size: 0.85em;
    padding: 0.1em 0.5em;
    border-radius: 0.3em;
    font-weight: 500;
}
.account-type.actif { background: #dbeafe; color: #2563eb; }
.account-type.passif { background: #fef9c3; color: #d97706; }
.account-type.charge { background: #fee2e2; color: #dc2626; }
.account-type.produit { background: #dcfce7; color: #059669; }
.account-type.autre, .account-type['capitaux propres'] { background: #e0e7ef; color: #64748b; }
</style>
@endpush

@push('scripts')
<script>
// Animation bouton + (FAB)
document.querySelectorAll('.animate-bounce').forEach(btn => {
    btn.addEventListener('mouseenter', () => btn.classList.remove('animate-bounce'));
    btn.addEventListener('mouseleave', () => btn.classList.add('animate-bounce'));
});
</script>
@endpush

@endsection
