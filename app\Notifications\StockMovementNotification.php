<?php

namespace App\Notifications;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StockMovementNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $stock;
    protected $oldQuantity;
    protected $type;

    public function __construct(Stock $stock, $oldQuantity, $type)
    {
        $this->stock = $stock;
        $this->oldQuantity = $oldQuantity;
        $this->type = $type;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $subject = match($this->type) {
            'in' => 'Entrée de stock',
            'out' => 'Sortie de stock',
            'adjustment' => 'Ajustement de stock',
            'transfer' => 'Transfert de stock',
            default => 'Mouvement de stock'
        };

        $message = match($this->type) {
            'in' => 'Une entrée de stock a été enregistrée',
            'out' => 'Une sortie de stock a été enregistrée',
            'adjustment' => 'Un ajustement de stock a été effectué',
            'transfer' => 'Un transfert de stock a été effectué',
            default => 'Un mouvement de stock a été enregistré'
        };

        return (new MailMessage)
            ->subject($subject . ' - ' . $this->stock->product->name)
            ->line($message . ' pour le produit suivant :')
            ->line('Produit : ' . $this->stock->product->name)
            ->line('Boutique : ' . $this->stock->shop->name)
            ->line('Ancienne quantité : ' . $this->oldQuantity)
            ->line('Nouvelle quantité : ' . $this->stock->quantity)
            ->line('Différence : ' . ($this->stock->quantity - $this->oldQuantity))
            ->action('Voir l\'historique', route('stock.movements.history', $this->stock));
    }

    public function toArray($notifiable)
    {
        return [
            'stock_id' => $this->stock->id,
            'product_id' => $this->stock->product_id,
            'shop_id' => $this->stock->shop_id,
            'product_name' => $this->stock->product->name,
            'shop_name' => $this->stock->shop->name,
            'old_quantity' => $this->oldQuantity,
            'new_quantity' => $this->stock->quantity,
            'difference' => $this->stock->quantity - $this->oldQuantity,
            'type' => $this->type
        ];
    }
}
