<?php

namespace App\Http\Controllers;

use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ShopController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view shops')->only(['index', 'show']);
        $this->middleware('permission:create shops')->only(['create', 'store']);
        $this->middleware('permission:edit shops')->only(['edit', 'update']);
        $this->middleware('permission:delete shops')->only('destroy');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $shops = Shop::with('parentShop')->get();
        return view('shops.index', compact('shops'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $mainShops = Shop::where('is_main_shop', true)->get();
        return view('shops.create', compact('mainShops'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|unique:shops,code',
            'address' => 'required|string',
            'phone' => 'required|string',
            'email' => 'nullable|email',
            'is_main_shop' => 'boolean',
            'parent_shop_id' => [
                'nullable',
                'exists:shops,id',
                Rule::requiredIf(fn() => !$request->boolean('is_main_shop')),
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->boolean('is_main_shop') && $value !== null) {
                        $fail('Une boutique principale ne peut pas être une annexe.');
                    }
                }
            ]
        ]);

        $shop = Shop::create($validated);

        return redirect()
            ->route('shops.show', $shop)
            ->with('success', 'Boutique créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Shop $shop)
    {
        $shop->load(['users', 'childShops']);
        return view('shops.show', compact('shop'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Shop $shop)
    {
        $mainShops = Shop::where('is_main_shop', true)
            ->where('id', '!=', $shop->id)
            ->get();
        return view('shops.edit', compact('shop', 'mainShops'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Shop $shop)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', Rule::unique('shops')->ignore($shop->id)],
            'address' => 'required|string',
            'phone' => 'required|string',
            'email' => 'nullable|email',
            'is_main_shop' => 'boolean',
            'parent_shop_id' => [
                'nullable',
                'exists:shops,id',
                Rule::requiredIf(fn() => !$request->boolean('is_main_shop')),
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->boolean('is_main_shop') && $value !== null) {
                        $fail('Une boutique principale ne peut pas être une annexe.');
                    }
                }
            ]
        ]);

        $shop->update($validated);

        return redirect()
            ->route('shops.show', $shop)
            ->with('success', 'Boutique mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Shop $shop)
    {
        if ($shop->childShops()->exists()) {
            return back()->with('error', 'Impossible de supprimer une boutique qui a des annexes.');
        }

        if ($shop->users()->exists()) {
            return back()->with('error', 'Impossible de supprimer une boutique qui a des utilisateurs.');
        }

        $shop->delete();

        return redirect()
            ->route('shops.index')
            ->with('success', 'Boutique supprimée avec succès.');
    }
}
