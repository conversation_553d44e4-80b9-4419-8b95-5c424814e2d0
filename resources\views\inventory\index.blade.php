@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Gestion des Stocks</h2>
                    <a href="{{ route('inventory.create') }}" class="btn btn-primary">Nouveau Stock</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Magasin</th>
                                    <th>Quantité</th>
                                    <th>Min</th>
                                    <th>Max</th>
                                    <th>Point de commande</th>
                                    <th>Emplacement</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($stocks as $stock)
                                    <tr>
                                        <td>{{ $stock->product->name }}</td>
                                        <td>{{ $stock->shop->name }}</td>
                                        <td>{{ $stock->quantity }} {{ $stock->unit }}</td>
                                        <td>{{ $stock->min_quantity }}</td>
                                        <td>{{ $stock->max_quantity ?? '-' }}</td>
                                        <td>{{ $stock->reorder_point }}</td>
                                        <td>{{ $stock->location ?? '-' }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('inventory.show', $stock) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('inventory.edit', $stock) }}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#adjustModal{{ $stock->id }}">
                                                    <i class="fas fa-balance-scale"></i>
                                                </button>
                                            </div>

                                            <!-- Modal pour ajuster la quantité -->
                                            <div class="modal fade" id="adjustModal{{ $stock->id }}" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form action="{{ route('inventory.adjust', $stock) }}" method="POST">
                                                            @csrf
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Ajuster le stock</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="mb-3">
                                                                    <label for="adjustment" class="form-label">Ajustement</label>
                                                                    <input type="number" step="any" class="form-control" name="adjustment" required>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label for="notes" class="form-label">Notes</label>
                                                                    <textarea class="form-control" name="notes" required></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                                                <button type="submit" class="btn btn-primary">Enregistrer</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    {{ $stocks->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
