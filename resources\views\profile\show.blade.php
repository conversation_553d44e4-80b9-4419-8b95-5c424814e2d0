@extends('layouts.admin')

@section('title', 'Mon Prof<PERSON>')

@section('content')
<div class="py-6">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <h1 class="text-2xl font-semibold text-gray-900">Mon Profil</h1>
    </div>
    
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        @if (session('success'))
            <div class="mb-4 rounded-md bg-green-50 p-4 mt-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button" class="inline-flex rounded-md bg-green-50 p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-600 focus:ring-offset-green-50" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
                                <span class="sr-only">Fermer</span>
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        
        <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
            <!-- Carte du profil avec avatar -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="space-y-4">
                        <div class="mx-auto h-40 w-40 relative group">
                            @if($user->avatar_path)
                                <img src="{{ $user->avatar_url }}" 
                                     alt="Photo de profil" 
                                     class="h-40 w-40 rounded-full object-cover border-4 border-blue-100 transition-all duration-300 group-hover:border-blue-300">
                                <div class="absolute inset-0 rounded-full bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300 opacity-0 group-hover:opacity-100">
                                    <button type="button" @click="$dispatch('open-avatar-modal')" class="bg-white text-blue-600 rounded-full p-2 shadow-lg transform transition-transform duration-300 hover:scale-110">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                </div>

                            @else
                                <div class="h-40 w-40 rounded-full bg-gradient-to-r from-blue-500 to-blue-700 text-white flex items-center justify-center text-5xl font-semibold relative group">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                    <div class="absolute inset-0 rounded-full bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300 opacity-0 group-hover:opacity-100">
                                        <button type="button" @click="$dispatch('open-avatar-modal')" class="bg-white text-blue-600 rounded-full p-2 shadow-lg transform transition-transform duration-300 hover:scale-110">
                                            <i class="fas fa-camera"></i>
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>
                        
                        <div class="space-y-1">
                            <h3 class="text-xl font-medium text-gray-900">{{ $user->name }}</h3>
                            <p class="text-sm text-gray-500">{{ $user->email }}</p>
                            @if($user->phone)
                                <p class="text-sm text-gray-500">{{ $user->phone }}</p>
                            @endif
                        </div>
                        
                        <div class="flex justify-center space-x-3">
                            <div x-data="{ open: false }" x-cloak @open-avatar-modal.window="open = true">
                                <button @click="open = true" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <i class="fas fa-camera mr-2"></i> Changer la photo
                                </button>

                                <div x-show="open" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" x-ref="dialog" aria-modal="true">
                                    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                        <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="open = false" aria-hidden="true"></div>

                                        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                        
                                        <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                            <div class="absolute top-0 right-0 pt-4 pr-4">
                                                <button type="button" class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" @click="open = false">
                                                    <span class="sr-only">Fermer</span>
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="sm:flex sm:items-start">
                                                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                                    <i class="fas fa-camera text-blue-600"></i>
                                                </div>
                                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                                        Changer ma photo de profil
                                                    </h3>
                                                    <div class="mt-2">
                                                        <p class="text-sm text-gray-500">
                                                            Sélectionnez une nouvelle photo pour votre profil. Format recommandé : carré, minimum 300x300 pixels.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-5 sm:mt-4">
                                                <form action="{{ route('profile.avatar.update') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                                                    @csrf
                                                    <div>
                                                        <label for="avatar" class="block text-sm font-medium text-gray-700">
                                                            Choisir une image
                                                        </label>
                                                        <div class="mt-1 flex items-center">
                                                            <input type="file" id="avatar" name="avatar" accept="image/*" required class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                                        </div>
                                                    </div>
                                                    <div class="sm:flex sm:flex-row-reverse">
                                                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                                                            Enregistrer
                                                        </button>
                                                        <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="open = false">
                                                            Annuler
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            @if($user->avatar_path)
                                <div x-data="{ open: false }">
                                    <button @click="open = true" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                                        <i class="fas fa-trash mr-2"></i> Supprimer
                                    </button>

                                    <div x-show="open" class="fixed z-50 inset-0 overflow-y-auto" aria-labelledby="modal-title" x-ref="dialog" aria-modal="true" style="display: none;">
                                        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="open = false" aria-hidden="true"></div>

                                            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                            
                                            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                                <div class="sm:flex sm:items-start">
                                                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                                    </div>
                                                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                                            Supprimer ma photo de profil
                                                        </h3>
                                                        <div class="mt-2">
                                                            <p class="text-sm text-gray-500">
                                                                Êtes-vous sûr de vouloir supprimer votre photo de profil ? Cette action est irréversible.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                                    <form action="{{ route('profile.avatar.delete') }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                                                            Supprimer
                                                        </button>
                                                    </form>
                                                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="open = false">
                                                        Annuler
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Informations personnelles -->
            <div class="bg-white overflow-hidden shadow rounded-lg lg:col-span-2">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-user-circle text-blue-500 mr-2"></i>
                        Informations personnelles
                    </h3>
                    
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Nom complet -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-id-card text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Nom complet</dt>
                                </div>
                                <dd class="text-base text-gray-700">{{ $user->name }}</dd>
                            </div>
                            
                            <!-- Email -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-envelope text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Email</dt>
                                </div>
                                <dd class="text-base text-gray-700">{{ $user->email }}</dd>
                            </div>
                            
                            <!-- Téléphone -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-phone text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Téléphone</dt>
                                </div>
                                <dd class="text-base text-gray-700">
                                    @if($user->phone)
                                        <a href="tel:{{ $user->phone }}" class="text-blue-600 hover:text-blue-800">
                                            {{ $user->phone }}
                                        </a>
                                    @else
                                        <span class="text-gray-500 italic">Non renseigné</span>
                                    @endif
                                </dd>
                            </div>
                            
                            <!-- Langue -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-language text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Langue</dt>
                                </div>
                                <dd class="text-base text-gray-700">
                                    @if($user->language === 'fr')
                                        <span class="inline-flex items-center">
                                            <img src="https://flagcdn.com/w20/fr.png" alt="Drapeau français" class="mr-2 h-4">
                                            Français
                                        </span>
                                    @elseif($user->language === 'en')
                                        <span class="inline-flex items-center">
                                            <img src="https://flagcdn.com/w20/gb.png" alt="Drapeau anglais" class="mr-2 h-4">
                                            English
                                        </span>
                                    @else
                                        <span class="text-gray-500 italic">{{ $user->language ?? 'Non renseignée' }}</span>
                                    @endif
                                </dd>
                            </div>
                            
                            <!-- Adresse -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md sm:col-span-2">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Adresse</dt>
                                </div>
                                <dd class="text-base text-gray-700">
                                    @if($user->address)
                                        {{ $user->address }}
                                    @else
                                        <span class="text-gray-500 italic">Non renseignée</span>
                                    @endif
                                </dd>
                            </div>
                            
                            <!-- Fuseau horaire -->
                            <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md sm:col-span-2">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-clock text-blue-500 mr-2"></i>
                                    <dt class="text-sm font-medium text-gray-900">Fuseau horaire</dt>
                                </div>
                                <dd class="text-base text-gray-700">
                                    @if($user->timezone)
                                        {{ $user->timezone }}
                                    @else
                                        <span class="text-gray-500 italic">Non renseigné</span>
                                    @endif
                                </dd>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8 flex justify-end">
                        <a href="{{ route('profile.edit') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <i class="fas fa-edit mr-2"></i> Modifier mes informations
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Sécurité -->
            <div class="bg-white overflow-hidden shadow rounded-lg lg:col-span-3">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                        Sécurité
                    </h3>
                    
                    <div class="border-t border-gray-200 pt-4">
                        <div class="bg-gray-50 rounded-lg p-5 transition-all duration-200 hover:shadow-md">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div class="mb-4 sm:mb-0">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-key text-blue-500 mr-2"></i>
                                        <h4 class="text-sm font-medium text-gray-900">Mot de passe</h4>
                                    </div>
                                    <p class="text-sm text-gray-500">
                                        <i class="fas fa-clock text-gray-400 mr-1"></i>
                                        Dernière modification: {{ $user->password_changed_at ? $user->password_changed_at->diffForHumans() : 'Jamais' }}
                                    </p>
                                    <p class="text-sm text-gray-500 mt-1">
                                        <i class="fas fa-info-circle text-gray-400 mr-1"></i>
                                        Pour votre sécurité, utilisez un mot de passe fort et unique que vous ne partagez avec personne.
                                    </p>
                                </div>
                                <a href="{{ route('profile.password') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <i class="fas fa-key mr-2"></i> Modifier le mot de passe
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
