@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Gestion des Utilisateurs</h2>
                <a href="{{ route('admin.users.create') }}" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Ajouter un utilisateur
                </a>
            </div>
        </div>
    </div>

    <!-- Filtres et actions -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <!-- Recherche -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Rechercher</label>
                    <input type="text" id="search" name="search" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                           placeholder="Nom ou email...">
                </div>

                <!-- Filtre par rôle -->
                <div>
                    <label for="role-filter" class="block text-sm font-medium text-gray-700">Rôle</label>
                    <select id="role-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Tous les rôles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->name }}">{{ ucfirst($role->name) }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Filtre par boutique -->
                <div>
                    <label for="shop-filter" class="block text-sm font-medium text-gray-700">Boutique</label>
                    <select id="shop-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}">{{ $shop->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Filtre par statut -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700">Statut</label>
                    <select id="status-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Tous les statuts</option>
                        <option value="active">Actif</option>
                        <option value="inactive">Inactif</option>
                    </select>
                </div>
            </div>

            <!-- Actions groupées -->
            <div class="flex space-x-2">
                <button type="button" id="activate-selected" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50">
                    <i class="fas fa-check mr-2"></i>Activer
                </button>
                <button type="button" id="deactivate-selected" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50">
                    <i class="fas fa-ban mr-2"></i>Désactiver
                </button>
                <button type="button" id="delete-selected" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50">
                    <i class="fas fa-trash mr-2"></i>Supprimer
                </button>
            </div>
        </div>
    </div>

    <!-- Liste des utilisateurs -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="overflow-x-auto" id="users-table">
            @include('admin.users._table')
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50">
            {{ $users->links() }}
        </div>
    </div>
</div>

<!-- Modal de confirmation -->
<div id="confirm-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-4" id="modal-title"></h3>
        <div class="mt-4 flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors" id="modal-cancel">
                Annuler
            </button>
            <button type="button" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" id="modal-confirm">
                Confirmer
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentSort = { field: 'created_at', direction: 'desc' };
    const debounceTimeout = 300;
    let debounceTimer;

    // Fonction pour mettre à jour la table
    function updateTable(params = {}) {
        const queryParams = new URLSearchParams({
            ...params,
            sort: currentSort.field,
            direction: currentSort.direction
        });

        fetch(`{{ route('admin.users.index') }}?${queryParams}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('users-table').innerHTML = data.html;
            document.querySelector('.px-6.py-4.bg-gray-50').innerHTML = data.pagination;
            initializeEventListeners();
        });
    }

    // Gestionnaire de recherche et filtres
    function handleFilters() {
        const params = {
            search: document.getElementById('search').value,
            role: document.getElementById('role-filter').value,
            shop: document.getElementById('shop-filter').value,
            status: document.getElementById('status-filter').value
        };

        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => updateTable(params), debounceTimeout);
    }

    // Écouteurs d'événements pour les filtres
    ['search', 'role-filter', 'shop-filter', 'status-filter'].forEach(id => {
        document.getElementById(id).addEventListener('change', handleFilters);
        if (id === 'search') {
            document.getElementById(id).addEventListener('keyup', handleFilters);
        }
    });

    // Tri
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-sort]')) {
            const field = e.target.closest('[data-sort]').dataset.sort;
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'asc';
            }
            updateTable();
        }
    });

    // Gestion du statut
    function handleStatusToggle(userId) {
        const button = document.querySelector(`.status-toggle[data-user-id="${userId}"]`);
        const originalText = button.textContent;
        const originalClasses = button.className;
        
        // Désactiver le bouton et montrer le chargement
        button.disabled = true;
        button.textContent = 'Chargement...';
        button.className = 'status-toggle px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800';

        fetch(`{{ url('admin/users') }}/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw response;
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Mettre à jour le bouton avec le nouveau statut
                button.textContent = data.status === 'active' ? 'Actif' : 'Inactif';
                button.className = `status-toggle px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    data.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                }`;
                
                // Afficher un toast de succès
                showToast(data.message, 'success');
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            // Restaurer l'état original du bouton
            button.textContent = originalText;
            button.className = originalClasses;
            
            // Gérer l'erreur et afficher un message
            if (error instanceof Response) {
                error.json().then(data => {
                    showToast(data.message || "Une erreur est survenue", 'error');
                });
            } else {
                showToast(error.message || "Une erreur est survenue", 'error');
            }
        })
        .finally(() => {
            button.disabled = false;
        });
    }

    // Actions groupées avec gestion améliorée des erreurs
    function handleBulkAction(action) {
        const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
        
        if (selectedUsers.length === 0) {
            showToast('Veuillez sélectionner au moins un utilisateur', 'warning');
            return;
        }

        const confirmMessages = {
            'activate': 'Voulez-vous vraiment activer les utilisateurs sélectionnés ?',
            'deactivate': 'Voulez-vous vraiment désactiver les utilisateurs sélectionnés ?',
            'delete': 'Voulez-vous vraiment supprimer les utilisateurs sélectionnés ?'
        };

        // Désactiver les boutons pendant l'action
        const actionButtons = document.querySelectorAll('#activate-selected, #deactivate-selected, #delete-selected');
        actionButtons.forEach(btn => btn.disabled = true);

        showConfirmModal(confirmMessages[action], () => {
            fetch('{{ route("admin.users.bulk-action") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    user_ids: selectedUsers
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw response;
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    updateTable();
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                if (error instanceof Response) {
                    error.json().then(data => {
                        showToast(data.message || "Une erreur est survenue", 'error');
                    });
                } else {
                    showToast(error.message || "Une erreur est survenue", 'error');
                }
            })
            .finally(() => {
                actionButtons.forEach(btn => btn.disabled = false);
                document.getElementById('confirm-modal').classList.add('hidden');
            });
        });
    }

    // Fonction pour afficher les toasts
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white ${colors[type]} shadow-lg transform transition-all duration-500 translate-y-0 opacity-0`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animation d'entrée
        setTimeout(() => {
            toast.style.transform = 'translateY(0)';
            toast.style.opacity = '1';
        }, 10);

        // Animation de sortie
        setTimeout(() => {
            toast.style.transform = 'translateY(-100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 500);
        }, 3000);
    }

    // Modal de confirmation
    function showConfirmModal(message, callback) {
        const modal = document.getElementById('confirm-modal');
        const title = document.getElementById('modal-title');
        const confirmBtn = document.getElementById('modal-confirm');
        const cancelBtn = document.getElementById('modal-cancel');

        title.textContent = message;
        modal.classList.remove('hidden');

        confirmBtn.onclick = () => {
            callback();
            modal.classList.add('hidden');
        };

        cancelBtn.onclick = () => {
            modal.classList.add('hidden');
        };
    }

    // Chargement des dernières connexions
    function loadLastLogins() {
        document.querySelectorAll('tr[data-user-id]').forEach(row => {
            const userId = row.dataset.userId;
            const cell = row.querySelector('.last-login-cell');
            const spinner = cell.querySelector('.loading-spinner');
            const text = cell.querySelector('.last-login-text');

            spinner.classList.remove('hidden');
            text.classList.add('hidden');

            fetch(`{{ url('admin/users') }}/${userId}/last-login`)
                .then(response => response.json())
                .then(data => {
                    text.textContent = data.last_login;
                    spinner.classList.add('hidden');
                    text.classList.remove('hidden');
                });
        });
    }

    // Initialisation des écouteurs d'événements
    function initializeEventListeners() {
        // Sélection/désélection de tous les utilisateurs
        const selectAll = document.getElementById('select-all');
        if (selectAll) {
            selectAll.onchange = function() {
                document.querySelectorAll('.user-checkbox').forEach(cb => {
                    cb.checked = this.checked;
                });
            };
        }

        // Toggle status
        document.querySelectorAll('.status-toggle').forEach(btn => {
            btn.onclick = function() {
                if (!this.disabled) {
                    handleStatusToggle(this.dataset.userId);
                }
            };
        });

        // Boutons d'action groupée
        document.getElementById('activate-selected').onclick = () => handleBulkAction('activate');
        document.getElementById('deactivate-selected').onclick = () => handleBulkAction('deactivate');
        document.getElementById('delete-selected').onclick = () => handleBulkAction('delete');

        // Charger les dernières connexions
        loadLastLogins();
    }

    // Initialisation
    initializeEventListeners();
});
</script>
@endpush
