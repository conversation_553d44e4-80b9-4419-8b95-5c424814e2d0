<?php

namespace App\Http\Controllers\Biller;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:biller']);
    }

    public function index()
    {
        $shop = Auth::user()->shop;

        // Statistiques des factures
        $stats = [
            'daily_sales' => Sale::where('shop_id', $shop->id)
                                ->whereDate('created_at', today())
                                ->count(),
            'daily_amount' => Sale::where('shop_id', $shop->id)
                                ->whereDate('created_at', today())
                                ->sum('total_amount'),
            'pending_credit' => Sale::where('shop_id', $shop->id)
                                ->where('payment_type', 'credit')
                                ->where('status', 'pending')
                                ->count(),
            'pending_checks' => Sale::where('shop_id', $shop->id)
                                ->where('payment_type', 'check')
                                ->where('payment_status', 'pending')
                                ->count()
        ];

        // Dernières factures
        $recentSales = Sale::with(['customer', 'items.product'])
            ->where('shop_id', $shop->id)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Factures en attente de paiement
        $pendingPayments = Sale::with('customer')
            ->where('shop_id', $shop->id)
            ->where('payment_status', 'pending')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Clients récents
        $recentCustomers = Customer::whereHas('sales', function($query) use ($shop) {
            $query->where('shop_id', $shop->id);
        })
        ->withCount(['sales' => function($query) use ($shop) {
            $query->where('shop_id', $shop->id);
        }])
        ->withSum(['sales' => function($query) use ($shop) {
            $query->where('shop_id', $shop->id);
        }], 'total_amount')
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();

        return view('biller.dashboard', compact(
            'stats',
            'recentSales',
            'pendingPayments',
            'recentCustomers'
        ));
    }
}
