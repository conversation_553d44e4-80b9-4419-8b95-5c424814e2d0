@extends('layouts.cashier')

@section('title', 'Tableau de bord caissier')

@section('content')
<div class="container-fluid">
    <!-- État de la caisse -->
    @if(!$currentRegister)
    <div class="alert alert-warning" role="alert">
        <h4 class="alert-heading">Caisse fermée!</h4>
        <p>Vous devez ouvrir la caisse pour commencer les opérations de la journée.</p>
        <hr>
        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#openRegisterModal">
            <i class="fas fa-cash-register"></i> Ouvrir la caisse
        </button>
    </div>
    @else
    <div class="alert alert-success" role="alert">
        <h4 class="alert-heading">Caisse ouverte</h4>
        <p>Caisse ouverte depuis {{ $currentRegister->opening_time->format('H:i') }} avec un fond de caisse de {{ number_format($currentRegister->opening_amount, 0, ',', ' ') }} FCFA</p>
        <hr>
        <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#closeRegisterModal">
            <i class="fas fa-cash-register"></i> Fermer la caisse
        </button>
    </div>
    @endif

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Encaissements du jour</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_sales'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Espèces</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['cash_amount'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Chèques</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['check_amount'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Paiements en attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_payments'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Derniers encaissements -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Derniers encaissements</h6>
                    <a href="{{ route('cashier.payments.create') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Nouvel encaissement
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Heure</th>
                                    <th>Client</th>
                                    <th>Montant</th>
                                    <th>Mode</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentPayments as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('H:i') }}</td>
                                    <td>{{ $payment->sale->customer->name }}</td>
                                    <td>{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                                    <td>
                                        <span class="badge badge-{{ $payment->payment_method === 'cash' ? 'success' : 'info' }}">
                                            {{ $payment->payment_method_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('cashier.payments.receipt', $payment) }}" class="btn btn-sm btn-secondary">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">Aucun encaissement aujourd'hui</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Paiements en attente -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Paiements en attente</h6>
                </div>
                <div class="card-body">
                    @if($pendingSales->isEmpty())
                        <p class="text-center">Aucun paiement en attente</p>
                    @else
                        <div class="list-group">
                            @foreach($pendingSales as $sale)
                            <a href="{{ route('cashier.payments.create', ['sale' => $sale->id]) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $sale->customer->name }}</h6>
                                    <small class="text-danger">
                                        {{ number_format($sale->remaining_amount, 0, ',', ' ') }} FCFA
                                    </small>
                                </div>
                                <small>
                                    Facture #{{ $sale->invoice_number }} - 
                                    {{ $sale->created_at->format('d/m/Y') }}
                                </small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ouverture Caisse -->
<div class="modal fade" id="openRegisterModal" tabindex="-1" role="dialog" aria-labelledby="openRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('cashier.register.open') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="openRegisterModalLabel">Ouverture de caisse</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="opening_amount">Montant d'ouverture</label>
                        <input type="number" class="form-control" id="opening_amount" name="opening_amount" required>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ouvrir la caisse</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Fermeture Caisse -->
<div class="modal fade" id="closeRegisterModal" tabindex="-1" role="dialog" aria-labelledby="closeRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('cashier.register.close') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="closeRegisterModalLabel">Fermeture de caisse</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="closing_amount">Montant de fermeture</label>
                        <input type="number" class="form-control" id="closing_amount" name="closing_amount" required>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Fermer la caisse</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
