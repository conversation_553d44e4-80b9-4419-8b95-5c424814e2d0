<div class="bg-white rounded-lg shadow-xl">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">{{ $title }}</h3>
    </div>

    <div class="divide-y divide-gray-200">
        @forelse($items as $item)
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        @if(isset($item->category))
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $categoryColorClass ?? 'bg-blue-100 text-blue-800' }}">
                                {{ $item->category->name }}
                            </span>
                        @endif
                        @if(isset($item->shop))
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $shopColorClass ?? 'bg-green-100 text-green-800' }} ml-2">
                                {{ $item->shop->name }}
                            </span>
                        @endif
                    </div>
                    <div class="text-sm text-gray-500">
                        {{ isset($item->created_at) ? $item->created_at->diffForHumans() : '' }}
                    </div>
                </div>

                <div class="mt-2">
                    <p class="text-sm text-gray-900">
                        @if(isset($item->name))
                            {{ $item->name }}
                        @endif
                        @if(isset($item->total_amount))
                            {{ number_format($item->total_amount, 2) }} €
                        @endif
                    </p>
                    @if(isset($item->description))
                        <p class="mt-1 text-sm text-gray-500">
                            {{ $item->description }}
                        </p>
                    @endif
                </div>

                @if(isset($item->quantity) && isset($item->min_stock))
                    <div class="mt-2">
                        <div class="flex items-center">
                            <div class="flex-1">
                                <div class="relative pt-1">
                                    <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                                        @php
                                            $percentage = ($item->quantity / $item->min_stock) * 100;
                                            $colorClass = $percentage <= 25 ? 'bg-red-500' : ($percentage <= 50 ? 'bg-yellow-500' : 'bg-green-500');
                                        @endphp
                                        <div style="width: {{ min($percentage, 100) }}%" 
                                             class="{{ $colorClass }} shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-2 text-sm text-gray-600">
                                {{ $item->quantity }} / {{ $item->min_stock }}
                            </div>
                        </div>
                    </div>
                @endif

                @if(isset($actions))
                    <div class="mt-4 flex space-x-2">
                        {{ $actions }}
                    </div>
                @endif
            </div>
        @empty
            <div class="p-6 text-center text-gray-500">
                {{ $emptyMessage ?? 'Aucun élément à afficher' }}
            </div>
        @endforelse
    </div>

    @if(isset($footer))
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            {{ $footer }}
        </div>
    @endif
</div>
