<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'value',
    ];

    /**
     * Les attributs à caster.
     *
     * @var array
     */
    protected $casts = [
        'value' => 'json',
    ];

    /**
     * Récupère un paramètre par sa clé.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        $setting = self::where('key', $key)->first();
        
        return $setting ? $setting->value : $default;
    }

    /**
     * Définit un paramètre.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set($key, $value)
    {
        self::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );
    }

    /**
     * Récupère tous les paramètres sous forme de tableau associatif.
     *
     * @return array
     */
    public static function getAllSettings()
    {
        $settings = self::all()->pluck('value', 'key')->toArray();
        
        // Valeurs par défaut si certains paramètres n'existent pas
        $defaults = [
            'site_name' => 'OREMI FRIGO',
            'site_description' => 'Système de gestion pour OREMI FRIGO',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+237 000 000 000',
            'address' => 'Douala, Cameroun',
            'timezone' => 'Africa/Douala',
            'date_format' => 'd/m/Y H:i',
            'per_page' => 15,
            'logo' => null,
            'favicon' => null,
            'footer_text' => '© ' . date('Y') . ' OREMI FRIGO. Tous droits réservés.',
            'social_links' => [
                'facebook' => '',
                'twitter' => '',
                'instagram' => '',
                'linkedin' => '',
            ],
            'maintenance_mode' => false,
        ];
        
        // Fusionner les paramètres existants avec les valeurs par défaut
        return array_merge($defaults, $settings);
    }
}
