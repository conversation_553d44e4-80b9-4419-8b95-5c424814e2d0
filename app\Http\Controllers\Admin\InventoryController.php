<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InventoryController extends Controller
{
    public function index() { return view('admin.inventories.index'); }
    public function create() { return view('admin.inventories.create'); }
    public function store(Request $request) { return redirect()->route('admin.inventories.index'); }
    public function show($id) { return view('admin.inventories.show', compact('id')); }
    public function edit($id) { return view('admin.inventories.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.inventories.index'); }
    public function destroy($id) { return redirect()->route('admin.inventories.index'); }
    public function pending() { return view('admin.inventories.pending'); }
}
