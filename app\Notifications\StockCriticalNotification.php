<?php

namespace App\Notifications;

use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StockCriticalNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $product;

    public function __construct(Product $product)
    {
        $this->product = $product;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('ALERTE - Stock Critique')
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('⚠️ Le stock d\'un produit est tombé en dessous du seuil critique.')
            ->line('Détails du produit :')
            ->line("Nom: {$this->product->name}")
            ->line("Stock actuel: {$this->product->current_stock}")
            ->line("Stock minimum: {$this->product->min_stock}")
            ->line("Boutique: {$this->product->shop->name}")
            ->action('Gérer le stock', url('/products/' . $this->product->id . '/stock'))
            ->line('Veuillez prendre les mesures nécessaires pour réapprovisionner le stock.');
    }

    public function toArray($notifiable)
    {
        return [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'current_stock' => $this->product->current_stock,
            'min_stock' => $this->product->min_stock,
            'shop_name' => $this->product->shop->name,
        ];
    }
}
