<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserLoginNotification extends Notification
{
    use Queueable;

    public $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Nouvelle connexion utilisateur')
            ->line("L'utilisateur {$this->user->name} vient de se connecter.")
            ->line("Email: {$this->user->email}")
            ->line("Boutique: {$this->user->shop->name}")
            ->line('Merci de votre attention !');
    }
}
