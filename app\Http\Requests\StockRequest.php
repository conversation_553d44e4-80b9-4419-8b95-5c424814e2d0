<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StockRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|exists:products,id',
            'shop_id' => 'required|exists:shops,id',
            'quantity' => 'required|integer|min:1',
            'type' => 'required|in:supply,sale',
            'status' => 'required|in:pending,completed,cancelled',
            'notes' => 'nullable|string|max:1000'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Le produit est obligatoire',
            'product_id.exists' => 'Le produit sélectionné n\'existe pas',
            'shop_id.required' => 'La boutique est obligatoire',
            'shop_id.exists' => 'La boutique sélectionnée n\'existe pas',
            'quantity.required' => 'La quantité est obligatoire',
            'quantity.integer' => 'La quantité doit être un nombre entier',
            'quantity.min' => 'La quantité doit être supérieure à 0',
            'type.required' => 'Le type de mouvement est obligatoire',
            'type.in' => 'Le type de mouvement doit être soit un approvisionnement soit une vente',
            'status.required' => 'Le statut est obligatoire',
            'status.in' => 'Le statut doit être en attente, complété ou annulé',
            'notes.max' => 'Les notes ne peuvent pas dépasser 1000 caractères'
        ];
    }
}
