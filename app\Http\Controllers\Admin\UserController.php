<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::with('shop', 'roles');

        // Filtrage
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('shop')) {
            $query->where('shop_id', $request->shop);
        }

        // Tri
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $users = $query->paginate(20)->withQueryString();
        $roles = Role::all();
        $shops = Shop::all();

        if ($request->ajax()) {
            return response()->json([
                'html' => view('admin.users._table', compact('users'))->render(),
                'pagination' => view('components.pagination', ['paginator' => $users])->render()
            ]);
        }

        return view('admin.users.index', compact('users', 'roles', 'shops'));
    }

    public function create()
    {
        $shops = Shop::all();
        $roles = Role::whereIn('name', ['admin', 'manager', 'user'])->get(); 
        return view('admin.users.create', compact('shops', 'roles'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'shop_id' => 'required|exists:shops,id',
            'role' => 'required|in:admin,manager,user',
            'status' => 'required|in:active,inactive'
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'shop_id' => $request->shop_id,
            'status' => $request->status,
            'role' => $request->role
        ]);

        $user->assignRole($request->role);

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur créé avec succès.');
    }

    public function show(User $user)
    {
        $user->load('shop', 'roles');
        return view('admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        $shops = Shop::all();
        $roles = Role::whereIn('name', ['admin', 'manager', 'user'])->get(); 
        return view('admin.users.edit', compact('user', 'shops', 'roles'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'shop_id' => 'required|exists:shops,id',
            'role' => 'required|in:admin,manager,user',
            'status' => 'required|in:active,inactive',
            'password' => 'nullable|string|min:8|confirmed'
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'shop_id' => $request->shop_id,
            'status' => $request->status,
            'role' => $request->role
        ];

        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        $user->update($userData);

        // Mettre à jour le rôle
        $user->syncRoles([$request->role]);

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur mis à jour avec succès.');
    }

    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Vous ne pouvez pas supprimer votre propre compte.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur supprimé avec succès.');
    }

    /**
     * Toggle user status between active and inactive
     */
    public function toggleStatus(User $user)
    {
        try {
            // Prevent self-deactivation
            if ($user->id === auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vous ne pouvez pas modifier votre propre statut.'
                ], 403);
            }

            DB::beginTransaction();
            
            $newStatus = $user->status === 'active' ? 'inactive' : 'active';
            $user->status = $newStatus;
            $user->save();

            // Log the activity
            activity()
                ->performedOn($user)
                ->causedBy(auth()->user())
                ->withProperties([
                    'old_status' => $newStatus === 'active' ? 'inactive' : 'active',
                    'new_status' => $newStatus
                ])
                ->log('user_status_changed');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Le statut de l'utilisateur a été mis à jour avec succès.",
                'status' => $newStatus
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Une erreur est survenue lors de la mise à jour du statut."
            ], 500);
        }
    }

    /**
     * Handle bulk actions on users
     */
    public function bulkAction(Request $request)
    {
        try {
            $action = $request->input('action');
            $userIds = $request->input('user_ids', []);

            if (empty($userIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun utilisateur sélectionné.'
                ], 400);
            }

            // Prevent self-modification
            if (in_array(auth()->id(), $userIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vous ne pouvez pas modifier votre propre compte.'
                ], 403);
            }

            DB::beginTransaction();

            $users = User::whereIn('id', $userIds)->get();
            $count = 0;

            foreach ($users as $user) {
                switch ($action) {
                    case 'activate':
                        if ($user->status !== 'active') {
                            $user->status = 'active';
                            $user->save();
                            $count++;
                            
                            activity()
                                ->performedOn($user)
                                ->causedBy(auth()->user())
                                ->withProperties(['new_status' => 'active'])
                                ->log('user_activated');
                        }
                        break;

                    case 'deactivate':
                        if ($user->status !== 'inactive') {
                            $user->status = 'inactive';
                            $user->save();
                            $count++;
                            
                            activity()
                                ->performedOn($user)
                                ->causedBy(auth()->user())
                                ->withProperties(['new_status' => 'inactive'])
                                ->log('user_deactivated');
                        }
                        break;

                    case 'delete':
                        $user->delete();
                        $count++;
                        
                        activity()
                            ->performedOn($user)
                            ->causedBy(auth()->user())
                            ->log('user_deleted');
                        break;
                }
            }

            DB::commit();

            $actionMessages = [
                'activate' => 'activé(s)',
                'deactivate' => 'désactivé(s)',
                'delete' => 'supprimé(s)'
            ];

            return response()->json([
                'success' => true,
                'message' => "{$count} utilisateur(s) {$actionMessages[$action]} avec succès."
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Une erreur est survenue lors de l'opération."
            ], 500);
        }
    }

    public function lastLogin(User $user)
    {
        return response()->json([
            'last_login' => $user->last_login ? Carbon::parse($user->last_login)->format('d/m/Y H:i:s') : 'Jamais'
        ]);
    }
}
