<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tax;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Tax::updateOrCreate([
            'name' => 'TVA 20%',
        ], [
            'rate' => 20.00,
            'type' => 'TVA',
            'description' => 'Taxe sur la valeur ajoutée standard en France.'
        ]);

        Tax::updateOrCreate([
            'name' => 'TVA 5.5%',
        ], [
            'rate' => 5.50,
            'type' => 'TVA',
            'description' => 'TVA taux réduit (produits alimentaires, livres, etc.).'
        ]);

        Tax::updateOrCreate([
            'name' => 'Impôt sur les sociétés',
        ], [
            'rate' => 25.00,
            'type' => 'impot',
            'description' => 'Taux standard d’impôt sur les sociétés.'
        ]);
    }
}
