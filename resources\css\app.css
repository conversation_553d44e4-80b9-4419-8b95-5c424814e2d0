@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 221.2 83.2% 53.3%;
        --radius: 0.5rem;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 217.2 91.2% 59.8%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 224.3 76.3% 48%;
    }
}

@layer base {
    * {
        /* @apply border-border; */
        /* @apply border; */
    }
    body {
        /* @apply bg-background text-foreground; */
        @apply bg-white text-black;
    }
}

.btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring;
    /* focus-visible:outline-none, focus:ring-ring et disabled:pointer-events-none retirés car non supportés */
}

.btn-primary {
    @apply bg-blue-500 text-white shadow hover:bg-blue-600;
}

.btn-destructive {
    @apply bg-red-600 text-white shadow-sm hover:bg-red-700;
}

.btn-outline {
    /* @apply border border-gray-300 bg-white shadow-sm hover:bg-gray-100 hover:text-gray-900; */
    @apply bg-white shadow-sm hover:bg-gray-100 hover:text-gray-900;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.btn-ghost {
    @apply hover:bg-gray-100 hover:text-gray-900;
}

.btn-link {
    @apply text-blue-600 hover:underline;
}

.input {
    /* @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2; */
    @apply flex h-10 w-full rounded-md bg-white px-3 py-2 text-sm ring-offset-white focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.badge {
    /* @apply inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2; */
    @apply inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.badge-secondary {
    /* @apply border-transparent bg-gray-200 text-gray-800 hover:bg-gray-300; */
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.badge-destructive {
    /* @apply border-transparent btn-destructive text-white; */
    @apply btn-destructive text-white;
}

.badge-outline {
    @apply text-gray-900;
}

.card {
    /* @apply rounded-lg border bg-white text-gray-900 shadow-sm; */
    @apply rounded-lg bg-white text-gray-900 shadow-sm;
}

.card-header {
    @apply flex flex-col space-y-1.5 p-6;
}

.card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
}

.card-description {
    @apply text-sm text-gray-500;
}

.card-content {
    @apply p-6 pt-0;
}

.card-footer {
    @apply flex items-center p-6 pt-0;
}

/* Bordures toujours visibles pour les champs de saisie du formulaire admin users */
input[type="text"],
input[type="email"],
input[type="password"],
select,
textarea {
    border-width: 1px !important;
    border-style: solid !important;
    border-color: #d1d5db !important; /* gris clair Tailwind: border-gray-300 */
    box-shadow: none !important;
}