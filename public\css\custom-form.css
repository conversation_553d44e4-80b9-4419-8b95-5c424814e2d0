.form-input,
.form-select,
.form-textarea {
    border-width: 2px !important;
    border-color: #ec4899 !important; /* pink-500 */
    border-radius: 0.5rem !important;
    background-color: #fff !important;
    color: #1e293b !important;
}
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: #ec4899 !important;
    box-shadow: 0 0 0 2px #fbcfe8 !important;
}
select.form-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23ec4899" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M7.293 7.293a1 1 0 011.414 0L10 8.586l1.293-1.293a1 1 0 111.414 1.414l-2 2a1 1 0 01-1.414 0l-2-2a1 1 0 010-1.414z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1.25em 1.25em;
    padding-right: 2.5rem;
}
