<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Shop;
use App\Models\Stock;
use App\Models\StockMovement;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;
use App\Notifications\LowStockAlert;

class StockManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $product;
    protected $shop;
    protected $stock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create()->assignRole('admin');
        $category = Category::factory()->create();
        $this->product = Product::factory()->create(['category_id' => $category->id]);
        $this->shop = Shop::factory()->create();
        $this->stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'quantity' => 100,
            'min_stock' => 20
        ]);
    }

    /** @test */
    public function admin_can_view_stock_movements()
    {
        $movement = StockMovement::factory()->create([
            'stock_id' => $this->stock->id,
            'user_id' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('stock.movements.index'));

        $response->assertStatus(200)
            ->assertViewIs('stock.movements.index')
            ->assertViewHas('movements')
            ->assertSee($this->product->name);
    }

    /** @test */
    public function admin_can_add_stock()
    {
        $data = [
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'type' => 'in',
            'quantity' => 50,
            'description' => 'Test stock addition'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('stock.movements.store'), $data);

        $response->assertRedirect(route('stock.movements.index'));

        $this->assertEquals(150, $this->stock->fresh()->quantity);

        $this->assertDatabaseHas('stock_movements', [
            'stock_id' => $this->stock->id,
            'type' => 'in',
            'quantity' => 50
        ]);
    }

    /** @test */
    public function admin_can_remove_stock()
    {
        $data = [
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'type' => 'out',
            'quantity' => 30,
            'description' => 'Test stock removal'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('stock.movements.store'), $data);

        $response->assertRedirect(route('stock.movements.index'));

        $this->assertEquals(70, $this->stock->fresh()->quantity);

        $this->assertDatabaseHas('stock_movements', [
            'stock_id' => $this->stock->id,
            'type' => 'out',
            'quantity' => -30
        ]);
    }

    /** @test */
    public function cannot_remove_more_stock_than_available()
    {
        $data = [
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'type' => 'out',
            'quantity' => 150,
            'description' => 'Test excessive stock removal'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('stock.movements.store'), $data);

        $response->assertSessionHasErrors();
        $this->assertEquals(100, $this->stock->fresh()->quantity);
    }

    /** @test */
    public function admin_can_transfer_stock_between_shops()
    {
        $destinationShop = Shop::factory()->create();
        
        $data = [
            'product_id' => $this->product->id,
            'from_shop_id' => $this->shop->id,
            'to_shop_id' => $destinationShop->id,
            'quantity' => 30,
            'description' => 'Test stock transfer'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('stock.movements.transfer'), $data);

        $response->assertRedirect(route('stock.movements.index'));

        $this->assertEquals(70, $this->stock->fresh()->quantity);

        $destinationStock = Stock::where('product_id', $this->product->id)
            ->where('shop_id', $destinationShop->id)
            ->first();

        $this->assertEquals(30, $destinationStock->quantity);
    }

    /** @test */
    public function low_stock_triggers_notification()
    {
        Notification::fake();

        $data = [
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'type' => 'out',
            'quantity' => 90,
            'description' => 'Test low stock alert'
        ];

        $this->actingAs($this->admin)
            ->post(route('stock.movements.store'), $data);

        $this->assertEquals(10, $this->stock->fresh()->quantity);

        Notification::assertSentTo(
            [$this->admin],
            LowStockAlert::class
        );
    }

    /** @test */
    public function can_view_stock_movement_history()
    {
        StockMovement::factory(3)->create([
            'stock_id' => $this->stock->id,
            'user_id' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('stock.movements.history', $this->stock));

        $response->assertStatus(200)
            ->assertViewIs('stock.movements.history')
            ->assertViewHas(['stock', 'movements']);
    }

    /** @test */
    public function can_filter_stock_movements()
    {
        $movement1 = StockMovement::factory()->create([
            'stock_id' => $this->stock->id,
            'user_id' => $this->admin->id,
            'type' => 'in'
        ]);

        $movement2 = StockMovement::factory()->create([
            'stock_id' => $this->stock->id,
            'user_id' => $this->admin->id,
            'type' => 'out'
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('stock.movements.index', ['type' => 'in']));

        $response->assertSee($movement1->created_at->format('d/m/Y'))
            ->assertDontSee($movement2->created_at->format('d/m/Y'));
    }
}
