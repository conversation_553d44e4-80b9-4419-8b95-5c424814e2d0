<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Stock;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:manager']);
    }

    public function index()
    {
        $shop = Auth::user()->shop;

        // Statistiques de la boutique
        $stats = [
            'daily_sales' => Sale::where('shop_id', $shop->id)
                                ->whereDate('created_at', today())
                                ->sum('total_amount'),
            'monthly_sales' => Sale::where('shop_id', $shop->id)
                                ->whereMonth('created_at', now()->month)
                                ->sum('total_amount'),
            'pending_deliveries' => $shop->deliveries()
                                    ->where('status', 'pending')
                                    ->count(),
            'low_stock_count' => Stock::where('shop_id', $shop->id)
                                    ->whereColumn('quantity', '<=', 'min_stock')
                                    ->count()
        ];

        // Produits en rupture de stock
        $lowStockProducts = Product::with(['stocks' => function($query) use ($shop) {
            $query->where('shop_id', $shop->id);
        }])->whereHas('stocks', function($query) use ($shop) {
            $query->where('shop_id', $shop->id)
                ->whereColumn('quantity', '<=', 'min_stock');
        })->get();

        // Ventes du jour
        $todaySales = Sale::with('customer')
            ->where('shop_id', $shop->id)
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Livraisons en attente
        $pendingDeliveries = $shop->deliveries()
            ->with('sale.customer')
            ->where('status', 'pending')
            ->orderBy('delivery_date')
            ->take(5)
            ->get();

        return view('manager.dashboard', compact(
            'stats',
            'lowStockProducts',
            'todaySales',
            'pendingDeliveries'
        ));
    }
}
