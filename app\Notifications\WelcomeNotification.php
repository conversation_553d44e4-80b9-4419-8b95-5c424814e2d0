<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Bienvenue sur ZIAD FRIGO')
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Nous sommes ravis de vous accueillir sur ZIAD Sarl.')
            ->line('Notre plateforme vous permet de gérer efficacement votre commerce.')
            ->action('Commencer maintenant', url('/dashboard'))
            ->line('Merci de nous faire confiance!');
    }
}
