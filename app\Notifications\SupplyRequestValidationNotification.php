<?php

namespace App\Notifications;

use App\Models\SupplyRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;

class SupplyRequestValidationNotification extends Notification
{
    use Queueable;

    protected $supplyRequest;

    /**
     * Créer une nouvelle instance de notification
     */
    public function __construct(SupplyRequest $supplyRequest)
    {
        $this->supplyRequest = $supplyRequest;
    }

    /**
     * Obtenir les canaux de livraison de la notification
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Obtenir la représentation mail de la notification
     */
    public function toMail($notifiable)
    {
        $url = route('supply-requests.show', $this->supplyRequest->id);

        return (new MailMessage)
            ->subject('Nouvelle Demande d\'Approvisionnement - ' . $this->supplyRequest->reference_number)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Une nouvelle demande d\'approvisionnement a été créée.')
            ->line('Référence : ' . $this->supplyRequest->reference_number)
            ->line('Boutique demandeuse : ' . $this->supplyRequest->requestingShop->name)
            ->line('Date requise : ' . $this->supplyRequest->required_date->format('d/m/Y'))
            ->when($this->supplyRequest->notes, function ($mail) {
                return $mail->line('Notes : ' . $this->supplyRequest->notes);
            })
            ->action('Voir la demande', $url)
            ->line('Veuillez examiner cette demande et la valider si possible.');
    }

    /**
     * Obtenir la représentation array de la notification
     */
    public function toArray($notifiable)
    {
        return [
            'supply_request_id' => $this->supplyRequest->id,
            'reference_number' => $this->supplyRequest->reference_number,
            'requesting_shop' => $this->supplyRequest->requestingShop->name,
            'required_date' => $this->supplyRequest->required_date,
            'notes' => $this->supplyRequest->notes,
            'type' => 'supply_request_validation'
        ];
    }
}
