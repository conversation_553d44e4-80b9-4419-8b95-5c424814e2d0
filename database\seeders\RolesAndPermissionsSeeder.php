<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Créer les permissions
        $permissions = [
            // Permissions des boutiques
            'view shops',
            'create shops',
            'edit shops',
            'delete shops',
            
            // Permissions des utilisateurs
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Permissions des produits
            'view products',
            'create products',
            'edit products',
            'delete products',
            
            // Permissions des stocks
            'view stock',
            'manage stock',
            'view inventory',
            'create inventory',
            
            // Permissions des ventes
            'create sales',
            'view sales',
            'cancel sales',
            'view reports',
            
            // Permissions de la comptabilité
            'view accounting',
            'manage accounting',
            'view expenses',
            'manage expenses'
        ];

        foreach ($permissions as $permission) {
            if (!Permission::where('name', $permission)->where('guard_name', 'web')->exists()) {
                Permission::create(['name' => $permission, 'guard_name' => 'web']);
            }
        }

        // Créer les rôles et assigner les permissions
        $roles = [
            'admin' => $permissions,
            
            'manager' => [
                'view shops', 'view users', 'view products', 'view stock',
                'manage stock', 'view inventory', 'create inventory',
                'view sales', 'view reports', 'view accounting', 'view expenses'
            ],
            
            'cashier' => [
                'view products', 'view stock', 'create sales',
                'view sales', 'cancel sales'
            ],
            
            'storekeeper' => [
                'view products', 'view stock', 'manage stock',
                'view inventory', 'create inventory'
            ],
            
            'accountant' => [
                'view sales', 'view reports', 'view accounting',
                'manage accounting', 'view expenses', 'manage expenses'
            ],
            
            'biller' => [
                'view products', 'create sales', 'view sales',
                'cancel sales'
            ]
        ];

        foreach ($roles as $role => $rolePermissions) {
            $createdRole = \Spatie\Permission\Models\Role::where('name', $role)->where('guard_name', 'web')->first();
            if (!$createdRole) {
                $createdRole = \Spatie\Permission\Models\Role::create(['name' => $role, 'guard_name' => 'web']);
            }
            $createdRole->givePermissionTo($rolePermissions);
        }
    }
}
