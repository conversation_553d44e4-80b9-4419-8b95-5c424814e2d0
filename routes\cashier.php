<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Cashier\DashboardController;
use App\Http\Controllers\Cashier\PaymentController;

Route::middleware(['auth', 'role:cashier'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('cashier.dashboard');
    
    // Gestion de la caisse
    Route::post('/register/open', [DashboardController::class, 'openRegister'])->name('cashier.register.open');
    Route::post('/register/close', [DashboardController::class, 'closeRegister'])->name('cashier.register.close');
    
    // Gestion des paiements
    Route::get('/payments', [PaymentController::class, 'index'])->name('cashier.payments.index');
    Route::get('/payments/create', [PaymentController::class, 'create'])->name('cashier.payments.create');
    Route::post('/payments', [PaymentController::class, 'store'])->name('cashier.payments.store');
    Route::get('/payments/{payment}/receipt', [PaymentController::class, 'printReceipt'])->name('cashier.payments.receipt');
    
    // Rapports de caisse
    Route::get('/reports/daily', [PaymentController::class, 'dailyReport'])->name('cashier.reports.daily');
    Route::get('/reports/register', [PaymentController::class, 'registerReport'])->name('cashier.reports.register');
});
