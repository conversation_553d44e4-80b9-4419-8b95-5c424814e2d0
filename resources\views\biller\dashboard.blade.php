@extends('layouts.biller')

@section('title', 'Tableau de bord facturier')

@section('content')
<div class="container-fluid">
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Factures du jour</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['daily_sales'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Montant du jour</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['daily_amount'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Crédits en attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_credit'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Chèques en attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_checks'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Dernières factures -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Dernières factures</h6>
                    <a href="{{ route('biller.sales.create') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle facture
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>N° Facture</th>
                                    <th>Client</th>
                                    <th>Montant</th>
                                    <th>Mode</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentSales as $sale)
                                <tr>
                                    <td>{{ $sale->invoice_number }}</td>
                                    <td>{{ $sale->customer->name }}</td>
                                    <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                    <td>
                                        <span class="badge badge-{{ $sale->payment_type_color }}">
                                            {{ $sale->payment_type_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $sale->status_color }}">
                                            {{ $sale->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('biller.sales.show', $sale) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('biller.sales.print', $sale) }}" class="btn btn-sm btn-secondary">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center">Aucune facture récente</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <!-- Paiements en attente -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Paiements en attente</h6>
                </div>
                <div class="card-body">
                    @if($pendingPayments->isEmpty())
                        <p class="text-center">Aucun paiement en attente</p>
                    @else
                        <div class="list-group">
                            @foreach($pendingPayments as $payment)
                            <a href="{{ route('biller.sales.show', $payment) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $payment->customer->name }}</h6>
                                    <small class="text-danger">
                                        {{ number_format($payment->total_amount, 0, ',', ' ') }} FCFA
                                    </small>
                                </div>
                                <small>
                                    Facture #{{ $payment->invoice_number }} - 
                                    {{ $payment->created_at->format('d/m/Y') }}
                                </small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Clients récents -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Clients récents</h6>
                </div>
                <div class="card-body">
                    @if($recentCustomers->isEmpty())
                        <p class="text-center">Aucun client récent</p>
                    @else
                        <div class="list-group">
                            @foreach($recentCustomers as $customer)
                            <a href="{{ route('biller.customers.show', $customer) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $customer->name }}</h6>
                                    <small>{{ $customer->sales_count }} ventes</small>
                                </div>
                                <small>
                                    Total : {{ number_format($customer->sales_sum_total_amount, 0, ',', ' ') }} FCFA
                                </small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
