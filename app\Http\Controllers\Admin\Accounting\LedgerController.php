<?php

namespace App\Http\Controllers\Admin\Accounting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Account;
use App\Models\JournalEntryLine;
use Illuminate\Support\Facades\Auth;

class LedgerController extends Controller
{
    // Grand Livre
    public function generalLedger(Request $request)
    {
        $accounts = Account::orderBy('code')->get();
        $accountId = $request->input('account_id');
        $from = $request->input('from');
        $to = $request->input('to');
        $lines = collect();
        $account = null;
        if ($accountId) {
            $account = Account::find($accountId);
            $query = $account->journalEntryLines();
            if ($from) $query->whereDate('created_at', '>=', $from);
            if ($to) $query->whereDate('created_at', '<=', $to);
            $lines = $query->orderBy('created_at')->get();
        }
        return view('admin.accounting.ledger', compact('accounts', 'lines', 'account', 'from', 'to'));
    }

    // Balance
    public function balance(Request $request)
    {
        $from = $request->input('from');
        $to = $request->input('to');
        $accounts = Account::orderBy('code')->get();
        foreach ($accounts as $account) {
            $account->total_debit = $account->totalDebit($from, $to);
            $account->total_credit = $account->totalCredit($from, $to);
            $account->balance = $account->balance($from, $to);
        }
        return view('admin.accounting.balance', compact('accounts', 'from', 'to'));
    }
}
