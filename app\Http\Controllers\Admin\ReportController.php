<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function index() { return view('admin.reports.index'); }
    public function create() { return view('admin.reports.create'); }
    public function store(Request $request) { return redirect()->route('admin.reports.index'); }
    public function show($id) { return view('admin.reports.show', compact('id')); }
    public function edit($id) { return view('admin.reports.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.reports.index'); }
    public function destroy($id) { return redirect()->route('admin.reports.index'); }
}
