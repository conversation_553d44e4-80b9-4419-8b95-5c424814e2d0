<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supply extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'store_id',
        'supplier_id',
        'validated_by',
        'reference_number',
        'status',
        'notes',
        'total_amount',
        'validated_at',
        'delivered_at'
    ];

    protected $casts = [
        'validated_at' => 'datetime',
        'delivered_at' => 'datetime'
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    public function validator()
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    public function items()
    {
        return $this->hasMany(SupplyItem::class);
    }
}
