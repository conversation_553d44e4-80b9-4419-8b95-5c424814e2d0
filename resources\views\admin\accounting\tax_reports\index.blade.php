@extends('layouts.admin')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>Rapports Fiscaux</h2>
        <a href="{{ route('admin.accounting.tax_reports.create') }}" class="btn btn-primary">Nouveau rapport</a>
    </div>
    <table class="table table-bordered table-hover">
        <thead class="thead-dark">
            <tr>
                <th>Période</th>
                <th>TVA collectée (€)</th>
                <th>TVA déductible (€)</th>
                <th>Total impôts (€)</th>
                <th>État</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($taxReports as $report)
                <tr>
                    <td>{{ $report->period }}</td>
                    <td>{{ number_format($report->total_tva_collected, 2) }}</td>
                    <td>{{ number_format($report->total_tva_deductible, 2) }}</td>
                    <td>{{ number_format($report->total_taxes, 2) }}</td>
                    <td>{{ ucfirst($report->status) }}</td>
                    <td>
                        <a href="{{ route('admin.accounting.tax_reports.show', $report) }}" class="btn btn-sm btn-info">Voir</a>
                        <a href="{{ route('admin.accounting.tax_reports.edit', $report) }}" class="btn btn-sm btn-warning">Éditer</a>
                        <form action="{{ route('admin.accounting.tax_reports.destroy', $report) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Supprimer ce rapport ?')">Supprimer</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">Aucun rapport fiscal enregistré.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
@endsection
