<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ProductImageController extends Controller
{
    public function index(Product $product)
    {
        $images = $product->images()->orderBy('is_primary', 'desc')->get();
        return view('products.images.index', compact('product', 'images'));
    }

    public function store(Request $request, Product $product)
    {
        $request->validate([
            'images.*' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'images' => 'max:5' // Maximum 5 images à la fois
        ], [
            'images.*.image' => 'Le fichier doit être une image.',
            'images.*.mimes' => 'L\'image doit être au format JPEG, PNG ou JPG.',
            'images.*.max' => 'L\'image ne doit pas dépasser 2Mo.',
            'images.max' => 'Vous ne pouvez pas uploader plus de 5 images à la fois.'
        ]);

        try {
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $imageFile) {
                    // Créer une version originale
                    $originalPath = $imageFile->store('products/original', 'public');
                    
                    // Créer une version thumbnail
                    $thumbnail = Image::make($imageFile)
                        ->fit(300, 300, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                    $thumbnailPath = 'products/thumbnails/' . basename($originalPath);
                    Storage::disk('public')->put($thumbnailPath, (string) $thumbnail->encode());

                    // Créer une version moyenne pour l'affichage
                    $medium = Image::make($imageFile)
                        ->fit(800, 800, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                    $mediumPath = 'products/medium/' . basename($originalPath);
                    Storage::disk('public')->put($mediumPath, (string) $medium->encode());

                    // Sauvegarder l'image en base de données
                    $product->images()->create([
                        'original_path' => $originalPath,
                        'thumbnail_path' => $thumbnailPath,
                        'medium_path' => $mediumPath,
                        'is_primary' => !$product->images()->exists()
                    ]);
                }
            }

            return redirect()
                ->route('products.images.index', $product)
                ->with('success', 'Images ajoutées avec succès.');

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de l\'upload des images.');
        }
    }

    public function destroy(Product $product, ProductImage $image)
    {
        try {
            // Supprimer les fichiers
            Storage::disk('public')->delete([
                $image->original_path,
                $image->thumbnail_path,
                $image->medium_path
            ]);

            // Si c'était l'image principale, définir une autre image comme principale
            if ($image->is_primary) {
                $nextImage = $product->images()
                    ->where('id', '!=', $image->id)
                    ->first();
                
                if ($nextImage) {
                    $nextImage->update(['is_primary' => true]);
                }
            }

            $image->delete();

            return redirect()
                ->route('products.images.index', $product)
                ->with('success', 'Image supprimée avec succès.');

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de la suppression de l\'image.');
        }
    }

    public function setPrimary(Product $product, ProductImage $image)
    {
        try {
            // Retirer le statut principal de toutes les autres images
            $product->images()->update(['is_primary' => false]);
            
            // Définir cette image comme principale
            $image->update(['is_primary' => true]);

            return redirect()
                ->route('products.images.index', $product)
                ->with('success', 'Image principale définie avec succès.');

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de la modification de l\'image principale.');
        }
    }

    public function reorder(Request $request, Product $product)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'exists:product_images,id'
        ]);

        try {
            foreach ($request->images as $index => $id) {
                ProductImage::where('id', $id)->update(['order' => $index]);
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            return response()->json(['success' => false], 500);
        }
    }
}
