@extends('layouts.admin')

@section('content')
<div class="max-w-4xl mx-auto py-10">
    <div class="bg-gradient-to-br from-pink-500 via-blue-500 to-purple-500 rounded-xl shadow-xl p-1">
        <div class="bg-white rounded-xl shadow p-8 flex flex-col md:flex-row gap-8">
            <div class="flex flex-col items-center md:items-start md:w-1/3">
                <div class="relative">
                    <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="h-48 w-48 rounded-xl object-cover border-4 border-blue-200 shadow-lg">
                    @if(!$product->is_active)
                        <span class="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">Inactif</span>
                    @endif
                </div>
                <div class="mt-4 text-center md:text-left">
                    <span class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-semibold">SKU : {{ $product->sku }}</span>
                </div>
            </div>
            <div class="flex-1 flex flex-col gap-3 justify-between">
                <div>
                    <h2 class="text-3xl font-extrabold text-gray-900 mb-2 flex items-center gap-2">
                        {{ $product->name }}
                        @if($product->category)
                            <span class="ml-2 px-2 py-1 bg-pink-100 text-pink-700 rounded text-xs font-medium">{{ $product->category->name }}</span>
                        @endif
                    </h2>
                    <div class="flex flex-wrap gap-4 text-lg text-gray-700 mb-2">
                        <div class="flex items-center gap-1"><i class="fas fa-money-bill-wave text-green-500"></i> <b>{{ number_format($product->price, 2, ',', ' ') }} FCFA</b></div>
                        <div class="flex items-center gap-1"><i class="fas fa-boxes text-blue-500"></i> Stock : <b>{{ optional($product->stockDetails)->sum('quantity') }}</b></div>
                        <div class="flex items-center gap-1"><i class="fas fa-exclamation-triangle text-yellow-500"></i> Min : <b>{{ $product->min_stock }}</b></div>
                        <div class="flex items-center gap-1"><i class="fas fa-ruler-combined text-purple-500"></i> Unité : <b>{{ $product->unit }}</b></div>
                    </div>
                    <div class="flex items-center gap-2 mb-2">
                        <span class="px-2 py-1 rounded {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }} text-xs font-semibold">
                            {{ $product->is_active ? 'Actif' : 'Inactif' }}
                        </span>
                        <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">Créé le {{ $product->created_at->format('d/m/Y à H:i') }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="text-md font-semibold text-gray-800 mb-1">Description</h4>
                    <div class="bg-gray-50 border border-gray-200 rounded p-4 text-gray-700 min-h-[60px]">
                        {{ $product->description ?? 'Aucune description' }}
                    </div>
                </div>
                <div class="mt-6 flex flex-wrap gap-2">
                    <a href="{{ route('admin.products.edit', $product) }}" class="px-4 py-2 bg-pink-600 text-white rounded shadow hover:bg-pink-700 transition"><i class="fas fa-edit mr-1"></i>Modifier</a>
                    <a href="{{ route('admin.products.index') }}" class="px-4 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700 transition"><i class="fas fa-arrow-left mr-1"></i>Retour à la liste</a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
