@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>Détails du Stock</h2>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tr>
                                <th>Produit:</th>
                                <td>{{ $inventory->product->name }}</td>
                            </tr>
                            <tr>
                                <th>Magasin:</th>
                                <td>{{ $inventory->shop->name }}</td>
                            </tr>
                            <tr>
                                <th>Quantité:</th>
                                <td>{{ $inventory->quantity }} {{ $inventory->unit }}</td>
                            </tr>
                            <tr>
                                <th>Quantité Minimale:</th>
                                <td>{{ $inventory->min_quantity }}</td>
                            </tr>
                            <tr>
                                <th>Quantité Maximale:</th>
                                <td>{{ $inventory->max_quantity ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Point de Réapprovisionnement:</th>
                                <td>{{ $inventory->reorder_point }}</td>
                            </tr>
                            <tr>
                                <th>Emplacement:</th>
                                <td>{{ $inventory->location ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Dernière mise à jour:</th>
                                <td>{{ $inventory->updated_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <th>Notes:</th>
                                <td>{{ $inventory->notes ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="mt-4 d-flex justify-content-between">
                        <a href="{{ route('inventory.index') }}" class="btn btn-secondary">Retour</a>
                        <div>
                            <a href="{{ route('inventory.edit', $inventory) }}" class="btn btn-warning">Modifier</a>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                Supprimer
                            </button>
                        </div>
                    </div>

                    <!-- Modal de suppression -->
                    <div class="modal fade" id="deleteModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Confirmer la suppression</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    Êtes-vous sûr de vouloir supprimer ce stock ?
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                    <form action="{{ route('inventory.destroy', $inventory) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">Supprimer</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
