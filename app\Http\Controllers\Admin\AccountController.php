<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Account;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Recherche, filtre, pagination
        $query = Account::with('parent', 'children');
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%$search%")
                  ->orWhere('name', 'like', "%$search%")
                  ->orWhere('description', 'like', "%$search%")
                  ->orWhere('type', 'like', "%$search%") ;
            });
        }
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }
        $accounts = $query->orderBy('code')->get();

        // Construction de l'arbre (récursif)
        $tree = $this->buildAccountTree($accounts);
        $types = Account::select('type')->distinct()->pluck('type');
        return view('admin.accounting.accounts.index', compact('tree', 'accounts', 'types'));
    }

    /**
     * Construit un arbre imbriqué de comptes à partir d'une collection.
     */
    protected function buildAccountTree($accounts, $parentId = null)
    {
        $branch = [];
        foreach ($accounts as $account) {
            if ($account->parent_id == $parentId) {
                $children = $this->buildAccountTree($accounts, $account->id);
                if ($children) {
                    $account->children_tree = $children;
                }
                $branch[] = $account;
            }
        }
        return $branch;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accounts = Account::orderBy('code')->get();
        return view('admin.accounting.accounts.create', compact('accounts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string|max:20|unique:accounts,code',
            'name' => 'required|string|max:100',
            'type' => 'required|string',
            'parent_id' => 'nullable|exists:accounts,id',
            'description' => 'nullable|string|max:255',
        ]);
        Account::create($validated);
        return redirect()->route('admin.accounting.accounts.index')->with('success', 'Compte créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $account = Account::findOrFail($id);
        $parents = Account::where('id', '!=', $id)->get();
        $types = ['actif', 'passif', 'charge', 'produit', 'capitaux propres', 'autre'];
        return view('admin.accounting.accounts.edit', compact('account', 'parents', 'types'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $account = Account::findOrFail($id);
        $validated = $request->validate([
            'code' => 'required|string|max:20|unique:accounts,code,' . $account->id,
            'name' => 'required|string|max:100',
            'type' => 'required|string',
            'parent_id' => 'nullable|exists:accounts,id',
            'description' => 'nullable|string|max:255',
        ]);
        $account->update($validated);
        return redirect()->route('admin.accounting.accounts.index')->with('success', 'Compte mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $account = Account::findOrFail($id);
        $account->delete();
        return redirect()->route('admin.accounting.accounts.index')->with('success', 'Compte supprimé avec succès.');
    }
}
