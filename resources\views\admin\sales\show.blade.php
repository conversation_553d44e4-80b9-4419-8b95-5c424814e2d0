@extends('layouts.admin')

@section('content')
<div class="container mx-auto py-8">
    <div class="bg-white rounded-xl shadow-lg p-8 relative">
        <h1 class="text-3xl font-bold mb-6 flex items-center gap-2">
            <i class="fas fa-receipt text-blue-600"></i>
            Détail de la vente <span class="text-gray-400">#{{ $sale->id }}</span>
        </h1>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="space-y-2">
                <div class="flex items-center gap-2">
                    <i class="fas fa-store text-gray-500"></i>
                    <span class="font-semibold">Boutique :</span> {{ $sale->shop->name ?? '-' }}
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-user text-gray-500"></i>
                    <span class="font-semibold">Client :</span> {{ $sale->customer?->name ?? '-' }}
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-envelope text-gray-500"></i>
                    <span class="font-semibold">Email :</span> {{ $sale->customer?->email ?? '-' }}
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-phone text-gray-500"></i>
                    <span class="font-semibold">Téléphone :</span> {{ $sale->customer?->phone ?? '-' }}
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-calendar-alt text-gray-500"></i>
                    <span class="font-semibold">Date :</span> {{ $sale->created_at->format('d/m/Y H:i') }}
                </div>
            </div>
            <div class="space-y-2">
                <div class="flex items-center gap-2">
                    <i class="fas fa-credit-card text-gray-500"></i>
                    <span class="font-semibold">Méthode de paiement :</span>
                    <span class="inline-block px-2 py-1 rounded bg-blue-100 text-blue-800 text-xs font-semibold">
                        @if($sale->payment_method === 'cash')
                            Espèces
                        @elseif($sale->payment_method === 'card')
                            Carte
                        @elseif($sale->payment_method === 'mobile')
                            Mobile Money
                        @else
                            {{ ucfirst($sale->payment_method) }}
                        @endif
                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-info-circle text-gray-500"></i>
                    <span class="font-semibold">Statut :</span>
                    <span class="inline-block px-2 py-1 rounded bg-green-100 text-green-800 text-xs font-semibold">
                        @if($sale->payment_status === 'paid')
                            Payé
                        @elseif($sale->payment_status === 'pending')
                            En attente
                        @elseif($sale->payment_status === 'cancelled')
                            Annulée
                        @else
                            {{ ucfirst($sale->payment_status) }}
                        @endif
                    </span>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-gray-500 text-sm">Remise globale</div>
                <div class="text-xl font-bold text-blue-700">{{ number_format($sale->discount ?? 0, 2, ',', ' ') }} FCFA</div>
            </div>
            <div class="bg-yellow-50 rounded-lg p-4 text-center">
                <div class="text-gray-500 text-sm">TVA</div>
                <div class="text-xl font-bold text-yellow-700">{{ number_format($sale->tva ?? 0, 2, ',', ' ') }} %</div>
            </div>
            @php
                $total_brut = 0;
                foreach($sale->items as $item) {
                    $total_brut += $item->total_price;
                }
                $base = $total_brut - ($sale->discount ?? 0);
                $montant_tva = $sale->tva ? $base * $sale->tva / 100 : 0;
            @endphp
            <div class="bg-orange-50 rounded-lg p-4 text-center">
                <div class="text-gray-500 text-sm">Montant TVA</div>
                <div class="text-xl font-bold text-orange-700">{{ number_format($montant_tva, 2, ',', ' ') }} FCFA</div>
            </div>
            <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-gray-500 text-sm">Total TTC</div>
                <div class="text-2xl font-extrabold text-green-700">{{ number_format($sale->total_amount, 2, ',', ' ') }} FCFA</div>
            </div>
        </div>

        <h2 class="text-xl font-semibold mb-3 flex items-center gap-2">
            <i class="fas fa-boxes text-gray-500"></i>
            Produits vendus
        </h2>
        <div class="overflow-x-auto rounded-lg shadow">
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-blue-100 text-blue-900">
                    <tr>
                        <th class="py-2 px-3 text-left">Produit</th>
                        <th class="py-2 px-3 text-center">Qté</th>
                        <th class="py-2 px-3 text-right">Prix unitaire</th>
                        <th class="py-2 px-3 text-right">Total</th>
                        <th class="py-2 px-3 text-left">Notes</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($sale->items as $item)
                    <tr class="hover:bg-gray-50">
                        <td class="py-2 px-3 text-left">{{ $item->product->name ?? '-' }}</td>
                        <td class="py-2 px-3 text-center">{{ $item->quantity }}</td>
                        <td class="py-2 px-3 text-right">{{ number_format($item->unit_price, 2, ',', ' ') }} FCFA</td>
                        <td class="py-2 px-3 text-right font-bold">{{ number_format($item->total_price, 2, ',', ' ') }} FCFA</td>
                        <td class="py-2 px-3 text-left">{{ $item->notes }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="mt-8 flex justify-end">
            <a href="{{ route('admin.sales.index') }}" class="inline-flex items-center gap-2 bg-blue-600 text-white px-5 py-2 rounded-lg shadow hover:bg-blue-700 transition">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>
@endsection
