<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Store extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'manager_id',
        'is_active',
        'opening_hours',
        'description'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'opening_hours' => 'array'
    ];

    /**
     * Get the manager of the store.
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the sales for the store.
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the inventory records for the store.
     */
    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * Get the supplies for the store.
     */
    public function supplies()
    {
        return $this->hasMany(Supply::class);
    }

    /**
     * Get the products in stock for this store.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the staff members assigned to this store.
     */
    public function staff()
    {
        return $this->belongsToMany(User::class, 'store_user')
            ->withPivot('role')
            ->withTimestamps();
    }
}
