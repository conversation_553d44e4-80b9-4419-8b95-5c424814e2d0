<?php

namespace App\Notifications;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class InventoryNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $inventory;

    public function __construct(Stock $inventory)
    {
        $this->inventory = $inventory;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Inventaire Complété - ' . $this->inventory->shop->name)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Un inventaire vient d\'être complété dans votre boutique.')
            ->line('Détails de l\'inventaire :')
            ->line("Boutique: {$this->inventory->shop->name}")
            ->line("Date: {$this->inventory->completed_at}")
            ->line("Statut: {$this->inventory->status}")
            ->action('Voir les détails', url('/inventory/' . $this->inventory->id))
            ->line('Merci de votre attention !');
    }

    public function toArray($notifiable)
    {
        return [
            'inventory_id' => $this->inventory->id,
            'shop_name' => $this->inventory->shop->name,
            'status' => $this->inventory->status,
            'completed_at' => $this->inventory->completed_at,
        ];
    }
}
