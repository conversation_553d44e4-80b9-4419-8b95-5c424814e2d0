<?php

namespace Database\Seeders;

use App\Models\Shop;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ShopSeeder extends Seeder
{
    public function run()
    {
        $shops = [
            [
                'name' => 'ZIAD Sarl Siège',
                'slug' => 'ziad-sarl-siege',
                'address' => 'Lomé-Totsi en face de la station Total        ',
                'city' => 'Lomé',
                'postal_code' => '75000',
                'country' => 'Togo',
                'phone' => '01 23 45 67 89',
                'email' => '<EMAIL>',
                'description' => 'Siège social ZIAD Sarl',
                'is_active' => true
            ],
        ];

        foreach ($shops as $shop) {
            $slug = $shop['slug'];
            if (!Shop::where('slug', $slug)->exists()) {
                Shop::create($shop);
            }
        }
    }
}
