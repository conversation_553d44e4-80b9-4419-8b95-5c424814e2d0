<li>
    <div class="account-node">
        @if($account->type === 'actif')
            <i class="fas fa-piggy-bank text-blue-500"></i>
        @elseif($account->type === 'passif')
            <i class="fas fa-balance-scale text-yellow-500"></i>
        @elseif($account->type === 'charge')
            <i class="fas fa-arrow-down text-red-500"></i>
        @elseif($account->type === 'produit')
            <i class="fas fa-arrow-up text-green-500"></i>
        @else
            <i class="fas fa-archive text-gray-400"></i>
        @endif
        <span class="font-mono text-gray-500">{{ $account->code }}</span>
        <span class="font-bold text-gray-800 ml-2">{{ $account->name }}</span>
        <span class="account-type {{ $account->type }} ml-2">{{ ucfirst($account->type) }}</span>
        @if(!empty($account->description))
            <span class="text-xs text-gray-400 ml-2">{{ $account->description }}</span>
        @endif
        <div class="account-actions ml-auto flex gap-2">
            <a href="{{ route('admin.accounting.accounts.edit', $account) }}" class="inline-flex items-center px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition text-xs font-semibold">
                <i class="fas fa-edit mr-1"></i> Modifier
            </a>
            <a href="{{ route('admin.accounting.accounts.show', $account) }}" class="inline-flex items-center px-2 py-1 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 transition text-xs font-semibold">
                <i class="fas fa-eye mr-1"></i> Voir
            </a>
            <form action="{{ route('admin.accounting.accounts.destroy', $account) }}" method="POST" class="inline-block" onsubmit="return confirm('Supprimer ce compte ?');">
                @csrf
                @method('DELETE')
                <button class="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-700 text-xs flex items-center"><i class="fas fa-trash mr-1"></i>Supprimer</button>
            </form>
        </div>
    </div>
    @if(isset($account->children_tree) && count($account->children_tree))
        <ul class="account-tree">
            @foreach($account->children_tree as $child)
                @include('admin.accounting.accounts.partials.account_tree_node', ['account' => $child, 'level' => ($level ?? 0) + 1])
            @endforeach
        </ul>
    @endif
</li>
