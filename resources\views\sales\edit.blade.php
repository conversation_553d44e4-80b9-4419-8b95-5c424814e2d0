@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>Modifier la Vente #{{ $sale->id }}</h2>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('sales.update', $sale) }}">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="shop_id" class="form-label">Boutique</label>
                            <select name="shop_id" id="shop_id" class="form-control @error('shop_id') is-invalid @enderror" required>
                                <option value="">Sélectionnez une boutique</option>
                                @foreach($shops as $shop)
                                    <option value="{{ $shop->id }}" {{ (old('shop_id', $sale->shop_id) == $shop->id) ? 'selected' : '' }}>
                                        {{ $shop->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('shop_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">Montant</label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0" name="amount" id="amount" 
                                    class="form-control @error('amount') is-invalid @enderror" 
                                    value="{{ old('amount', $sale->amount) }}" required>
                                <span class="input-group-text">€</span>
                            </div>
                            @error('amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="payment_method" class="form-label">Méthode de paiement</label>
                            <select name="payment_method" id="payment_method" class="form-control @error('payment_method') is-invalid @enderror" required>
                                <option value="cash" {{ old('payment_method', $sale->payment_method) == 'cash' ? 'selected' : '' }}>Espèces</option>
                                <option value="card" {{ old('payment_method', $sale->payment_method) == 'card' ? 'selected' : '' }}>Carte bancaire</option>
                                <option value="transfer" {{ old('payment_method', $sale->payment_method) == 'transfer' ? 'selected' : '' }}>Virement</option>
                            </select>
                            @error('payment_method')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Statut</label>
                            <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                                <option value="pending" {{ old('status', $sale->status) == 'pending' ? 'selected' : '' }}>En attente</option>
                                <option value="completed" {{ old('status', $sale->status) == 'completed' ? 'selected' : '' }}>Terminée</option>
                                <option value="cancelled" {{ old('status', $sale->status) == 'cancelled' ? 'selected' : '' }}>Annulée</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (optionnel)</label>
                            <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror">{{ old('notes', $sale->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('sales.index') }}" class="btn btn-secondary">Retour</a>
                            <button type="submit" class="btn btn-primary">Mettre à jour la vente</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
