<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('stocks', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
        });
    }

    public function down()
    {
        Schema::table('stocks', function (Blueprint $table) {
            // On ajoute d'abord la colonne
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
        });
    }
};
