<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CheckPaymentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $payment;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Nouveau Paiement par Chèque à Traiter')
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Un nouveau paiement par chèque a été reçu et nécessite votre attention.')
            ->line('Détails du chèque :')
            ->line("Numéro du chèque: {$this->payment->check_number}")
            ->line("Banque: {$this->payment->bank_name}")
            ->line("Montant: " . number_format($this->payment->amount, 2) . " FCFA")
            ->line("Client: {$this->payment->sale->customer->name}")
            ->line("Boutique: {$this->payment->sale->shop->name}")
            ->action('Traiter le chèque', url('/payments/' . $this->payment->id . '/process-check'))
            ->line('Veuillez traiter ce chèque selon la procédure standard.');
    }

    public function toArray($notifiable)
    {
        return [
            'payment_id' => $this->payment->id,
            'check_number' => $this->payment->check_number,
            'bank_name' => $this->payment->bank_name,
            'amount' => $this->payment->amount,
            'customer_name' => $this->payment->sale->customer->name,
            'shop_name' => $this->payment->sale->shop->name,
        ];
    }
}
