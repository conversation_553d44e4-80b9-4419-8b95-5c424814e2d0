<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountingPeriod extends Model
{
    use HasFactory;

    protected $fillable = [
        'start_date',
        'end_date',
        'status',
        'summary'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'summary' => 'array'
    ];

    public function reports()
    {
        return $this->hasMany(FinancialReport::class);
    }

    public function transactions()
    {
        return AccountingTransaction::whereBetween('transaction_date', [$this->start_date, $this->end_date]);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'open');
    }

    public function close()
    {
        $this->status = 'closed';
        $this->save();
    }
}
