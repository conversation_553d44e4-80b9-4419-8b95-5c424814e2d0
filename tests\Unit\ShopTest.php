<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ShopTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_main_shop()
    {
        $shopData = [
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'email' => '<EMAIL>',
            'is_main_shop' => true
        ];

        $shop = Shop::create($shopData);

        $this->assertDatabaseHas('shops', $shopData);
        $this->assertTrue($shop->is_main_shop);
        $this->assertNull($shop->parent_shop_id);
    }

    public function test_can_create_annex_shop()
    {
        $mainShop = Shop::create([
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'is_main_shop' => true
        ]);

        $annexData = [
            'name' => 'Boutique Annexe 1',
            'code' => 'ANX-001',
            'address' => '456 Rue Secondaire',
            'phone' => '+987654321',
            'is_main_shop' => false,
            'parent_shop_id' => $mainShop->id
        ];

        $annexShop = Shop::create($annexData);

        $this->assertDatabaseHas('shops', $annexData);
        $this->assertFalse($annexShop->is_main_shop);
        $this->assertEquals($mainShop->id, $annexShop->parent_shop_id);
    }

    public function test_shop_has_users()
    {
        $shop = Shop::create([
            'name' => 'Test Shop',
            'code' => 'TEST-001',
            'address' => 'Test Address',
            'phone' => '+123456789'
        ]);

        $user = User::factory()->create([
            'shop_id' => $shop->id
        ]);

        $this->assertTrue($shop->users->contains($user));
        $this->assertEquals($shop->id, $user->shop->id);
    }

    public function test_main_shop_can_have_multiple_annexes()
    {
        $mainShop = Shop::create([
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'is_main_shop' => true
        ]);

        $annex1 = Shop::create([
            'name' => 'Annexe 1',
            'code' => 'ANX-001',
            'address' => 'Adresse Annexe 1',
            'phone' => '+111111111',
            'parent_shop_id' => $mainShop->id
        ]);

        $annex2 = Shop::create([
            'name' => 'Annexe 2',
            'code' => 'ANX-002',
            'address' => 'Adresse Annexe 2',
            'phone' => '+222222222',
            'parent_shop_id' => $mainShop->id
        ]);

        $this->assertEquals(2, $mainShop->childShops()->count());
        $this->assertTrue($mainShop->childShops->contains($annex1));
        $this->assertTrue($mainShop->childShops->contains($annex2));
    }

    public function test_shop_code_must_be_unique()
    {
        Shop::create([
            'name' => 'Shop 1',
            'code' => 'SHOP-001',
            'address' => 'Address 1',
            'phone' => '+123456789'
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Shop::create([
            'name' => 'Shop 2',
            'code' => 'SHOP-001', // même code
            'address' => 'Address 2',
            'phone' => '+987654321'
        ]);
    }
}
