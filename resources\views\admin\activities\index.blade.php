@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Journal d'Activités</h2>
                <div class="flex space-x-2">
                    <button onclick="refreshActivities()"
                            class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Actualiser
                    </button>
                    <button onclick="exportActivities()"
                            class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition">
                        <i class="fas fa-file-export mr-2"></i>
                        Exporter
                    </button>
                </div>
            </div>
        </div>

        <!-- Cartes de résumé -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
                <i class="fas fa-bolt fa-2x"></i>
                <div>
                    <div class="font-bold text-xl">{{ $todayCount ?? 0 }}</div>
                    <div class="text-sm">Activités aujourd’hui</div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-green-400 to-green-600 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
                <i class="fas fa-users fa-2x"></i>
                <div>
                    <div class="font-bold text-xl">{{ $activeUsers ?? 0 }}</div>
                    <div class="text-sm">Utilisateurs actifs</div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-yellow-400 to-pink-500 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
                <i class="fas fa-database fa-2x"></i>
                <div>
                    <div class="font-bold text-xl">{{ $totalCount ?? 0 }}</div>
                    <div class="text-sm">Total activités</div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="p-4 bg-gray-50 border-b" x-data="{ showFilters: false }">
            <button @click="showFilters = !showFilters" 
                    class="flex items-center text-gray-600 hover:text-blue-600 focus:outline-none">
                <i class="fas fa-filter mr-2"></i>
                <span>Filtres avancés</span>
                <i class="fas" :class="showFilters ? 'fa-chevron-up ml-2' : 'fa-chevron-down ml-2'"></i>
            </button>

            <div x-show="showFilters" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Utilisateur</label>
                    <select id="user-filter" class="form-select rounded-lg border-gray-300">
                        <option value="">Tous les utilisateurs</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Type d'activité</label>
                    <select id="type-filter" class="form-select rounded-lg border-gray-300">
                        <option value="">Tous les types</option>
                        <option value="login">Connexion</option>
                        <option value="logout">Déconnexion</option>
                        <option value="create">Création</option>
                        <option value="update">Modification</option>
                        <option value="delete">Suppression</option>
                        <option value="sale">Vente</option>
                        <option value="stock">Stock</option>
                        <option value="inventory">Inventaire</option>
                    </select>
                </div>

                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Boutique</label>
                    <select id="shop-filter" class="form-select rounded-lg border-gray-300">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}">{{ $shop->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Période</label>
                    <select id="period-filter" class="form-select rounded-lg border-gray-300">
                        <option value="today">Aujourd'hui</option>
                        <option value="yesterday">Hier</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                        <option value="custom">Période personnalisée</option>
                    </select>
                </div>

                <div class="md:col-span-4 grid grid-cols-1 md:grid-cols-2 gap-4" x-show="document.getElementById('period-filter').value === 'custom'">
                    <div class="flex flex-col">
                        <label class="mb-1 text-sm text-gray-600">Date début</label>
                        <input type="date" id="start-date" class="form-input rounded-lg border-gray-300">
                    </div>
                    <div class="flex flex-col">
                        <label class="mb-1 text-sm text-gray-600">Date fin</label>
                        <input type="date" id="end-date" class="form-input rounded-lg border-gray-300">
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau glassmorphism -->
        <div class="overflow-x-auto rounded-2xl shadow-2xl bg-white/60 backdrop-blur-md">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-white/80">
                    <tr>
                        <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Utilisateur</th>
                        <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Type</th>
                        <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Description</th>
                        <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Date</th>
                        <th class="px-6 py-3"></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($activities as $activity)
                    <tr class="hover:bg-blue-50 transition shadow-sm">
                        <td class="flex items-center gap-3 py-4">
                            <img src="{{ optional($activity->user)->profile_photo_url ?? asset('images/default-avatar.png') }}" class="w-10 h-10 rounded-full border-2 border-blue-500 shadow">
                            <div>
                                <div class="font-semibold">{{ optional($activity->user)->name ?? 'Utilisateur inconnu' }}</div>
                                <div class="text-xs text-gray-400">{{ optional($activity->user && $activity->user->roles ? $activity->user->roles->first() : null)->name ?? '' }}</div>
                            </div>
                        </td>
                        <td>
                            <span class="px-3 py-1 rounded-full text-xs font-bold
                                @if($activity->type == 'create') bg-green-100 text-green-700
                                @elseif($activity->type == 'delete') bg-red-100 text-red-700
                                @elseif($activity->type == 'update') bg-yellow-100 text-yellow-800
                                @else bg-blue-100 text-blue-700 @endif">
                                {{ ucfirst($activity->type) }}
                            </span>
                        </td>
                        <td>{{ $activity->description }}</td>
                        <td>{{ $activity->created_at->format('d/m/Y H:i:s') }}</td>
                        <td>
                            <button onclick="showActivityDetail({{ $activity->id }})"
                                class="bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1 rounded-lg shadow transition">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50 flex justify-between items-center">
            {{ $activities->links() }}
            <button onclick="window.scrollTo({top:0,behavior:'smooth'})" class="fixed bottom-6 right-6 z-50 bg-blue-600 text-white rounded-full shadow-lg p-3 hover:bg-blue-700 transition">
                <i class="fas fa-arrow-up"></i>
            </button>
        </div>
    </div>

    <!-- Modal de détail (à personnaliser selon ton JS) -->
    <div id="activity-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-lg w-full relative">
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 text-xl" onclick="closeActivityModal()"><i class="fas fa-times"></i></button>
            <div id="activity-detail-content">
                <!-- Contenu dynamique JS -->
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Mise à jour en temps réel des activités
    function refreshActivities() {
        fetch('/admin/activities/refresh')
            .then(response => response.json())
            .then(data => {
                document.getElementById('activities-table').innerHTML = data.html;
            });
    }

    // Export des activités
    function exportActivities() {
        const filters = {
            user: document.getElementById('user-filter').value,
            type: document.getElementById('type-filter').value,
            shop: document.getElementById('shop-filter').value,
            period: document.getElementById('period-filter').value,
            startDate: document.getElementById('start-date').value,
            endDate: document.getElementById('end-date').value
        };

        window.location.href = `/admin/activities/export?${new URLSearchParams(filters)}`;
    }

    // Affichage des détails d'une activité
    function showActivityDetail(activityId) {
        fetch(`/admin/activities/${activityId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('activity-detail-content').innerHTML = `
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Utilisateur</label>
                            <p class="mt-1 text-sm text-gray-900">${data.user.name}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Type</label>
                            <p class="mt-1 text-sm text-gray-900">${data.type}</p>
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900">${data.description}</p>
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Détails</label>
                            <pre class="mt-1 text-sm text-gray-900 bg-gray-50 p-4 rounded-lg overflow-auto">${JSON.stringify(data.details, null, 2)}</pre>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">IP</label>
                            <p class="mt-1 text-sm text-gray-900">${data.ip_address}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date</label>
                            <p class="mt-1 text-sm text-gray-900">${data.created_at}</p>
                        </div>
                    </div>
                `;
                window.dispatchEvent(new CustomEvent('activity-modal'));
            });
    }

    // Filtrage dynamique
    document.querySelectorAll('select, input[type="date"]').forEach(element => {
        element.addEventListener('change', function() {
            applyFilters();
        });
    });

    function applyFilters() {
        const filters = {
            user: document.getElementById('user-filter').value,
            type: document.getElementById('type-filter').value,
            shop: document.getElementById('shop-filter').value,
            period: document.getElementById('period-filter').value,
            startDate: document.getElementById('start-date').value,
            endDate: document.getElementById('end-date').value
        };

        const url = new URL(window.location.href);
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                url.searchParams.set(key, filters[key]);
            } else {
                url.searchParams.delete(key);
            }
        });

        window.location.href = url.toString();
    }

    // Mise à jour automatique toutes les 30 secondes
    setInterval(refreshActivities, 30000);
</script>
@endpush
@endsection
