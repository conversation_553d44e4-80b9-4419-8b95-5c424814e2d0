<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupplyRequestItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'supply_request_id',
        'product_id',
        'requested_quantity',
        'approved_quantity',
        'notes'
    ];

    protected $casts = [
        'requested_quantity' => 'integer',
        'approved_quantity' => 'integer'
    ];

    /**
     * Obtenir la demande d'approvisionnement parente
     */
    public function supplyRequest()
    {
        return $this->belongsTo(SupplyRequest::class);
    }

    /**
     * Obtenir le produit concerné
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Vérifier si la quantité approuvée correspond à la quantité demandée
     */
    public function isFullyApproved()
    {
        return $this->approved_quantity >= $this->requested_quantity;
    }
}
