@extends('layouts.admin')

@section('content')
<div class="container mt-4">
    <h2>Éditer le rapport fiscal</h2>
    <form action="{{ route('admin.accounting.tax_reports.update', $taxReport) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="form-group">
            <label for="period">Période</label>
            <input type="text" name="period" id="period" class="form-control" value="{{ $taxReport->period }}" required>
        </div>
        <div class="form-group">
            <label for="total_tva_collected">TVA collectée (€)</label>
            <input type="number" step="0.01" name="total_tva_collected" id="total_tva_collected" class="form-control" value="{{ $taxReport->total_tva_collected }}" required>
        </div>
        <div class="form-group">
            <label for="total_tva_deductible">TVA déductible (€)</label>
            <input type="number" step="0.01" name="total_tva_deductible" id="total_tva_deductible" class="form-control" value="{{ $taxReport->total_tva_deductible }}" required>
        </div>
        <div class="form-group">
            <label for="total_taxes">Total impôts (€)</label>
            <input type="number" step="0.01" name="total_taxes" id="total_taxes" class="form-control" value="{{ $taxReport->total_taxes }}" required>
        </div>
        <div class="form-group">
            <label for="status">État</label>
            <select name="status" id="status" class="form-control" required>
                <option value="draft" @if($taxReport->status=='draft') selected @endif>Brouillon</option>
                <option value="submitted" @if($taxReport->status=='submitted') selected @endif>Soumis</option>
                <option value="paid" @if($taxReport->status=='paid') selected @endif>Payé</option>
            </select>
        </div>
        <button type="submit" class="btn btn-success">Mettre à jour</button>
        <a href="{{ route('admin.accounting.tax_reports.index') }}" class="btn btn-secondary">Annuler</a>
    </form>
</div>
@endsection
