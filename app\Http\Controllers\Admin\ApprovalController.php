<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ApprovalController extends Controller
{
    public function index() { return view('admin.approvals.index'); }
    public function create() { return view('admin.approvals.create'); }
    public function store(Request $request) { return redirect()->route('admin.approvals.index'); }
    public function show($id) { return view('admin.approvals.show', compact('id')); }
    public function edit($id) { return view('admin.approvals.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.approvals.index'); }
    public function destroy($id) { return redirect()->route('admin.approvals.index'); }
}
