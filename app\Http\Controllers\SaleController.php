<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Product;
use App\Models\Stock;
use Illuminate\Http\Request;
use App\Http\Requests\SaleRequest;
use Illuminate\Support\Facades\DB;
use PDF;

class SaleController extends Controller
{
    public function index()
    {
        $sales = Sale::with(['user', 'shop', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('sales.index', compact('sales'));
    }

    public function create()
    {
        $products = Product::with('stock')->get();
        return view('sales.create', compact('products'));
    }

    public function store(SaleRequest $request)
    {
        DB::beginTransaction();
        try {
            $sale = Sale::create([
                'user_id' => auth()->id(),
                'shop_id' => $request->shop_id,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'total_amount' => 0,
                'payment_method' => $request->payment_method,
                'payment_status' => $request->payment_status,
                'notes' => $request->notes
            ]);

            $totalAmount = 0;
            foreach ($request->items as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                // Vérifier le stock disponible
                if ($product->quantity < $item['quantity']) {
                    throw new \Exception("Stock insuffisant pour {$product->name}");
                }

                // Créer la ligne de vente
                $sale->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['quantity'] * $item['unit_price']
                ]);

                // Mettre à jour le stock
                $product->decrement('quantity', $item['quantity']);
                
                // Enregistrer le mouvement de stock
                Stock::create([
                    'product_id' => $item['product_id'],
                    'shop_id' => $request->shop_id,
                    'quantity' => $item['quantity'],
                    'type' => 'sale',
                    'status' => 'completed',
                    'notes' => "Vente #{$sale->id}"
                ]);

                $totalAmount += $item['quantity'] * $item['unit_price'];
            }

            $sale->update(['total_amount' => $totalAmount]);

            DB::commit();
            return redirect()->route('sales.show', $sale)->with('success', 'Vente enregistrée avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    public function show(Sale $sale)
    {
        $sale->load(['user', 'shop', 'items.product']);
        return view('sales.show', compact('sale'));
    }

    public function edit(Sale $sale)
    {
        if ($sale->status !== 'pending') {
            return back()->with('error', 'Cette vente ne peut plus être modifiée');
        }

        $products = Product::with('stock')->get();
        return view('sales.edit', compact('sale', 'products'));
    }

    public function update(SaleRequest $request, Sale $sale)
    {
        if ($sale->status !== 'pending') {
            return back()->with('error', 'Cette vente ne peut plus être modifiée');
        }

        DB::beginTransaction();
        try {
            // Annuler les mouvements de stock précédents
            foreach ($sale->items as $item) {
                $item->product->increment('quantity', $item->quantity);
            }

            // Supprimer les anciennes lignes
            $sale->items()->delete();

            // Créer les nouvelles lignes
            $totalAmount = 0;
            foreach ($request->items as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                if ($product->quantity < $item['quantity']) {
                    throw new \Exception("Stock insuffisant pour {$product->name}");
                }

                $sale->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['quantity'] * $item['unit_price']
                ]);

                $product->decrement('quantity', $item['quantity']);
                
                Stock::create([
                    'product_id' => $item['product_id'],
                    'shop_id' => $request->shop_id,
                    'quantity' => $item['quantity'],
                    'type' => 'sale',
                    'status' => 'completed',
                    'notes' => "Vente #{$sale->id} (modification)"
                ]);

                $totalAmount += $item['quantity'] * $item['unit_price'];
            }

            $sale->update([
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'payment_status' => $request->payment_status,
                'notes' => $request->notes
            ]);

            DB::commit();
            return redirect()->route('sales.show', $sale)->with('success', 'Vente mise à jour avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    public function destroy(Sale $sale)
    {
        if ($sale->status !== 'pending') {
            return back()->with('error', 'Cette vente ne peut plus être supprimée');
        }

        DB::beginTransaction();
        try {
            // Restaurer les stocks
            foreach ($sale->items as $item) {
                $item->product->increment('quantity', $item->quantity);
                
                Stock::create([
                    'product_id' => $item->product_id,
                    'shop_id' => $sale->shop_id,
                    'quantity' => $item->quantity,
                    'type' => 'supply',
                    'status' => 'completed',
                    'notes' => "Annulation vente #{$sale->id}"
                ]);
            }

            $sale->delete();

            DB::commit();
            return redirect()->route('sales.index')->with('success', 'Vente supprimée avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Une erreur est survenue lors de la suppression');
        }
    }

    public function invoice(Sale $sale)
    {
        $sale->load(['user', 'shop', 'items.product']);
        $pdf = PDF::loadView('sales.invoice', compact('sale'));
        return $pdf->download("facture-{$sale->id}.pdf");
    }

    public function export()
    {
        return Excel::download(new SalesExport, 'ventes.xlsx');
    }
}
