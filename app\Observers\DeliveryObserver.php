<?php

namespace App\Observers;

use App\Models\Delivery;
use App\Models\User;
use App\Notifications\DeliveryNotification;

class DeliveryObserver
{
    public function created(Delivery $delivery)
    {
        $admins = User::role('admin')->get();
        $managers = User::role('manager')->get();
        
        $recipients = $admins->merge($managers)->unique('id');
        
        foreach ($recipients as $recipient) {
            $recipient->notify(new DeliveryNotification($delivery));
        }
    }
}
