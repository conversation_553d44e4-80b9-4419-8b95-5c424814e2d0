<?php

namespace App\Notifications;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LowStockAlert extends Notification implements ShouldQueue
{
    use Queueable;

    protected $stock;

    public function __construct(Stock $stock)
    {
        $this->stock = $stock;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Alerte de stock bas - ' . $this->stock->product->name)
            ->line('Le stock du produit suivant est bas :')
            ->line('Produit : ' . $this->stock->product->name)
            ->line('Boutique : ' . $this->stock->shop->name)
            ->line('Quantité actuelle : ' . $this->stock->quantity)
            ->line('Stock minimum : ' . $this->stock->min_stock)
            ->action('Voir le produit', route('products.show', $this->stock->product))
            ->line('Veuillez procéder au réapprovisionnement dès que possible.');
    }

    public function toArray($notifiable)
    {
        return [
            'stock_id' => $this->stock->id,
            'product_id' => $this->stock->product_id,
            'shop_id' => $this->stock->shop_id,
            'product_name' => $this->stock->product->name,
            'shop_name' => $this->stock->shop->name,
            'quantity' => $this->stock->quantity,
            'min_stock' => $this->stock->min_stock,
            'type' => 'low_stock_alert'
        ];
    }
}
