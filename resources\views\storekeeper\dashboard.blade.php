@extends('layouts.storekeeper')

@section('title', 'Tableau de bord magasinier')

@section('content')
<div class="container-fluid">
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Produits</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_products'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock Critique</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['low_stock'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Approvisionnements</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_supplies'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck-loading fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Transferts</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_transfers'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Mouvements de stock récents -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Mouvements de stock récents</h6>
                    <div>
                        <a href="{{ route('storekeeper.supplies.create') }}" class="btn btn-sm btn-info mr-2">
                            <i class="fas fa-plus"></i> Approvisionnement
                        </a>
                        <a href="{{ route('storekeeper.transfers.create') }}" class="btn btn-sm btn-success">
                            <i class="fas fa-exchange-alt"></i> Transfert
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Quantité</th>
                                    <th>Opérateur</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentMovements as $movement)
                                <tr>
                                    <td>{{ $movement->created_at->format('d/m/Y H:i') }}</td>
                                    <td>{{ $movement->product->name }}</td>
                                    <td>
                                        <span class="badge badge-{{ $movement->type_color }}">
                                            {{ $movement->type_label }}
                                        </span>
                                    </td>
                                    <td class="{{ $movement->quantity > 0 ? 'text-success' : 'text-danger' }}">
                                        {{ $movement->quantity > 0 ? '+' : '' }}{{ $movement->quantity }}
                                    </td>
                                    <td>{{ $movement->user->name }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">Aucun mouvement récent</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <!-- Stock critique -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Stock critique</h6>
                </div>
                <div class="card-body">
                    @if($lowStockProducts->isEmpty())
                        <p class="text-center">Aucun produit en stock critique</p>
                    @else
                        <div class="list-group">
                            @foreach($lowStockProducts as $product)
                            <a href="{{ route('storekeeper.products.show', $product) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                    <small class="text-danger">
                                        {{ $product->stocks->first()->quantity }} unités
                                    </small>
                                </div>
                                <small>Stock minimum : {{ $product->min_stock }}</small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Approvisionnements en attente -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Approvisionnements en attente</h6>
                </div>
                <div class="card-body">
                    @if($pendingSupplies->isEmpty())
                        <p class="text-center">Aucun approvisionnement en attente</p>
                    @else
                        <div class="list-group">
                            @foreach($pendingSupplies as $supply)
                            <a href="{{ route('storekeeper.supplies.show', $supply) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $supply->supplier->name }}</h6>
                                    <small>{{ $supply->expected_date->format('d/m/Y') }}</small>
                                </div>
                                <small>{{ $supply->items_count }} produits</small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Transferts en attente -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Transferts en attente</h6>
                </div>
                <div class="card-body">
                    @if($pendingTransfers->isEmpty())
                        <p class="text-center">Aucun transfert en attente</p>
                    @else
                        <div class="list-group">
                            @foreach($pendingTransfers as $transfer)
                            <a href="{{ route('storekeeper.transfers.show', $transfer) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        {{ $transfer->source_shop_id == auth()->user()->shop_id ? 
                                            'Vers ' . $transfer->destinationShop->name :
                                            'De ' . $transfer->sourceShop->name }}
                                    </h6>
                                    <small>{{ $transfer->created_at->format('d/m/Y') }}</small>
                                </div>
                                <small>{{ $transfer->items_count }} produits</small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
