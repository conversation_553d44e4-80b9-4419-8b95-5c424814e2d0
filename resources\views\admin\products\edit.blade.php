@extends('layouts.admin')

@section('content')
<div class="max-w-4xl mx-auto py-10">
    <div class="bg-gradient-to-br from-pink-500 via-blue-500 to-purple-500 rounded-xl shadow-xl p-1">
        <div class="bg-white rounded-xl shadow p-8 flex flex-col gap-6">
            <h2 class="text-3xl font-extrabold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-edit text-pink-500"></i> Modifier le produit
            </h2>
            <form method="POST" action="{{ route('admin.products.update', $product) }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nom du produit -->
                    <div>
                        <label for="name" class="block text-md font-medium text-gray-700 mb-1">Nom du produit</label>
                        <input type="text" name="name" id="name" class="form-input w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" value="{{ old('name', $product->name) }}" required style="border-width:2px!important;border-color:#ec4899!important;" />
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Catégorie -->
                    <div>
                        <label for="category_id" class="block text-md font-medium text-gray-700 mb-1">Catégorie</label>
                        <select name="category_id" id="category_id" class="form-select w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" required>
                            <option value="">Sélectionnez une catégorie</option>
                            @foreach($categories as $id => $cat)
                                <option value="{{ $id }}" {{ old('category_id', $product->category_id) == $id ? 'selected' : '' }}>{{ $cat }}</option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Prix -->
                    <div>
                        <label for="price" class="block text-md font-medium text-gray-700 mb-1">Prix (FCFA)</label>
                        <input type="number" step="0.01" name="price" id="price" class="form-input w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" value="{{ old('price', $product->price) }}" required />
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Unité -->
                    <div>
                        <label for="unit" class="block text-md font-medium text-gray-700 mb-1">Unité</label>
                        <select name="unit" id="unit" class="form-select w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" required>
                            <option value="">Sélectionnez une unité</option>
                            <option value="pièce" {{ old('unit', $product->unit) == 'pièce' ? 'selected' : '' }}>Pièce</option>
                            <option value="paquet" {{ old('unit', $product->unit) == 'paquet' ? 'selected' : '' }}>Paquet</option>
                            <option value="boîte" {{ old('unit', $product->unit) == 'boîte' ? 'selected' : '' }}>Boîte</option>
                            <option value="carton" {{ old('unit', $product->unit) == 'carton' ? 'selected' : '' }}>Carton</option>
                            <option value="bouteille" {{ old('unit', $product->unit) == 'bouteille' ? 'selected' : '' }}>Bouteille</option>
                            <option value="litre" {{ old('unit', $product->unit) == 'litre' ? 'selected' : '' }}>Litre</option>
                            <option value="kilogramme" {{ old('unit', $product->unit) == 'kilogramme' ? 'selected' : '' }}>Kilogramme</option>
                            <option value="sachet" {{ old('unit', $product->unit) == 'sachet' ? 'selected' : '' }}>Sachet</option>
                            <option value="seau" {{ old('unit', $product->unit) == 'seau' ? 'selected' : '' }}>Seau</option>
                            <option value="fût" {{ old('unit', $product->unit) == 'fût' ? 'selected' : '' }}>Fût</option>
                        </select>
                        @error('unit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Stock initial -->
                    <div>
                        <label for="stock" class="block text-md font-medium text-gray-700 mb-1">Stock initial</label>
                        <input type="number" name="stock" id="stock" min="0" class="form-input w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" value="{{ old('stock', $product->stock) }}" required />
                        @error('stock')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Stock minimum -->
                    <div>
                        <label for="min_stock" class="block text-md font-medium text-gray-700 mb-1">Stock minimum</label>
                        <input type="number" name="min_stock" id="min_stock" min="0" class="form-input w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" value="{{ old('min_stock', $product->min_stock) }}" required />
                        @error('min_stock')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Boutique -->
                    <div>
                        <label for="shop_id" class="block text-md font-medium text-gray-700 mb-1">Boutique</label>
                        <select name="shop_id" id="shop_id" class="form-select w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" required>
                            <option value="">Sélectionnez une boutique</option>
                            @foreach($shops as $id => $shop)
                                <option value="{{ $id }}" {{ old('shop_id', $product->shop_id) == $id ? 'selected' : '' }}>{{ $shop }}</option>
                            @endforeach
                        </select>
                        @error('shop_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <!-- Statut -->
                    <div>
                        <label for="is_active" class="block text-md font-medium text-gray-700 mb-1">Statut</label>
                        <select name="is_active" id="is_active" class="form-select w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" required>
                            <option value="1" {{ old('is_active', $product->is_active) == 1 ? 'selected' : '' }}>Actif</option>
                            <option value="0" {{ old('is_active', $product->is_active) == 0 ? 'selected' : '' }}>Inactif</option>
                        </select>
                        @error('is_active')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                <!-- Description -->
                <div>
                    <label for="description" class="block text-md font-medium text-gray-700 mb-1">Description</label>
                    <textarea name="description" id="description" rows="3" class="form-textarea w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" required>{{ old('description', $product->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- Image -->
                <div>
                    <label for="image" class="block text-md font-medium text-gray-700 mb-1">Image du produit</label>
                    <input type="file" name="image" id="image" class="form-input w-full rounded border-2 border-pink-500 focus:border-pink-500 focus:ring-pink-500" accept="image/*">
                    @if($product->image_url)
                        <div class="mt-2">
                            <img src="{{ $product->image_url }}" alt="Image actuelle" class="h-20 w-20 object-cover rounded border border-pink-500">
                        </div>
                    @endif
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div class="flex justify-end gap-2 mt-6">
                    <button type="submit" class="px-6 py-2 bg-pink-600 text-white rounded shadow hover:bg-pink-700 transition"><i class="fas fa-save mr-1"></i>Enregistrer les modifications</button>
                    <a href="{{ route('admin.products.show', $product) }}" class="px-6 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700 transition"><i class="fas fa-arrow-left mr-1"></i>Annuler</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/custom-form.css') }}">
@endpush
