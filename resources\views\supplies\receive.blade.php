@extends('layouts.app')

@section('title', 'Réception de l\'approvisionnement')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            Réception de l'approvisionnement {{ $supply->reference_number }}
        </h1>
        <a href="{{ route('supplies.show', $supply) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Réf<PERSON>rence</dt>
                        <dd class="col-sm-8">{{ $supply->reference_number }}</dd>

                        <dt class="col-sm-4">Fournisseur</dt>
                        <dd class="col-sm-8">{{ $supply->supplier->name }}</dd>

                        <dt class="col-sm-4">Boutique</dt>
                        <dd class="col-sm-8">{{ $supply->shop->name }}</dd>

                        <dt class="col-sm-4">Date commande</dt>
                        <dd class="col-sm-8">{{ $supply->order_date->format('d/m/Y') }}</dd>

                        <dt class="col-sm-4">Livraison prévue</dt>
                        <dd class="col-sm-8">{{ $supply->expected_delivery_date->format('d/m/Y') }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Réception des produits</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('supplies.receive', $supply) }}" method="POST" id="receiveForm">
                        @csrf
                        @method('PUT')

                        <div class="table-responsive mb-4">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Produit</th>
                                        <th>Commandé</th>
                                        <th>Reçu</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($supply->items as $item)
                                        <tr>
                                            <td>{{ $item->product->name }}</td>
                                            <td>{{ $item->ordered_quantity }}</td>
                                            <td>
                                                <input type="hidden" name="items[{{ $item->id }}][id]" 
                                                       value="{{ $item->id }}">
                                                <input type="number" class="form-control @error('items.'.$item->id.'.received_quantity') is-invalid @enderror"
                                                       name="items[{{ $item->id }}][received_quantity]"
                                                       value="{{ old('items.'.$item->id.'.received_quantity', $item->ordered_quantity) }}"
                                                       min="0" max="{{ $item->ordered_quantity }}" required>
                                                @error('items.'.$item->id.'.received_quantity')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </td>
                                            <td>
                                                <input type="text" class="form-control"
                                                       name="items[{{ $item->id }}][notes]"
                                                       value="{{ old('items.'.$item->id.'.notes') }}"
                                                       placeholder="Commentaires sur la réception">
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes générales de réception</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-primary" id="fillAll">
                                <i class="fas fa-check-double"></i> Tout marquer comme reçu
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Confirmer la réception
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation de réception partielle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Certains produits n'ont pas été reçus en totalité. Voulez-vous confirmer la réception partielle ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmReceive">Confirmer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Remplir toutes les quantités
        $('#fillAll').on('click', function() {
            $('input[name$="[received_quantity]"]').each(function() {
                $(this).val($(this).attr('max'));
            });
        });

        // Vérification avant soumission
        $('#receiveForm').on('submit', function(e) {
            e.preventDefault();
            let isPartial = false;

            $('input[name$="[received_quantity]"]').each(function() {
                if (parseInt($(this).val()) < parseInt($(this).attr('max'))) {
                    isPartial = true;
                    return false;
                }
            });

            if (isPartial) {
                $('#confirmModal').modal('show');
            } else {
                this.submit();
            }
        });

        // Confirmation de réception partielle
        $('#confirmReceive').on('click', function() {
            $('#receiveForm')[0].submit();
        });

        // Validation des quantités
        $('input[name$="[received_quantity]"]').on('input', function() {
            const max = parseInt($(this).attr('max'));
            const val = parseInt($(this).val());
            
            if (val > max) {
                $(this).val(max);
            }
        });
    });
</script>
@endpush
