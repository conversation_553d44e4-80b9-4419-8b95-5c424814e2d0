<?php

namespace App\Notifications;

use App\Models\Sale;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SaleNotification extends Notification
{
    use Queueable;

    public $sale;

    public function __construct(Sale $sale)
    {
        $this->sale = $sale;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Nouvelle vente')
            ->line('Une nouvelle vente a été enregistrée.')
            ->line("Montant: {$this->sale->amount} FCFA")
            ->line("Boutique: {$this->sale->shop->name}")
            ->line("Vendeur: {$this->sale->user->name}")
            ->line("Statut: {$this->sale->status}")
            ->line("Mode de paiement: {$this->sale->payment_method}")
            ->line('Merci de votre attention !');
    }
}
