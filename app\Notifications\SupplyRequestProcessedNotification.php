<?php

namespace App\Notifications;

use App\Models\SupplyRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;

class SupplyRequestProcessedNotification extends Notification
{
    use Queueable;

    protected $supplyRequest;

    /**
     * Créer une nouvelle instance de notification
     */
    public function __construct(SupplyRequest $supplyRequest)
    {
        $this->supplyRequest = $supplyRequest;
    }

    /**
     * Obtenir les canaux de livraison de la notification
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Obtenir la représentation mail de la notification
     */
    public function toMail($notifiable)
    {
        $url = route('supply-requests.show', $this->supplyRequest->id);

        return (new MailMessage)
            ->subject('Demande d\'Approvisionnement Traitée - ' . $this->supplyRequest->reference_number)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Votre demande d\'approvisionnement ' . $this->supplyRequest->reference_number . ' a été traitée.')
            ->line('Boutique source : ' . $this->supplyRequest->supplyingShop->name)
            ->line('Date de traitement : ' . now()->format('d/m/Y H:i'))
            ->line('Traité par : ' . $this->supplyRequest->processor->name)
            ->when($this->supplyRequest->notes, function ($mail) {
                return $mail->line('Notes : ' . $this->supplyRequest->notes);
            })
            ->action('Voir les détails', $url)
            ->line('Les stocks ont été automatiquement mis à jour dans votre boutique.');
    }

    /**
     * Obtenir la représentation array de la notification
     */
    public function toArray($notifiable)
    {
        return [
            'supply_request_id' => $this->supplyRequest->id,
            'reference_number' => $this->supplyRequest->reference_number,
            'supplying_shop' => $this->supplyRequest->supplyingShop->name,
            'processed_date' => now(),
            'processor_name' => $this->supplyRequest->processor->name,
            'notes' => $this->supplyRequest->notes,
            'type' => 'supply_request_processed'
        ];
    }
}
