<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class Login<PERSON>ontroller extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Get the post-login redirect path based on user role.
     *
     * @return string
     */
    protected function redirectTo()
    {
        $user = auth()->user();
        if ($user->hasRole('admin')) {
            return '/admin/dashboard';
        } elseif ($user->hasRole('manager')) {
            return '/manager/dashboard';
        } elseif ($user->hasRole('biller')) {
            return '/biller/dashboard';
        } elseif ($user->hasRole('cashier')) {
            return '/cashier/dashboard';
        } elseif ($user->hasRole('storekeeper')) {
            return '/storekeeper/dashboard';
        } elseif ($user->hasRole('accountant')) {
            return '/accountant/dashboard';
        }
        return '/dashboard';
    }

    /**
     * Show the application's login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.custom-login'); // Remplacez par le chemin de votre vue personnalisée
    }
}
