<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockDetail extends Model
{
    use HasFactory;

    // Correction du nom de la table pour correspondre à la migration
    protected $table = 'stock_details';

    protected $fillable = [
        'stock_id',
        'product_id',
        'quantity',
        'unit',
        'unit_price',
        'total_price',
    ];

    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
