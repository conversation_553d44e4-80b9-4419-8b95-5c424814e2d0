<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Alert;

class AlertController extends Controller
{
    public function index()
    {
        $alerts = Alert::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.alerts.index', compact('alerts'));
    }

    public function markAsRead(Request $request)
    {
        $alertIds = $request->input('alert_ids', []);
        Alert::whereIn('id', $alertIds)->update(['read_at' => now()]);

        return response()->json(['message' => 'Alertes marquées comme lues']);
    }

    public function destroy(Alert $alert)
    {
        $alert->delete();
        return response()->json(['message' => 'Alerte supprimée']);
    }

    public function deleteMultiple(Request $request)
    {
        $alertIds = $request->input('alert_ids', []);
        Alert::whereIn('id', $alertIds)->delete();
        return response()->json(['message' => 'Alertes supprimées']);
    }
}
