@extends('layouts.admin')
@section('title', "Modifier l'écriture comptable")

@push('styles')
<style>
body {
    background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
    min-height: 100vh;
}
.vibrant-bg {
    background: linear-gradient(120deg, rgba(129,140,248,0.25) 0%, rgba(236,72,153,0.18) 100%);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
}
.vibrant-table-bg {
    background: linear-gradient(90deg, rgba(34,211,238,0.12) 0%, rgba(236,72,153,0.10) 100%);
}
.text-gradient {
    background: linear-gradient(90deg, #6366f1, #06b6d4, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.text-gradient-blue { color: #2563eb; }
.text-gradient-green { color: #22d3ee; }
.text-gradient-pink { color: #ec4899; }
.glass-card, .glass-table-container, .glass-alert {
    background: rgba(255,255,255,0.32);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
    backdrop-filter: blur(8px);
    border-radius: 18px;
    border: 1px solid rgba(255,255,255,0.18);
}
.glass-btn, .neon-btn {
    background: linear-gradient(90deg, #818cf8 0%, #5eead4 100%);
    border: none;
    border-radius: 999px;
    padding: 0.6em 1.2em;
    color: #fff;
    font-weight: bold;
    box-shadow: 0 0 10px #818cf8, 0 0 30px #5eead4;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.glass-btn:hover, .neon-btn:hover {
    background: linear-gradient(90deg, #ec4899 0%, #818cf8 100%);
    color: #fff;
    box-shadow: 0 0 24px #ec4899, 0 0 40px #818cf8;
}
.vibrant-input {
    background: rgba(255,255,255,0.7);
    border: 1.5px solid #818cf8;
    border-radius: 999px;
    padding: 0.7em 1.2em;
    font-size: 1.1em;
    box-shadow: 0 1px 4px 0 rgba(31,38,135,0.08);
    transition: border 0.2s, box-shadow 0.2s;
}
.vibrant-input:focus {
    border: 2px solid #ec4899;
    box-shadow: 0 2px 8px 0 rgba(236,72,153,0.18);
    outline: none;
}
.glass-row-anim {
    animation: fadeInUp 0.6s;
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px);}
    to { opacity: 1; transform: translateY(0);}
}
</style>
@endpush

@section('content')
<div class="glass-edit-header d-flex align-items-center justify-content-between mb-4">
    <div>
        <a href="{{ route('admin.accounting.journal_entries.index') }}" class="glass-btn glass-back me-3" title="Retour">
            <i class="fas fa-arrow-left"></i>
        </a>
        <span class="h3 fw-bold text-gradient">
            <i class="fas fa-pen-fancy me-2"></i>Modifier l'écriture
        </span>
    </div>
    <button class="glass-btn glass-dark" id="toggle-darkmode" title="Mode sombre">
        <i class="fas fa-moon"></i>
    </button>
</div>

@if($errors->any())
    <div class="glass-alert glass-danger mb-3">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<form method="POST" action="{{ route('admin.accounting.journal_entries.update', $entry) }}" class="glass-card glass-form p-4 rounded-4 shadow-lg border-0 mx-auto vibrant-bg" style="max-width: 950px;">
    @csrf
    @method('PUT')
    <div class="row g-4">
        <div class="col-md-6">
            <label for="date" class="form-label fw-bold text-gradient">Date</label>
            <input type="date" name="date" id="date" class="glass-input vibrant-input" value="{{ old('date', is_object($entry->date) ? $entry->date->format('Y-m-d') : \Illuminate\Support\Carbon::parse($entry->date)->format('Y-m-d')) }}" required>
        </div>
        <div class="col-md-6">
            <label for="reference" class="form-label fw-bold text-gradient-blue">Référence</label>
            <input type="text" name="reference" id="reference" class="glass-input vibrant-input" value="{{ old('reference', $entry->reference) }}">
        </div>
        <div class="col-12">
            <label for="description" class="form-label fw-bold text-gradient-pink">Description</label>
            <input type="text" name="description" id="description" class="glass-input vibrant-input" value="{{ old('description', $entry->description) }}">
        </div>
    </div>

    <h5 class="mt-4 mb-2 text-gradient-green">Lignes d'écriture <span class="text-danger">*</span></h5>
    <div class="glass-table-container mb-4 vibrant-table-bg">
        <table class="glass-table align-middle">
            <thead>
                <tr>
                    <th class="text-gradient-blue">Compte</th>
                    <th class="text-gradient-green">Débit</th>
                    <th class="text-gradient-pink">Crédit</th>
                    <th></th>
                </tr>
            </thead>
            <tbody id="lines-table-body">
                @foreach($entry->lines as $i => $line)
                    <tr class="glass-row-anim">
                        <td>
                            <select name="lines[{{ $i }}][account_id]" class="glass-input vibrant-input" required>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}" @if($line->account_id == $account->id) selected @endif>
                                        {{ $account->code }} - {{ $account->name }}
                                    </option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            <input type="number" name="lines[{{ $i }}][debit]" class="glass-input vibrant-input text-end" value="{{ old('lines.'.$i.'.debit', $line->debit) }}" step="0.01" min="0">
                        </td>
                        <td>
                            <input type="number" name="lines[{{ $i }}][credit]" class="glass-input vibrant-input text-end" value="{{ old('lines.'.$i.'.credit', $line->credit) }}" step="0.01" min="0">
                        </td>
                        <td>
                            <button type="button" class="glass-btn glass-remove-line neon-btn" onclick="removeLine(this)" title="Supprimer cette ligne">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <button type="button" class="glass-btn glass-add-line neon-btn mt-2" onclick="addLine()" title="Ajouter une ligne">
            <i class="fas fa-plus"></i> Ajouter une ligne
        </button>
    </div>

    <div class="d-flex justify-content-end gap-3 mt-4">
        <button type="submit" class="glass-btn glass-save neon-btn">
            <i class="fas fa-save"></i> Enregistrer
        </button>
        <a href="{{ route('admin.accounting.journal_entries.index') }}" class="glass-btn glass-cancel neon-btn">
            <i class="fas fa-times"></i> Annuler
        </a>
    </div>
</form>

@if(session('success'))
    <div class="glass-alert glass-success mt-3">
        {{ session('success') }}
    </div>
@endif

<script>
function addLine() {
    const tbody = document.getElementById('lines-table-body');
    const idx = tbody.children.length;
    const row = document.createElement('tr');
    row.className = 'glass-row-anim';
    row.innerHTML = `
        <td>
            <select name="lines[${idx}][account_id]" class="glass-input vibrant-input" required>
                @foreach($accounts as $account)
                    <option value="{{ $account->id }}">{{ $account->code }} - {{ $account->name }}</option>
                @endforeach
            </select>
        </td>
        <td><input type="number" name="lines[${idx}][debit]" class="glass-input vibrant-input text-end" step="0.01" min="0"></td>
        <td><input type="number" name="lines[${idx}][credit]" class="glass-input vibrant-input text-end" step="0.01" min="0"></td>
        <td>
            <button type="button" class="glass-btn glass-remove-line neon-btn" onclick="removeLine(this)" title="Supprimer cette ligne">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
}
function removeLine(btn) {
    btn.closest('tr').remove();
}
</script>
@endsection
