<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $defaultSettings = [
            'site_name' => 'OREMI FRIGO',
            'site_description' => 'Système de gestion pour OREMI FRIGO',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+237 000 000 000',
            'address' => 'Douala, Cameroun',
            'timezone' => 'Africa/Douala',
            'date_format' => 'd/m/Y H:i',
            'per_page' => 15,
            'logo' => null,
            'favicon' => null,
            'footer_text' => '© ' . date('Y') . ' OREMI FRIGO. Tous droits réservés.',
            'social_links' => [
                'facebook' => '',
                'twitter' => '',
                'instagram' => '',
                'linkedin' => '',
            ],
            'maintenance_mode' => false,
        ];

        Setting::set('app_settings', $defaultSettings);
    }
}
