@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Comptabilité</h2>
                <div class="flex space-x-4">
                    <a href="{{ route('admin.accounting.revenue') }}" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                        <i class="fas fa-chart-line mr-2"></i>Revenus
                    </a>
                    <a href="{{ route('admin.accounting.expenses.index') }}" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>Dépenses
                    </a>
                    <a href="{{ route('admin.accounting.accounts.index') }}" class="px-4 py-2 bg-white text-green-600 rounded-lg hover:bg-green-50 transition-colors font-bold border border-green-600">
                        <i class="fas fa-book mr-2"></i>Plan comptable
                    </a>
                    <a href="{{ route('admin.accounting.journal_entries.index') }}" class="px-4 py-2 bg-white text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors font-bold border border-indigo-600">
                        <i class="fas fa-book-open mr-2"></i>Journal des écritures
                    </a>
                    <a href="{{ route('admin.accounting.taxes.index') }}" class="px-4 py-2 bg-white text-yellow-700 rounded-lg hover:bg-yellow-50 transition-colors font-bold border border-yellow-500">
                        <i class="fas fa-percentage mr-2"></i>Gestion des Taxes
                    </a>
                    <a href="{{ route('admin.accounting.ledger') }}" class="px-4 py-2 bg-white text-purple-700 rounded-lg hover:bg-purple-50 transition-colors font-bold border border-purple-500">
                        <i class="fas fa-list-ol mr-2"></i>Grand Livre
                    </a>
                    <a href="{{ route('admin.accounting.balance') }}" class="px-4 py-2 bg-white text-orange-700 rounded-lg hover:bg-orange-50 transition-colors font-bold border border-orange-500">
                        <i class="fas fa-balance-scale mr-2"></i>Balance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-end mb-6">
        <a href="{{ route('admin.accounting.accounts.index') }}" class="inline-flex items-center px-6 py-3 rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 text-white font-bold shadow-lg hover:from-blue-600 hover:to-blue-800 transition-all text-lg">
            <i class="fas fa-sitemap mr-2"></i> Accéder à l’arbre du Plan Comptable
        </a>
    </div>

    <!-- Cartes des statistiques -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Revenus -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Revenus du mois</p>
                        <p class="text-2xl font-bold text-green-600">{{ number_format($totalRevenue, 2, ',', ' ') }} FCFA</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-arrow-up text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dépenses -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Dépenses du mois</p>
                        <p class="text-2xl font-bold text-red-600">{{ number_format($totalExpenses, 2, ',', ' ') }} FCFA</p>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-arrow-down text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Balance du mois</p>
                        <p class="text-2xl font-bold {{ $balance >= 0 ? 'text-blue-600' : 'text-red-600' }}">
                            {{ number_format($balance, 2, ',', ' ') }} FCFA
                        </p>
                    </div>
                    <div class="p-3 {{ $balance >= 0 ? 'bg-blue-100' : 'bg-red-100' }} rounded-full">
                        <i class="fas fa-balance-scale {{ $balance >= 0 ? 'text-blue-600' : 'text-red-600' }} text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique des statistiques mensuelles -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Statistiques mensuelles</h3>
            <canvas id="monthlyStats" class="w-full" height="300"></canvas>
        </div>
    </div>

    <div id="account-tree-section">
        <!-- arbre du plan comptable -->
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthlyStats = @json($monthlyStats);
    const months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                   'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    
    const labels = monthlyStats.map(stat => months[stat.month - 1]);
    const revenues = monthlyStats.map(stat => stat.revenue);
    const sales = monthlyStats.map(stat => stat.total_sales);

    new Chart(document.getElementById('monthlyStats'), {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Revenus (FCFA)',
                    data: revenues,
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1,
                    yAxisID: 'y'
                },
                {
                    label: 'Nombre de ventes',
                    data: sales,
                    backgroundColor: 'rgba(16, 185, 129, 0.5)',
                    borderColor: 'rgb(16, 185, 129)',
                    borderWidth: 1,
                    type: 'line',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Revenus (FCFA)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Nombre de ventes'
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
