<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProductImageController;
use App\Http\Controllers\ProductImportExportController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\RecoveryController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\StoreController as AdminStoreController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\SupplyController as AdminSupplyController;
use App\Http\Controllers\Admin\InventoryController as AdminInventoryController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\Admin\SupplierAjaxController;
use App\Http\Controllers\Admin\CustomerAjaxController;
use App\Http\Controllers\AvatarController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::match(['get', 'post'], '/', function () {
    if (request()->isMethod('post')) {
        // Logique de traitement du formulaire
        return (new App\Http\Controllers\Auth\LoginController)->login(request());
    }
    return view('welcome');
});

// Routes d'enregistrement
Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('register', [RegisterController::class, 'register']);

// Routes de réinitialisation de mot de passe
Route::get('password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');

// Route publique pour la liste des boutiques
Route::get('/shops', [ShopController::class, 'index'])->name('shops.index');

// Routes protégées
Route::middleware(['auth'])->group(function () {
    Route::resource('shops', ShopController::class)->except(['index', 'create', 'store', 'edit', 'update', 'destroy'])->names([
        'show' => 'public.shops.show'
    ]);
    
    // Routes de l'inventaire standard
    Route::resource('inventory', InventoryController::class)->names('app.inventory');
});

// Routes du profil utilisateur
Route::middleware(['auth'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/password', [ProfileController::class, 'editPassword'])->name('profile.password');
    Route::patch('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');
    
    // Routes pour la gestion des avatars
    Route::post('/profile/avatar', [AvatarController::class, 'update'])->name('profile.avatar.update');
    Route::delete('/profile/avatar', [AvatarController::class, 'destroy'])->name('profile.avatar.delete');
    Route::get('/profile/avatar', function() {
        return redirect()->route('profile.show');
    });
});

// Routes d'administration
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');
    
    // Gestion des boutiques
    Route::resource('shops', App\Http\Controllers\Admin\ShopController::class)
        ->names([
            'index' => 'admin.shops.index',
            'create' => 'admin.shops.create',
            'store' => 'admin.shops.store',
            'show' => 'admin.shops.show',
            'edit' => 'admin.shops.edit',
            'update' => 'admin.shops.update2',
            'destroy' => 'admin.shops.destroy',
        ]);
    // Gestion des boutiques
    Route::get('/stores', [AdminStoreController::class, 'index'])->name('admin.stores.index');
    Route::get('/stores/create', [AdminStoreController::class, 'create'])->name('admin.stores.create');
    Route::post('/stores', [AdminStoreController::class, 'store'])->name('admin.stores.store');
    Route::get('/stores/{store}', [AdminStoreController::class, 'show'])->name('admin.stores.show');
    Route::get('/stores/{store}/edit', [AdminStoreController::class, 'edit'])->name('admin.stores.edit');
    Route::put('/stores/{store}', [AdminStoreController::class, 'update'])->name('admin.stores.update');
    Route::delete('/stores/{store}', [AdminStoreController::class, 'destroy'])->name('admin.stores.destroy');
    
    // Gestion des utilisateurs
    Route::get('/users', [AdminUserController::class, 'index'])->name('admin.users.index');
    Route::get('/users/create', [AdminUserController::class, 'create'])->name('admin.users.create');
    Route::post('/users', [AdminUserController::class, 'store'])->name('admin.users.store');
    Route::get('/users/{user}', [AdminUserController::class, 'show'])->name('admin.users.show');
    Route::get('/users/{user}/edit', [AdminUserController::class, 'edit'])->name('admin.users.edit');
    Route::put('/users/{user}', [AdminUserController::class, 'update'])->name('admin.users.update');
    Route::delete('/users/{user}', [AdminUserController::class, 'destroy'])->name('admin.users.destroy');
    
    // Gestion des approvisionnements
    Route::get('/supplies/pending', [AdminSupplyController::class, 'pending'])->name('admin.supplies.pending');
    Route::get('/supplies', [AdminSupplyController::class, 'index'])->name('admin.supplies.index');
    Route::get('/supplies/create', [AdminSupplyController::class, 'create'])->name('admin.supplies.create');
    Route::post('/supplies', [AdminSupplyController::class, 'store'])->name('admin.supplies.store');
    Route::get('/supplies/{supply}', [AdminSupplyController::class, 'show'])->name('admin.supplies.show');
    Route::get('/supplies/{supply}/edit', [AdminSupplyController::class, 'edit'])->name('admin.supplies.edit');
    Route::put('/supplies/{supply}', [AdminSupplyController::class, 'update'])->name('admin.supplies.update');
    Route::delete('/supplies/{supply}', [AdminSupplyController::class, 'destroy'])->name('admin.supplies.destroy');
    
    // Gestion des inventaires
    Route::get('/inventories/pending', [AdminInventoryController::class, 'pending'])->name('admin.inventories.pending');
    Route::get('/inventories', [AdminInventoryController::class, 'index'])->name('admin.inventories.index');
    Route::get('/inventories/create', [AdminInventoryController::class, 'create'])->name('admin.inventories.create');
    Route::post('/inventories', [AdminInventoryController::class, 'store'])->name('admin.inventories.store');
    Route::get('/inventories/{inventory}', [AdminInventoryController::class, 'show'])->name('admin.inventories.show');
    Route::get('/inventories/{inventory}/edit', [AdminInventoryController::class, 'edit'])->name('admin.inventories.edit');
    Route::put('/inventories/{inventory}', [AdminInventoryController::class, 'update'])->name('admin.inventories.update');
    Route::delete('/inventories/{inventory}', [AdminInventoryController::class, 'destroy'])->name('admin.inventories.destroy');
    
    // Gestion des stocks
    Route::resource('stocks', App\Http\Controllers\Admin\StockController::class);
    
    // Comptabilité
    Route::prefix('accounting')->group(function () {
        // Écritures comptables
        Route::resource('journal_entries', App\Http\Controllers\Admin\JournalEntryController::class)
            ->names([
                'index' => 'admin.accounting.journal_entries.index',
                'create' => 'admin.accounting.journal_entries.create',
                'store' => 'admin.accounting.journal_entries.store',
                'show' => 'admin.accounting.journal_entries.show',
                'edit' => 'admin.accounting.journal_entries.edit',
                'update' => 'admin.accounting.journal_entries.update',
                'destroy' => 'admin.accounting.journal_entries.destroy',
            ]);
        
        // Taxes
        Route::resource('taxes', App\Http\Controllers\Admin\Accounting\TaxController::class)
            ->names([
                'index' => 'admin.accounting.taxes.index',
                'create' => 'admin.accounting.taxes.create',
                'store' => 'admin.accounting.taxes.store',
                'edit' => 'admin.accounting.taxes.edit',
                'update' => 'admin.accounting.taxes.update',
                'destroy' => 'admin.accounting.taxes.destroy',
                'show' => 'admin.accounting.taxes.show',
            ]);
        
        // Rapports fiscaux
        Route::resource('tax_reports', App\Http\Controllers\Admin\Accounting\TaxReportController::class)
            ->names([
                'index' => 'admin.accounting.tax_reports.index',
                'create' => 'admin.accounting.tax_reports.create',
                'store' => 'admin.accounting.tax_reports.store',
                'show' => 'admin.accounting.tax_reports.show',
                'edit' => 'admin.accounting.tax_reports.edit',
                'update' => 'admin.accounting.tax_reports.update',
                'destroy' => 'admin.accounting.tax_reports.destroy',
            ]);
        
        // Grand Livre
        Route::get('ledger', [App\Http\Controllers\Admin\Accounting\LedgerController::class, 'generalLedger'])->name('admin.accounting.ledger')->middleware('admin');
        // Balance
        Route::get('balance', [App\Http\Controllers\Admin\Accounting\LedgerController::class, 'balance'])->name('admin.accounting.balance')->middleware('admin');
        
        // Dépenses
        // Route::resource('expenses', App\Http\Controllers\Admin\Accounting\ExpenseController::class)
        //     ->names([
        //         'index' => 'admin.accounting.expenses.index',
        //         'create' => 'admin.accounting.expenses.create',
        //         'store' => 'admin.accounting.expenses.store',
        //         'edit' => 'admin.accounting.expenses.edit',
        //         'update' => 'admin.accounting.expenses.update',
        //         'destroy' => 'admin.accounting.expenses.destroy',
        //     ]);
        // Statistiques AJAX pour dépenses
        Route::get('expenses-stats', [App\Http\Controllers\Admin\Accounting\ExpenseController::class, 'stats'])
            ->name('admin.accounting.expenses.stats');
        // Création rapide de catégorie (AJAX)
        Route::post('expenses/category/quick-add', [App\Http\Controllers\Admin\Accounting\ExpenseController::class, 'quickAddCategory'])
            ->name('admin.accounting.expenses.category.quickadd');
        // Exports Excel/PDF
        Route::get('expenses-export-excel', [App\Http\Controllers\Admin\Accounting\ExpenseController::class, 'exportExcel'])->name('admin.accounting.expenses.export.excel');
        Route::get('expenses-export-pdf', [App\Http\Controllers\Admin\Accounting\ExpenseController::class, 'exportPdf'])->name('admin.accounting.expenses.export.pdf');
    });
    
    // Gestion des catégories
    Route::resource('categories', CategoryController::class)
        ->names([
            'index' => 'categories.index',
            'create' => 'categories.create',
            'store' => 'categories.store',
            'show' => 'categories.show',
            'edit' => 'categories.edit',
            'update' => 'categories.update',
            'destroy' => 'categories.destroy',
        ]);
    
    // Gestion des fournisseurs
    Route::resource('suppliers', App\Http\Controllers\Admin\SupplierController::class)->names([
        'index' => 'admin.suppliers.index',
        'create' => 'admin.suppliers.create',
        'store' => 'admin.suppliers.store',
        'show' => 'admin.suppliers.show',
        'edit' => 'admin.suppliers.edit',
        'update' => 'admin.suppliers.update',
        'destroy' => 'admin.suppliers.destroy',
    ]);
    
    // Routes AJAX pour les fournisseurs
    Route::get('/suppliers/search', [SupplierAjaxController::class, 'search'])->name('admin.suppliers.search');
    Route::post('/suppliers/quick-create', [SupplierAjaxController::class, 'quickCreate'])->name('admin.suppliers.quickCreate');
    
    // Gestion des fournisseurs
    Route::post('/suppliers/ajax-create', [SupplierAjaxController::class, 'ajaxCreate'])->name('admin.suppliers.ajax-create');
    
    // Route AJAX pour création rapide de client depuis la vente
    Route::post('/customers/ajax-store', [CustomerAjaxController::class, 'store'])->name('admin.customers.ajax-store');
    
    // Mise à jour du statut d'une vente
    Route::post('/sales/{sale}/update-status', [App\Http\Controllers\Admin\SaleController::class, 'updateStatus'])->name('admin.sales.update-status');
    
    // Affichage de la facture thermique
    Route::get('/sales/{sale}/invoice', [App\Http\Controllers\Admin\SaleController::class, 'invoice'])->name('admin.sales.invoice');
    
    // Alertes (notifications système)
    Route::get('/alerts', [App\Http\Controllers\Admin\AlertController::class, 'index'])->name('admin.alerts.index');
    Route::post('/alerts/mark-as-read', [App\Http\Controllers\Admin\AlertController::class, 'markAsRead'])->name('admin.alerts.markAsRead');
    Route::delete('/alerts/{alert}', [App\Http\Controllers\Admin\AlertController::class, 'destroy'])->name('admin.alerts.destroy');
    Route::post('/alerts/delete-multiple', [App\Http\Controllers\Admin\AlertController::class, 'deleteMultiple'])->name('admin.alerts.deleteMultiple');
    
    // Routes pour le monitoring
    Route::get('/monitoring', [App\Http\Controllers\Admin\MonitoringController::class, 'index'])->name('monitoring.index');
    Route::get('/monitoring/data', [App\Http\Controllers\Admin\MonitoringController::class, 'data'])->name('monitoring.data');
    
    // Routes pour les paramètres
    Route::get('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('settings.clear-cache');

    // Gestion des produits
    Route::resource('products', App\Http\Controllers\ProductController::class)
        ->names([
            'index' => 'admin.products.index',
            'create' => 'admin.products.create',
            'store' => 'admin.products.store',
            'show' => 'admin.products.show',
            'edit' => 'admin.products.edit',
            'update' => 'admin.products.update',
            'destroy' => 'admin.products.destroy',
        ]);

});

Route::post('logout', [LoginController::class, 'logout'])->name('logout');
