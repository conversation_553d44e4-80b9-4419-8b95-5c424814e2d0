<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Affiche la page des paramètres.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $settings = Setting::getAllSettings();
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Met à jour les paramètres.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        // Validation des données
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'timezone' => 'required|string|in:' . implode(',', timezone_identifiers_list()),
            'date_format' => 'required|string|in:d/m/Y H:i,Y-m-d H:i,d-m-Y H:i',
            'per_page' => 'required|integer|min:5|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'favicon' => 'nullable|image|mimes:jpeg,png,jpg,gif,ico|max:1024',
            'footer_text' => 'nullable|string|max:500',
            'social_links.facebook' => 'nullable|url|max:255',
            'social_links.twitter' => 'nullable|url|max:255',
            'social_links.instagram' => 'nullable|url|max:255',
            'social_links.linkedin' => 'nullable|url|max:255',
            'maintenance_mode' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Récupération des paramètres actuels
        $settings = Setting::getAllSettings();

        // Traitement du logo
        if ($request->hasFile('logo')) {
            // Supprimer l'ancien logo s'il existe
            if (!empty($settings['logo'])) {
                Storage::delete($settings['logo']);
            }
            
            // Stocker le nouveau logo
            $logoPath = $request->file('logo')->store('public/settings');
            $settings['logo'] = str_replace('public/', '', $logoPath);
        }

        // Traitement du favicon
        if ($request->hasFile('favicon')) {
            // Supprimer l'ancien favicon s'il existe
            if (!empty($settings['favicon'])) {
                Storage::delete($settings['favicon']);
            }
            
            // Stocker le nouveau favicon
            $faviconPath = $request->file('favicon')->store('public/settings');
            $settings['favicon'] = str_replace('public/', '', $faviconPath);
        }

        // Mise à jour des paramètres textuels
        $settings['site_name'] = $request->input('site_name');
        $settings['site_description'] = $request->input('site_description');
        $settings['contact_email'] = $request->input('contact_email');
        $settings['contact_phone'] = $request->input('contact_phone');
        $settings['address'] = $request->input('address');
        $settings['timezone'] = $request->input('timezone');
        $settings['date_format'] = $request->input('date_format');
        $settings['per_page'] = (int) $request->input('per_page');
        $settings['footer_text'] = $request->input('footer_text');
        $settings['social_links'] = $request->input('social_links', [
            'facebook' => '',
            'twitter' => '',
            'instagram' => '',
            'linkedin' => '',
        ]);
        $settings['maintenance_mode'] = (bool) $request->input('maintenance_mode', false);

        // Enregistrement des paramètres
        Setting::set('app_settings', $settings);

        // Mise à jour du fuseau horaire de l'application
        config(['app.timezone' => $settings['timezone']]);

        // Notification de succès
        return redirect()->route('admin.settings.index')
            ->with('success', 'Les paramètres ont été mis à jour avec succès.');
    }

    /**
     * Vide le cache de l'application.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCache()
    {
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        
        return redirect()->route('admin.settings.index')
            ->with('success', 'Le cache a été vidé avec succès.');
    }
}
