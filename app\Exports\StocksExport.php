<?php

namespace App\Exports;

use App\Models\Stock;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StocksExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    public function query()
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'Boutique',
            'SKU',
            'Produit',
            'Catégorie',
            'Stock actuel',
            'Stock minimum',
            'Valeur unitaire',
            'Valeur totale',
            'Statut du stock',
            'Dernière mise à jour'
        ];
    }

    public function map($stock): array
    {
        return [
            $stock->shop->name,
            $stock->product->sku,
            $stock->product->name,
            $stock->product->category->name,
            $stock->quantity,
            $stock->min_stock,
            $stock->product->unit_price,
            $stock->quantity * $stock->product->unit_price,
            $this->getStockStatus($stock),
            $stock->updated_at->format('d/m/Y H:i')
        ];
    }

    protected function getStockStatus($stock): string
    {
        if ($stock->quantity === 0) {
            return 'Rupture';
        } elseif ($stock->quantity <= $stock->min_stock) {
            return 'Stock bas';
        } else {
            return 'Normal';
        }
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'E:H' => ['numberFormat' => ['formatCode' => '#,##0']],
        ];
    }
}
