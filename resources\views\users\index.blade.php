@extends('layouts.admin')

@section('content')
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800 flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-white">Gestion des Utilisateurs</h2>
            @can('create users')
            <a href="{{ route('users.create') }}" 
               class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>Nouvel Utilisateur
            </a>
            @endcan
        </div>

        <!-- Filtres -->
        <div class="p-4 bg-gray-50" x-data="{ showFilters: false }">
            <button @click="showFilters = !showFilters" 
                    class="flex items-center text-gray-600 hover:text-blue-600 focus:outline-none">
                <i class="fas fa-filter mr-2"></i>
                <span>Filtres</span>
                <i class="fas" :class="showFilters ? 'fa-chevron-up ml-2' : 'fa-chevron-down ml-2'"></i>
            </button>

            <div x-show="showFilters" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Rôle</label>
                    <select id="role-filter" class="form-select rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">Tous les rôles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->name }}">{{ ucfirst($role->name) }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Boutique</label>
                    <select id="shop-filter" class="form-select rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}">{{ $shop->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex flex-col">
                    <label class="mb-1 text-sm text-gray-600">Statut</label>
                    <select id="status-filter" class="form-select rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">Tous les statuts</option>
                        <option value="1">Actif</option>
                        <option value="0">Inactif</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Liste des utilisateurs -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Photo
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Nom & Contact
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Rôle
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Boutique
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Dernière connexion
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($users as $user)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full object-cover" 
                                         src="{{ $user->profile_photo_url }}" 
                                         alt="{{ $user->name }}">
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                <div class="text-sm text-gray-500">{{ $user->phone }}</div>
                            </td>
                            <td class="px-6 py-4">
                                @foreach($user->roles as $role)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        @if($role->name === 'admin')
                                            bg-red-100 text-red-800
                                        @elseif($role->name === 'manager')
                                            bg-blue-100 text-blue-800
                                        @elseif($role->name === 'cashier')
                                            bg-green-100 text-green-800
                                        @elseif($role->name === 'storekeeper')
                                            bg-yellow-100 text-yellow-800
                                        @elseif($role->name === 'accountant')
                                            bg-purple-100 text-purple-800
                                        @else
                                            bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($role->name) }}
                                    </span>
                                @endforeach
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $user->shop->name ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Jamais' }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('users.show', $user) }}" 
                                       class="text-blue-600 hover:text-blue-900" 
                                       title="Voir">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    @can('edit users')
                                    <a href="{{ route('users.edit', $user) }}" 
                                       class="text-yellow-600 hover:text-yellow-900"
                                       title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endcan

                                    @can('delete users')
                                    <form id="delete-form-{{ $user->id }}" 
                                          action="{{ route('users.destroy', $user) }}" 
                                          method="POST" 
                                          class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" 
                                                onclick="confirmDelete('delete-form-{{ $user->id }}')"
                                                class="text-red-600 hover:text-red-900"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endcan

                                    @can('edit users')
                                    <form action="{{ route('users.toggle-status', $user) }}" 
                                          method="POST" 
                                          class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                class="{{ $user->is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' }}"
                                                title="{{ $user->is_active ? 'Désactiver' : 'Activer' }}">
                                            <i class="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }}"></i>
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50">
            {{ $users->links() }}
        </div>
    </div>

    @push('scripts')
    <script>
        // Filtrage dynamique des utilisateurs
        document.addEventListener('DOMContentLoaded', function() {
            const roleFilter = document.getElementById('role-filter');
            const shopFilter = document.getElementById('shop-filter');
            const statusFilter = document.getElementById('status-filter');

            const filters = [roleFilter, shopFilter, statusFilter];

            filters.forEach(filter => {
                filter.addEventListener('change', function() {
                    applyFilters();
                });
            });

            function applyFilters() {
                const url = new URL(window.location.href);
                
                const role = roleFilter.value;
                const shop = shopFilter.value;
                const status = statusFilter.value;

                if (role) url.searchParams.set('role', role);
                else url.searchParams.delete('role');

                if (shop) url.searchParams.set('shop', shop);
                else url.searchParams.delete('shop');

                if (status) url.searchParams.set('status', status);
                else url.searchParams.delete('status');

                window.location.href = url.toString();
            }

            // Set initial filter values from URL
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('role')) roleFilter.value = urlParams.get('role');
            if (urlParams.has('shop')) shopFilter.value = urlParams.get('shop');
            if (urlParams.has('status')) statusFilter.value = urlParams.get('status');
        });
    </script>
    @endpush
@endsection
