<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Account;
use App\Models\JournalEntryLine;
use App\Models\JournalEntry;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LedgerSeeder extends Seeder
{
    public function run()
    {
        DB::transaction(function () {
            // Création de quelques comptes de base
            $accounts = [
                ['code' => '512', 'name' => 'Banque', 'type' => 'actif'],
                ['code' => '530', 'name' => 'Caisse', 'type' => 'actif'],
                ['code' => '401', 'name' => 'Fournisseurs', 'type' => 'passif'],
                ['code' => '411', 'name' => 'Clients', 'type' => 'actif'],
                ['code' => '606', 'name' => 'Achats', 'type' => 'charge'],
                ['code' => '706', 'name' => 'Ventes', 'type' => 'produit'],
                ['code' => '44566', 'name' => 'TVA déductible', 'type' => 'actif'],
                ['code' => '44571', 'name' => 'TVA collectée', 'type' => 'passif'],
                ['code' => '641', 'name' => 'Salaires', 'type' => 'charge'],
                ['code' => '681', 'name' => 'Dotations aux amortissements', 'type' => 'charge'],
                ['code' => '218', 'name' => 'Matériel', 'type' => 'actif'],
                ['code' => '281', 'name' => 'Amortissements', 'type' => 'passif'],
            ];
            $accountIds = [];
            foreach ($accounts as $acc) {
                $account = Account::firstOrCreate([
                    'code' => $acc['code'],
                ], [
                    'name' => $acc['name'],
                    'type' => $acc['type'],
                ]);
                $accountIds[$acc['code']] = $account->id;
            }
            // Création de quelques écritures (journal_entries)
            $journal1 = JournalEntry::create([
                'date' => Carbon::now()->subDays(10),
                'reference' => 'FAC-001',
                'description' => 'Vente à client',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal1->id,
                'account_id' => $accountIds['411'],
                'debit' => 1200,
                'credit' => 0,
                'label' => 'Facture client',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal1->id,
                'account_id' => $accountIds['706'],
                'debit' => 0,
                'credit' => 1200,
                'label' => 'Vente de marchandises',
            ]);

            $journal2 = JournalEntry::create([
                'date' => Carbon::now()->subDays(7),
                'reference' => 'ACH-001',
                'description' => 'Achat fournisseur',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal2->id,
                'account_id' => $accountIds['606'],
                'debit' => 500,
                'credit' => 0,
                'label' => 'Achat de fournitures',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal2->id,
                'account_id' => $accountIds['401'],
                'debit' => 0,
                'credit' => 500,
                'label' => 'Dette fournisseur',
            ]);

            $journal3 = JournalEntry::create([
                'date' => Carbon::now()->subDays(3),
                'reference' => 'REG-001',
                'description' => 'Règlement fournisseur',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal3->id,
                'account_id' => $accountIds['401'],
                'debit' => 500,
                'credit' => 0,
                'label' => 'Paiement fournisseur',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal3->id,
                'account_id' => $accountIds['512'],
                'debit' => 0,
                'credit' => 500,
                'label' => 'Sortie banque',
            ]);

            // Vente avec TVA
            $journal4 = JournalEntry::create([
                'date' => Carbon::now()->subDays(20),
                'reference' => 'FAC-002',
                'description' => 'Vente avec TVA',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal4->id,
                'account_id' => $accountIds['411'],
                'debit' => 2400,
                'credit' => 0,
                'label' => 'Facture client TTC',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal4->id,
                'account_id' => $accountIds['706'],
                'debit' => 0,
                'credit' => 2000,
                'label' => 'Vente HT',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal4->id,
                'account_id' => $accountIds['44571'],
                'debit' => 0,
                'credit' => 400,
                'label' => 'TVA collectée',
            ]);

            // Achat avec TVA
            $journal5 = JournalEntry::create([
                'date' => Carbon::now()->subDays(18),
                'reference' => 'ACH-002',
                'description' => 'Achat avec TVA',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal5->id,
                'account_id' => $accountIds['606'],
                'debit' => 1200,
                'credit' => 0,
                'label' => 'Achat HT',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal5->id,
                'account_id' => $accountIds['44566'],
                'debit' => 240,
                'credit' => 0,
                'label' => 'TVA déductible',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal5->id,
                'account_id' => $accountIds['401'],
                'debit' => 0,
                'credit' => 1440,
                'label' => 'Dette fournisseur TTC',
            ]);

            // Paiement de salaires
            $journal6 = JournalEntry::create([
                'date' => Carbon::now()->subDays(15),
                'reference' => 'SAL-001',
                'description' => 'Paiement salaires',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal6->id,
                'account_id' => $accountIds['641'],
                'debit' => 3000,
                'credit' => 0,
                'label' => 'Salaire brut',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal6->id,
                'account_id' => $accountIds['512'],
                'debit' => 0,
                'credit' => 3000,
                'label' => 'Paiement banque',
            ]);

            // Achat de matériel (immobilisation)
            $journal7 = JournalEntry::create([
                'date' => Carbon::now()->subDays(12),
                'reference' => 'INV-001',
                'description' => 'Achat matériel',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal7->id,
                'account_id' => $accountIds['218'],
                'debit' => 5000,
                'credit' => 0,
                'label' => 'Achat matériel',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal7->id,
                'account_id' => $accountIds['401'],
                'debit' => 0,
                'credit' => 5000,
                'label' => 'Dette fournisseur',
            ]);

            // Dotation aux amortissements
            $journal8 = JournalEntry::create([
                'date' => Carbon::now()->subDays(5),
                'reference' => 'AMT-001',
                'description' => 'Dotation aux amortissements',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal8->id,
                'account_id' => $accountIds['681'], // 681 Dotations aux amortissements (charge)
                'debit' => 400,
                'credit' => 0,
                'label' => 'Dotation',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal8->id,
                'account_id' => $accountIds['281'],
                'debit' => 0,
                'credit' => 400,
                'label' => 'Amortissement matériel',
            ]);

            // Transfert Banque → Caisse
            $journal9 = JournalEntry::create([
                'date' => Carbon::now()->subDays(2),
                'reference' => 'TRF-001',
                'description' => 'Transfert Banque vers Caisse',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal9->id,
                'account_id' => $accountIds['530'],
                'debit' => 300,
                'credit' => 0,
                'label' => 'Entrée caisse',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal9->id,
                'account_id' => $accountIds['512'],
                'debit' => 0,
                'credit' => 300,
                'label' => 'Sortie banque',
            ]);

            // Règlement partiel client
            $journal10 = JournalEntry::create([
                'date' => Carbon::now()->subDays(1),
                'reference' => 'REG-002',
                'description' => 'Règlement partiel client',
                'created_by' => 1,
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal10->id,
                'account_id' => $accountIds['512'],
                'debit' => 800,
                'credit' => 0,
                'label' => 'Encaissement banque',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $journal10->id,
                'account_id' => $accountIds['411'],
                'debit' => 0,
                'credit' => 800,
                'label' => 'Règlement client',
            ]);
        });
    }
}
