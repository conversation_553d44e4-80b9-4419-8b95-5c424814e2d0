@extends('layouts.app')

@section('title', 'Mouvements de stock')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Mouvements de stock</h1>
        <div>
            @can('create', App\Models\StockMovement::class)
            <a href="{{ route('stock.movements.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau mouvement
            </a>
            <a href="{{ route('stock.movements.transfer') }}" class="btn btn-info">
                <i class="fas fa-exchange-alt"></i> Transfert entre boutiques
            </a>
            @endcan
        </div>
    </div>

    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('stock.movements.index') }}" method="GET" class="row">
                <div class="col-md-3 mb-3">
                    <label for="shop_id">Boutique</label>
                    <select class="form-control" id="shop_id" name="shop_id">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}" 
                                    {{ request('shop_id') == $shop->id ? 'selected' : '' }}>
                                {{ $shop->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="product_id">Produit</label>
                    <select class="form-control" id="product_id" name="product_id">
                        <option value="">Tous les produits</option>
                        @foreach($products as $product)
                            <option value="{{ $product->id }}" 
                                    {{ request('product_id') == $product->id ? 'selected' : '' }}>
                                {{ $product->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2 mb-3">
                    <label for="type">Type de mouvement</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">Tous les types</option>
                        <option value="in" {{ request('type') == 'in' ? 'selected' : '' }}>Entrée</option>
                        <option value="out" {{ request('type') == 'out' ? 'selected' : '' }}>Sortie</option>
                        <option value="adjustment" {{ request('type') == 'adjustment' ? 'selected' : '' }}>Ajustement</option>
                        <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>Transfert</option>
                    </select>
                </div>

                <div class="col-md-2 mb-3">
                    <label for="date_start">Date début</label>
                    <input type="date" class="form-control" id="date_start" name="date_start" 
                           value="{{ request('date_start') }}">
                </div>

                <div class="col-md-2 mb-3">
                    <label for="date_end">Date fin</label>
                    <input type="date" class="form-control" id="date_end" name="date_end" 
                           value="{{ request('date_end') }}">
                </div>

                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Filtrer
                    </button>
                    <a href="{{ route('stock.movements.index') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des mouvements -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Produit</th>
                            <th>Boutique</th>
                            <th>Type</th>
                            <th>Quantité</th>
                            <th>Stock final</th>
                            <th>Utilisateur</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($movements as $movement)
                        <tr>
                            <td>{{ $movement->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <a href="{{ route('products.show', $movement->stock->product) }}">
                                    {{ $movement->stock->product->name }}
                                </a>
                            </td>
                            <td>{{ $movement->stock->shop->name }}</td>
                            <td>
                                @php
                                    $badgeClass = match($movement->type) {
                                        'in' => 'success',
                                        'out' => 'danger',
                                        'adjustment' => 'warning',
                                        'transfer' => 'info',
                                        default => 'secondary'
                                    };
                                    $typeLabel = match($movement->type) {
                                        'in' => 'Entrée',
                                        'out' => 'Sortie',
                                        'adjustment' => 'Ajustement',
                                        'transfer' => 'Transfert',
                                        default => 'Inconnu'
                                    };
                                @endphp
                                <span class="badge badge-{{ $badgeClass }}">
                                    {{ $typeLabel }}
                                </span>
                            </td>
                            <td class="text-right">
                                @if($movement->quantity > 0)
                                    <span class="text-success">+{{ $movement->quantity }}</span>
                                @else
                                    <span class="text-danger">{{ $movement->quantity }}</span>
                                @endif
                            </td>
                            <td class="text-right">{{ $movement->final_quantity }}</td>
                            <td>{{ $movement->user->name }}</td>
                            <td>{{ $movement->description }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center">Aucun mouvement de stock trouvé</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-end">
                {{ $movements->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialiser Select2 pour les longs selects
    $('#product_id, #shop_id').select2({
        theme: 'bootstrap4',
        placeholder: 'Sélectionner...',
        allowClear: true
    });
});
</script>
@endpush
