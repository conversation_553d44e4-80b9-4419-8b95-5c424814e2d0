<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\CashRegister;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        $shop = Auth::user()->shop;
        $today = Carbon::today();

        // Vérifier si la caisse est ouverte
        $currentRegister = CashRegister::where('shop_id', $shop->id)
            ->where('user_id', Auth::id())
            ->whereNull('closed_at')
            ->first();

        // Statistiques de la caisse
        $stats = [
            'total_sales' => Payment::where('shop_id', $shop->id)
                                ->whereDate('created_at', $today)
                                ->where('status', 'completed')
                                ->count(),
            'cash_amount' => Payment::where('shop_id', $shop->id)
                                ->whereDate('created_at', $today)
                                ->where('payment_method', 'cash')
                                ->where('status', 'completed')
                                ->sum('amount'),
            'check_amount' => Payment::where('shop_id', $shop->id)
                                ->whereDate('created_at', $today)
                                ->where('payment_method', 'check')
                                ->where('status', 'completed')
                                ->sum('amount'),
            'pending_payments' => Sale::where('shop_id', $shop->id)
                                ->where('payment_status', 'pending')
                                ->count()
        ];

        // Derniers encaissements
        $recentPayments = Payment::with(['sale.customer'])
            ->where('shop_id', $shop->id)
            ->whereDate('created_at', $today)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Factures en attente de paiement
        $pendingSales = Sale::with('customer')
            ->where('shop_id', $shop->id)
            ->where('payment_status', 'pending')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Mouvements de caisse du jour
        $cashMovements = CashRegister::where('shop_id', $shop->id)
            ->whereDate('created_at', $today)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('cashier.dashboard', compact(
            'currentRegister',
            'stats',
            'recentPayments',
            'pendingSales',
            'cashMovements'
        ));
    }

    public function openRegister(Request $request)
    {
        $request->validate([
            'opening_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        // Vérifier qu'il n'y a pas déjà une caisse ouverte
        $existingRegister = CashRegister::where('shop_id', Auth::user()->shop_id)
            ->where('user_id', Auth::id())
            ->whereNull('closed_at')
            ->first();

        if ($existingRegister) {
            return back()->with('error', 'Une caisse est déjà ouverte.');
        }

        // Créer une nouvelle session de caisse
        CashRegister::create([
            'shop_id' => Auth::user()->shop_id,
            'user_id' => Auth::id(),
            'opening_amount' => $request->opening_amount,
            'opening_time' => now(),
            'notes' => $request->notes
        ]);

        return redirect()->route('cashier.dashboard')
            ->with('success', 'Caisse ouverte avec succès.');
    }

    public function closeRegister(Request $request)
    {
        $request->validate([
            'closing_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        $register = CashRegister::where('shop_id', Auth::user()->shop_id)
            ->where('user_id', Auth::id())
            ->whereNull('closed_at')
            ->firstOrFail();

        // Calculer le montant théorique
        $theoreticalAmount = $register->opening_amount + 
            Payment::where('shop_id', Auth::user()->shop_id)
                ->where('created_at', '>=', $register->opening_time)
                ->where('payment_method', 'cash')
                ->where('status', 'completed')
                ->sum('amount');

        $register->update([
            'closing_amount' => $request->closing_amount,
            'theoretical_amount' => $theoreticalAmount,
            'difference' => $request->closing_amount - $theoreticalAmount,
            'closed_at' => now(),
            'closing_notes' => $request->notes
        ]);

        return redirect()->route('cashier.dashboard')
            ->with('success', 'Caisse fermée avec succès.');
    }
}
