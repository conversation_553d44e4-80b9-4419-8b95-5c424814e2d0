@extends('layouts.admin')

@section('title', 'Gestion des approvisionnements')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-extrabold text-blue-900 flex items-center gap-2">
            <i class="fas fa-truck-loading text-blue-500"></i>
            Gestion des approvisionnements
        </h1>
        <a href="{{ route('admin.stocks.create') }}"
           class="inline-flex items-center px-5 py-2 bg-gradient-to-r from-blue-600 to-blue-400 text-white rounded-lg shadow hover:scale-105 transition-transform font-semibold">
            <i class="fas fa-plus mr-2"></i> Nouvel approvisionnement
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    <div class="mt-6 bg-white rounded-xl shadow-lg overflow-x-auto">
        <table class="min-w-full divide-y divide-blue-200">
            <thead class="bg-blue-50 sticky top-0 z-10">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Fournisseur</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Boutique</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Note</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Produits</th>
                    <th class="px-6 py-3 text-center text-xs font-bold text-blue-700 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-blue-100">
                @forelse($stocks as $stock)
                <tr class="hover:bg-blue-50 transition">
                    <td class="px-6 py-4 text-sm text-gray-900 font-semibold">
                        <i class="fas fa-calendar-alt text-blue-400 mr-1"></i>
                        {{ $stock->supply_date ? $stock->supply_date->format('d/m/Y') : '-' }}
                    </td>
                    <td class="px-6 py-4">
                        <span class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-bold shadow">
                            {{ $stock->supplier ? $stock->supplier->name : '-' }}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="inline-block px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-xs font-bold shadow">
                            {{ $stock->shop ? $stock->shop->name : '-' }}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        @if($stock->notes)
                            <span class="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                                <i class="fas fa-sticky-note mr-1"></i> {{ Str::limit($stock->notes, 30) }}
                            </span>
                        @else
                            <span class="text-gray-400 italic">-</span>
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex flex-wrap gap-2">
                            @foreach($stock->details as $detail)
                                <span class="inline-flex items-center px-2 py-1 bg-blue-200 text-blue-900 rounded-full text-xs font-semibold shadow">
                                    <i class="fas fa-box-open mr-1"></i>
                                    {{ $detail->product->name }}
                                    <span class="ml-2 font-bold">{{ $detail->quantity }} {{ $detail->unit }}</span>
                                    <span class="ml-2 text-blue-700">({{ number_format($detail->unit_price, 0, ',', ' ') }} FCFA)</span>
                                </span>
                            @endforeach
                        </div>
                    </td>
                    <td class="px-6 py-4 text-center">
                        <div class="flex justify-center gap-2">
                            <a href="{{ route('admin.stocks.show', $stock) }}"
                               class="inline-flex items-center justify-center w-9 h-9 bg-green-100 hover:bg-green-200 text-green-700 rounded-full shadow transition"
                               title="Détail">
                                <i class="fas fa-eye"></i>
                            </a>
                            <form action="{{ route('admin.stocks.destroy', $stock) }}" method="POST" class="inline-block delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="button"
                                        class="inline-flex items-center justify-center w-9 h-9 bg-red-100 hover:bg-red-200 text-red-700 rounded-full shadow transition delete-btn"
                                        title="Supprimer">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center text-gray-400">
                        <i class="fas fa-box-open fa-2x mb-2"></i>
                        <div>Aucun approvisionnement trouvé.</div>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
        <div class="mt-6">
            {{ $stocks->links() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.delete-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const form = btn.closest('form');
            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Cette action est irréversible !",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, supprimer',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });
});
</script>
@endpush
