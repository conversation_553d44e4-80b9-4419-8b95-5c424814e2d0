<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\Request;
use App\Http\Requests\SupplierRequest;
use Illuminate\Support\Facades\DB;

class SupplierController extends Controller
{
    /**
     * Afficher la liste des fournisseurs
     */
    public function index()
    {
        $suppliers = Supplier::with('supplies')
            ->withCount('products')
            ->paginate(15);

        return view('suppliers.index', compact('suppliers'));
    }

    /**
     * Afficher le formulaire de création d'un fournisseur
     */
    public function create()
    {
        return view('suppliers.create');
    }

    /**
     * Enregistrer un nouveau fournisseur
     */
    public function store(SupplierRequest $request)
    {
        try {
            DB::beginTransaction();

            $supplier = Supplier::create($request->validated());

            if ($request->has('products')) {
                $supplier->products()->attach($request->products);
            }

            DB::commit();

            return redirect()
                ->route('suppliers.show', $supplier)
                ->with('success', 'Fournisseur créé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la création du fournisseur');
        }
    }

    /**
     * Afficher les détails d'un fournisseur
     */
    public function show(Supplier $supplier)
    {
        $supplier->load(['supplies', 'products']);
        
        return view('suppliers.show', compact('supplier'));
    }

    /**
     * Afficher le formulaire de modification d'un fournisseur
     */
    public function edit(Supplier $supplier)
    {
        $supplier->load('products');
        
        return view('suppliers.edit', compact('supplier'));
    }

    /**
     * Mettre à jour un fournisseur
     */
    public function update(SupplierRequest $request, Supplier $supplier)
    {
        try {
            DB::beginTransaction();

            $supplier->update($request->validated());

            if ($request->has('products')) {
                $supplier->products()->sync($request->products);
            }

            DB::commit();

            return redirect()
                ->route('suppliers.show', $supplier)
                ->with('success', 'Fournisseur mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour du fournisseur');
        }
    }

    /**
     * Supprimer un fournisseur
     */
    public function destroy(Supplier $supplier)
    {
        try {
            if ($supplier->supplies()->exists()) {
                return back()->with('error', 'Ce fournisseur ne peut pas être supprimé car il a des approvisionnements associés');
            }

            $supplier->delete();

            return redirect()
                ->route('suppliers.index')
                ->with('success', 'Fournisseur supprimé avec succès');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la suppression du fournisseur');
        }
    }
}
