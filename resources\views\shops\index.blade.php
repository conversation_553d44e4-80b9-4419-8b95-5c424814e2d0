@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Liste des Boutiques</h5>
                    @can('create shops')
                    <a href="{{ route('shops.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle Boutique
                    </a>
                    @endcan
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Boutique Parent</th>
                                    <th>Téléphone</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($shops as $shop)
                                <tr>
                                    <td>{{ $shop->code }}</td>
                                    <td>{{ $shop->name }}</td>
                                    <td>
                                        @if($shop->is_main_shop)
                                            <span class="badge bg-primary">Principale</span>
                                        @else
                                            <span class="badge bg-secondary">Annexe</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $shop->parentShop ? $shop->parentShop->name : '-' }}
                                    </td>
                                    <td>{{ $shop->phone }}</td>
                                    <td>
                                        @if($shop->is_active)
                                            <span class="badge bg-success">Actif</span>
                                        @else
                                            <span class="badge bg-danger">Inactif</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @can('view shops')
                                            <a href="{{ route('shops.show', $shop) }}" 
                                               class="btn btn-info btn-sm"
                                               title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan

                                            @can('edit shops')
                                            <a href="{{ route('shops.edit', $shop) }}" 
                                               class="btn btn-warning btn-sm"
                                               title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan

                                            @can('delete shops')
                                            <form action="{{ route('shops.destroy', $shop) }}" 
                                                  method="POST" 
                                                  class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn btn-danger btn-sm"
                                                        title="Supprimer"
                                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette boutique ?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
