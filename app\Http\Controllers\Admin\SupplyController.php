<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SupplyController extends Controller
{
    public function index() { return view('admin.supplies.index'); }
    public function create() { return view('admin.supplies.create'); }
    public function store(Request $request) { return redirect()->route('admin.supplies.index'); }
    public function show($id) { return view('admin.supplies.show', compact('id')); }
    public function edit($id) { return view('admin.supplies.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.supplies.index'); }
    public function destroy($id) { return redirect()->route('admin.supplies.index'); }
    public function pending() { return view('admin.supplies.pending'); }
}
