<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class CreateAdminUser extends Command
{
    protected $signature = 'make:admin';
    protected $description = 'Create an admin user';

    public function handle()
    {
        $user = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'role' => 'admin',
            'status' => 'active'
        ]);

        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $user->assignRole($adminRole);
        }

        $this->info('Admin user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: admin123');
    }
}
