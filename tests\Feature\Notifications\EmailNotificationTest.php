<?php

namespace Tests\Feature\Notifications;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use App\Models\Sale;
use App\Models\Stock;
use App\Models\Product;
use App\Models\Delivery;
use App\Notifications\UserLoginNotification;
use App\Notifications\DeliveryNotification;
use App\Notifications\StockSupplyNotification;
use App\Notifications\SaleNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Database\Seeders\RolesAndPermissionsSeeder;

class EmailNotificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $manager;
    protected $shop;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Désactiver l'envoi réel d'emails pendant les tests
        Notification::fake();

        // Exécuter le seeder des rôles et permissions
        $this->seed(RolesAndPermissionsSeeder::class);

        // Créer un admin et un gérant pour les tests
        $this->admin = User::factory()->create()->assignRole('admin');
        $this->manager = User::factory()->create()->assignRole('manager');
        
        // Créer une boutique
        $this->shop = Shop::factory()->create();
    }

    /** @test */
    public function it_sends_notification_when_user_logs_in()
    {
        // Créer un utilisateur
        $user = User::factory()
            ->withShop($this->shop)
            ->create();

        // Déclencher manuellement l'événement de connexion
        event(new \App\Events\UserLoggedIn($user));
        
        // Vérifier que la notification a été envoyée
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            UserLoginNotification::class,
            function ($notification) use ($user) {
                return $notification->user->id === $user->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_delivery_is_created()
    {
        // Créer une vente
        $sale = Sale::factory()
            ->withShop($this->shop)
            ->withUser($this->manager)
            ->create();

        // Créer une livraison
        $delivery = Delivery::factory()
            ->withSale($sale)
            ->pending()
            ->create([
                'delivery_date' => now()->addHour(),
                'delivery_address' => $this->faker->address(),
            ]);

        // Vérifier que la notification a été envoyée
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            DeliveryNotification::class,
            function ($notification) use ($delivery) {
                return $notification->delivery->id === $delivery->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_stock_is_supplied()
    {
        // Créer un produit
        $product = Product::factory()->create();

        // Créer un approvisionnement
        $supply = Stock::factory()
            ->supply()
            ->withProduct($product)
            ->withShop($this->shop)
            ->create([
                'quantity' => 100,
                'status' => 'pending'
            ]);

        // Vérifier que la notification a été envoyée
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            StockSupplyNotification::class,
            function ($notification) use ($supply) {
                return $notification->supply->id === $supply->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_sale_is_created()
    {
        // Créer une vente
        $sale = Sale::factory()
            ->withShop($this->shop)
            ->withUser($this->manager)
            ->create([
                'amount' => 50000,
                'status' => 'pending',
                'payment_method' => 'cash',
            ]);

        // Vérifier que la notification a été envoyée
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            SaleNotification::class,
            function ($notification) use ($sale) {
                return $notification->sale->id === $sale->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_inventory_is_completed()
    {
        // Créer un inventaire
        $inventory = Stock::factory()
            ->inventory()
            ->withShop($this->shop)
            ->create([
                'status' => 'completed'
            ]);

        // Vérifier que la notification a été envoyée à l'admin et au gérant
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            InventoryNotification::class,
            function ($notification) use ($inventory) {
                return $notification->inventory->id === $inventory->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_stock_level_is_critical()
    {
        // Créer un produit avec un stock critique
        $product = Product::factory()->create([
            'min_stock' => 10
        ]);

        // Créer un mouvement de stock qui rend le niveau critique
        $stock = Stock::factory()
            ->sale()
            ->withProduct($product)
            ->withShop($this->shop)
            ->create([
                'quantity' => 5,
                'status' => 'pending'
            ]);

        // Vérifier que la notification a été envoyée à l'admin et au gérant
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            StockCriticalNotification::class,
            function ($notification) use ($product) {
                return $notification->product->id === $product->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_when_payment_is_received()
    {
        // Créer une vente
        $sale = Sale::factory()
            ->withShop($this->shop)
            ->withUser($this->manager)
            ->create([
                'status' => 'pending',
                'payment_status' => 'pending'
            ]);

        // Simuler un paiement
        $payment = $sale->payments()->create([
            'amount' => $sale->total_amount,
            'payment_method' => 'cash',
            'status' => 'completed'
        ]);

        // Vérifier que la notification a été envoyée
        Notification::assertSentTo(
            [$this->admin, $this->manager],
            PaymentNotification::class,
            function ($notification) use ($payment) {
                return $notification->payment->id === $payment->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_for_credit_sale_approval()
    {
        // Créer une vente à crédit
        $sale = Sale::factory()
            ->withShop($this->shop)
            ->withUser($this->manager)
            ->create([
                'status' => 'pending',
                'payment_type' => 'credit'
            ]);

        // Vérifier que la notification a été envoyée à l'admin pour approbation
        Notification::assertSentTo(
            $this->admin,
            CreditSaleApprovalNotification::class,
            function ($notification) use ($sale) {
                return $notification->sale->id === $sale->id;
            }
        );
    }

    /** @test */
    public function it_sends_notification_for_check_payment()
    {
        // Créer une vente avec paiement par chèque
        $sale = Sale::factory()
            ->withShop($this->shop)
            ->withUser($this->manager)
            ->create([
                'status' => 'pending',
                'payment_type' => 'check'
            ]);

        $payment = $sale->payments()->create([
            'amount' => $sale->total_amount,
            'payment_method' => 'check',
            'check_number' => $this->faker->numerify('####-####-####'),
            'bank_name' => $this->faker->company,
            'status' => 'pending'
        ]);

        // Vérifier que la notification a été envoyée au comptable
        Notification::assertSentTo(
            User::role('accountant')->first(),
            CheckPaymentNotification::class,
            function ($notification) use ($payment) {
                return $notification->payment->id === $payment->id;
            }
        );
    }
}
