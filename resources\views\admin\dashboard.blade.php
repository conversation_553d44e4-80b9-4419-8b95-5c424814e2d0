@extends('layouts.admin')

@section('content')
<div class="space-y-8 py-6 mb-16">
    <!-- En-tête avec résumé -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 p-6">
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div>
                    <h2 class="text-2xl font-bold text-white">Tableau de bord</h2>
                    <p class="text-blue-100 mt-1">{{ now()->format('d F Y') }} · Bienvenue {{ Auth::user()->name }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.monitoring.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-800 bg-blue-100 hover:bg-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        <i class="fas fa-chart-line mr-2"></i> Monitoring
                    </a>
                    <a href="{{ route('admin.settings.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-700 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        <i class="fas fa-cog mr-2"></i> Paramètres
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Chiffre d'affaires -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-5px]">
            <div class="p-1 bg-gradient-to-r from-blue-500 to-blue-700"></div>
            <div class="p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Chiffre d'Affaires</p>
                        <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ number_format($revenue, 0, ',', ' ') }} FCFA</h3>
                        <div class="mt-2 flex items-center">
                            @if($revenueChange > 0)
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-arrow-up mr-1"></i>{{ $revenueChange }}%
                                </span>
                            @elseif($revenueChange < 0)
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-arrow-down mr-1"></i>{{ abs($revenueChange) }}%
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-minus mr-1"></i>0%
                                </span>
                            @endif
                            <span class="text-xs text-gray-500 ml-2">vs. mois dernier</span>
                        </div>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div class="h-1 bg-blue-600 rounded-full" style="width: {{ min(100, max(5, $revenueChange + 50)) }}%"></div>
                </div>
            </div>
        </div>

        <!-- Stocks critiques -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-5px]">
            <div class="p-1 bg-gradient-to-r from-red-500 to-red-700"></div>
            <div class="p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Stocks Critiques</p>
                        <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ $criticalStocks }} produits</h3>
                        <div class="mt-2 flex items-center">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $criticalStocksPercentage > 10 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $criticalStocksPercentage }}% du stock
                            </span>
                        </div>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-box-open text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div class="h-1 bg-red-600 rounded-full" style="width: {{ min(100, max(5, $criticalStocksPercentage * 3)) }}%"></div>
                </div>
            </div>
        </div>

        <!-- Créances -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-5px]">
            <div class="p-1 bg-gradient-to-r from-yellow-500 to-yellow-700"></div>
            <div class="p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Créances</p>
                        <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ number_format($debts, 0, ',', ' ') }} FCFA</h3>
                        <div class="mt-2 flex items-center">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $debtDays > 30 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $debtDays }} jours en moyenne
                            </span>
                        </div>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-coins text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div class="h-1 bg-yellow-600 rounded-full" style="width: {{ min(100, max(5, $debtDays * 2)) }}%"></div>
                </div>
            </div>
        </div>

        <!-- Utilisateurs -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-5px]">
            <div class="p-1 bg-gradient-to-r from-indigo-500 to-indigo-700"></div>
            <div class="p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Utilisateurs</p>
                        <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ array_sum($userStats) }}</h3>
                        <div class="mt-2 flex items-center">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                <i class="fas fa-circle text-green-500 mr-1 text-xs"></i> {{ $userStats['active'] ?? 0 }} actifs
                            </span>
                        </div>
                    </div>
                    <div class="p-3 bg-indigo-100 rounded-full">
                        <i class="fas fa-users text-indigo-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div class="h-1 bg-indigo-600 rounded-full" style="width: {{ min(100, max(5, ($userStats['active'] ?? 0) / max(1, array_sum($userStats)) * 100)) }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique et Alertes -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Graphique d'évolution -->
        <div class="lg:col-span-2 bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900">Évolution du chiffre d'affaires</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium rounded-md bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors">Semaine</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors">Mois</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors">Année</button>
                    </div>
                </div>
                <div class="w-full h-64 overflow-hidden">
                    <canvas id="revenueChart" class="w-full h-full"></canvas>
                </div>
            </div>
        </div>

        <!-- Alertes et notifications -->
        <div class="space-y-4">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-4 bg-gradient-to-r from-red-500 to-red-700">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i> Alertes
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <div class="flex items-start p-3 bg-red-50 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-red-500 mt-0.5 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-red-800">{{ $criticalStocks }} produits en stock critique</p>
                            <a href="{{ route('admin.products.index') }}?filter=critical" class="text-xs text-red-600 hover:text-red-800 mt-1 inline-block">Voir les produits →</a>
                        </div>
                    </div>
                    <div class="flex items-start p-3 bg-yellow-50 rounded-lg">
                        <i class="fas fa-money-bill-wave text-yellow-500 mt-0.5 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">{{ number_format($debts, 0, ',', ' ') }} FCFA de créances</p>
                            <a href="{{ route('admin.sales.index') }}?filter=pending" class="text-xs text-yellow-600 hover:text-yellow-800 mt-1 inline-block">Gérer les créances →</a>
                        </div>
                    </div>
                    <a href="{{ route('admin.alerts.index') }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 mt-2">
                        <i class="fas fa-bell mr-2"></i> Toutes les alertes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Dernières ventes -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-900">Dernières ventes</h3>
                <a href="{{ route('admin.sales.index') }}" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                    Voir toutes <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($lastSales as $sale)
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                <div class="font-medium">{{ $sale->created_at->format('d/m/Y') }}</div>
                                <div class="text-gray-500">{{ $sale->created_at->format('H:i') }}</div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ $sale->customer_name }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm">
                                @if($sale->payment_status === 'paid')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i> Payée
                                    </span>
                                @elseif($sale->payment_status === 'pending')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i> En attente
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i> Annulée
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ route('admin.sales.show', $sale->id) }}" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.sales.edit', $sale->id) }}" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Accès rapides -->
    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
        <a href="{{ route('admin.stores.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-store text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-all duration-300">Boutiques</span>
        </a>
        <a href="{{ route('admin.users.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-indigo-100 text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-users text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-all duration-300">Utilisateurs</span>
        </a>
        <a href="{{ route('admin.products.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-green-100 text-green-600 group-hover:bg-green-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-boxes text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-green-600 transition-all duration-300">Produits</span>
        </a>
        <a href="{{ route('admin.sales.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 group-hover:bg-yellow-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-receipt text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-yellow-600 transition-all duration-300">Ventes</span>
        </a>
        <a href="{{ route('admin.accounting.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600 group-hover:bg-purple-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-calculator text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-purple-600 transition-all duration-300">Comptabilité</span>
        </a>
        <a href="{{ route('admin.settings.index') }}" class="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center hover:shadow-lg transition-all duration-300 hover:translate-y-[-5px] group">
            <div class="p-3 rounded-full bg-gray-100 text-gray-600 group-hover:bg-gray-600 group-hover:text-white transition-all duration-300">
                <i class="fas fa-cog text-xl"></i>
            </div>
            <span class="mt-2 text-sm font-medium text-gray-700 group-hover:text-gray-600 transition-all duration-300">Paramètres</span>
        </a>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: @json($revenueChartLabels ?? []),
            datasets: [{
                label: 'Chiffre d\'Affaires',
                data: @json($revenueChartData ?? []),
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { 
                legend: { display: false },
                tooltip: {
                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                    titleFont: { weight: 'bold', size: 13 },
                    bodyFont: { size: 12 },
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            let value = context.raw;
                            return value.toLocaleString('fr-FR') + ' FCFA';
                        }
                    }
                }
            },
            scales: { 
                y: { 
                    beginAtZero: true,
                    grid: { color: 'rgba(226, 232, 240, 0.6)' },
                    ticks: {
                        callback: function(value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(0) + 'k';
                            }
                            return value;
                        }
                    }
                },
                x: {
                    grid: { display: false }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                line: {
                    tension: 0.4
                }
            }
        }
    });
</script>
@endpush

@endsection
