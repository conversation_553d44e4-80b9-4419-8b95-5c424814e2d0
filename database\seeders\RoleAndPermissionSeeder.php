<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Boutiques
            'view shops',
            'create shops',
            'edit shops',
            'delete shops',
            // Utilisateurs
            'view users',
            'create users',
            'edit users',
            'delete users',
            // Produits
            'view products',
            'create products',
            'edit products',
            'delete products',
            // Stocks
            'view stock',
            'manage stock',
            // Ventes
            'create sales',
            'view sales',
            'cancel sales',
            // Inventaire
            'view inventory',
            'create inventory',
            'validate inventory',
            // Comptabilité
            'view accounting',
            'manage accounting',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Création des rôles avec leurs permissions
        $roles = [
            'admin' => $permissions,
            'manager' => [
                'view shops', 'view users', 'view products', 'view stock', 'manage stock',
                'view sales', 'create sales', 'cancel sales',
                'view inventory', 'create inventory',
                'view accounting'
            ],
            'cashier' => [
                'view products', 'view stock', 'create sales', 'view sales'
            ],
            'biller' => [
                'view products', 'view stock', 'create sales', 'view sales'
            ],
            'storekeeper' => [
                'view products', 'view stock', 'manage stock',
                'view inventory', 'create inventory'
            ],
            'accountant' => [
                'view sales', 'view accounting', 'manage accounting'
            ]
        ];

        foreach ($roles as $role => $rolePermissions) {
            $createdRole = Role::create(['name' => $role]);
            $createdRole->givePermissionTo($rolePermissions);
        }
    }
}
