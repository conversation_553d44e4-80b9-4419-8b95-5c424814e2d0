<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CategoryTest extends TestCase
{
    use RefreshDatabase;

    private $category;

    protected function setUp(): void
    {
        parent::setUp();

        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'slug' => 'test-category',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_has_many_products()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $this->assertTrue($this->category->products->contains($product));
        $this->assertInstanceOf(Product::class, $this->category->products->first());
    }

    /** @test */
    public function it_generates_slug_from_name()
    {
        $category = Category::factory()->create([
            'name' => 'New Test Category'
        ]);

        $this->assertEquals('new-test-category', $category->slug);
    }

    /** @test */
    public function it_has_unique_slug()
    {
        $category1 = Category::factory()->create(['name' => 'Same Name']);
        $category2 = Category::factory()->create(['name' => 'Same Name']);

        $this->assertNotEquals($category1->slug, $category2->slug);
    }

    /** @test */
    public function it_can_have_parent_category()
    {
        $parentCategory = Category::factory()->create();
        $childCategory = Category::factory()->create([
            'parent_id' => $parentCategory->id
        ]);

        $this->assertInstanceOf(Category::class, $childCategory->parent);
        $this->assertEquals($parentCategory->id, $childCategory->parent->id);
    }

    /** @test */
    public function it_can_have_child_categories()
    {
        $childCategory = Category::factory()->create([
            'parent_id' => $this->category->id
        ]);

        $this->assertTrue($this->category->children->contains($childCategory));
        $this->assertInstanceOf(Category::class, $this->category->children->first());
    }

    /** @test */
    public function it_can_get_all_products_including_child_categories()
    {
        $childCategory = Category::factory()->create([
            'parent_id' => $this->category->id
        ]);

        $parentProduct = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $childProduct = Product::factory()->create([
            'category_id' => $childCategory->id
        ]);

        $allProducts = $this->category->allProducts();

        $this->assertTrue($allProducts->contains($parentProduct));
        $this->assertTrue($allProducts->contains($childProduct));
    }

    /** @test */
    public function it_can_determine_if_it_has_products()
    {
        $this->assertFalse($this->category->hasProducts());

        Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $this->category->refresh();
        $this->assertTrue($this->category->hasProducts());
    }

    /** @test */
    public function it_can_be_searched_by_name()
    {
        $searchResults = Category::search('Test')->get();
        $this->assertTrue($searchResults->contains($this->category));
    }

    /** @test */
    public function it_can_get_active_categories()
    {
        $inactiveCategory = Category::factory()->create([
            'status' => 'inactive'
        ]);

        $activeCategories = Category::active()->get();

        $this->assertTrue($activeCategories->contains($this->category));
        $this->assertFalse($activeCategories->contains($inactiveCategory));
    }

    /** @test */
    public function it_can_get_categories_with_products_count()
    {
        Product::factory()->count(3)->create([
            'category_id' => $this->category->id
        ]);

        $categoryWithCount = Category::withProductsCount()->find($this->category->id);
        $this->assertEquals(3, $categoryWithCount->products_count);
    }
}
