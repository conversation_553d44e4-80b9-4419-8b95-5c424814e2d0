@extends('layouts.app')

@section('title', 'Nouveau Transfert Inter-boutiques')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Nouveau Transfert Inter-boutiques</h1>
        <a href="{{ route('transfers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('transfers.store') }}" method="POST" id="transferForm">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="from_shop_id" class="form-label required">Boutique source</label>
                            <select name="from_shop_id" id="from_shop_id" class="form-select @error('from_shop_id') is-invalid @enderror" required>
                                <option value="">Sélectionner la boutique source</option>
                                @foreach($shops as $shop)
                                    <option value="{{ $shop->id }}" @selected(old('from_shop_id') == $shop->id)
                                            data-is-main="{{ $shop->is_main }}">
                                        {{ $shop->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('from_shop_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="to_shop_id" class="form-label required">Boutique destination</label>
                            <select name="to_shop_id" id="to_shop_id" class="form-select @error('to_shop_id') is-invalid @enderror" required>
                                <option value="">Sélectionner la boutique destination</option>
                                @foreach($shops as $shop)
                                    <option value="{{ $shop->id }}" @selected(old('to_shop_id') == $shop->id)
                                            data-is-main="{{ $shop->is_main }}">
                                        {{ $shop->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('to_shop_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="transfer_date" class="form-label required">Date du transfert</label>
                            <input type="date" class="form-control @error('transfer_date') is-invalid @enderror"
                                   id="transfer_date" name="transfer_date"
                                   value="{{ old('transfer_date', date('Y-m-d')) }}" required>
                            @error('transfer_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                      id="notes" name="notes" rows="4">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <h4 class="mb-3">Produits à transférer</h4>
                <div class="table-responsive mb-4">
                    <table class="table" id="products-table">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Stock disponible</th>
                                <th>Quantité à transférer</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr id="empty-row">
                                <td colspan="4" class="text-center">Aucun produit ajouté</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="mb-4">
                    <button type="button" class="btn btn-outline-primary" id="add-product">
                        <i class="fas fa-plus"></i> Ajouter un produit
                    </button>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Créer le bon de transfert
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'ajout de produit -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="product_id" class="form-label required">Produit</label>
                    <select class="form-select" id="product_id" required>
                        <option value="">Sélectionner un produit</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="quantity" class="form-label required">Quantité à transférer</label>
                    <input type="number" class="form-control" id="quantity" min="1" required>
                    <small class="text-muted">Stock disponible: <span id="available-stock">0</span></small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="add-product-confirm">Ajouter</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        let products = [];
        let rowCount = 0;

        // Initialisation de Select2
        $('#from_shop_id, #to_shop_id').select2({
            theme: 'bootstrap-5'
        });

        // Empêcher la sélection de la même boutique
        $('#to_shop_id').on('change', function() {
            const fromShopId = $('#from_shop_id').val();
            const toShopId = $(this).val();

            if (fromShopId && fromShopId === toShopId) {
                alert('La boutique source et destination ne peuvent pas être identiques');
                $(this).val('').trigger('change');
            }
        });

        // Chargement des produits lors du changement de boutique source
        $('#from_shop_id').on('change', function() {
            const shopId = $(this).val();
            if (shopId) {
                $.get(`/api/shops/${shopId}/products-with-stock`, function(data) {
                    products = data;
                    updateProductSelect();
                });
            }
        });

        // Mise à jour du select des produits
        function updateProductSelect() {
            const $select = $('#product_id');
            $select.empty().append('<option value="">Sélectionner un produit</option>');
            products.forEach(product => {
                if (product.stock > 0) {
                    $select.append(`<option value="${product.id}" data-stock="${product.stock}">
                        ${product.name} (Stock: ${product.stock})
                    </option>`);
                }
            });
        }

        // Événement d'ajout de produit
        $('#add-product').on('click', function() {
            if (!$('#from_shop_id').val()) {
                alert('Veuillez d\'abord sélectionner une boutique source');
                return;
            }
            $('#productModal').modal('show');
        });

        // Mise à jour du stock disponible
        $('#product_id').on('change', function() {
            const option = $(this).find(':selected');
            if (option.val()) {
                const stock = option.data('stock');
                $('#available-stock').text(stock);
                $('#quantity').attr('max', stock);
            }
        });

        // Confirmation d'ajout de produit
        $('#add-product-confirm').on('click', function() {
            const productId = $('#product_id').val();
            const quantity = $('#quantity').val();
            const maxStock = $('#quantity').attr('max');

            if (!productId || !quantity) {
                alert('Tous les champs sont obligatoires');
                return;
            }

            if (parseInt(quantity) > parseInt(maxStock)) {
                alert('La quantité ne peut pas dépasser le stock disponible');
                return;
            }

            const product = products.find(p => p.id == productId);
            addProductRow(product, quantity);
            $('#productModal').modal('hide');
            $('#product_id, #quantity').val('');
        });

        // Ajout d'une ligne de produit
        function addProductRow(product, quantity) {
            $('#empty-row').remove();
            rowCount++;

            const row = `
                <tr>
                    <td>
                        ${product.name}
                        <input type="hidden" name="items[${rowCount}][product_id]" value="${product.id}">
                    </td>
                    <td>${product.stock}</td>
                    <td>
                        <input type="number" class="form-control form-control-sm quantity" 
                               name="items[${rowCount}][quantity]" value="${quantity}" 
                               min="1" max="${product.stock}">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-row">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#products-table tbody').append(row);
        }

        // Suppression d'une ligne
        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
            if ($('#products-table tbody tr').length === 0) {
                $('#products-table tbody').append(`
                    <tr id="empty-row">
                        <td colspan="4" class="text-center">Aucun produit ajouté</td>
                    </tr>
                `);
            }
        });

        // Validation du formulaire
        $('#transferForm').on('submit', function(e) {
            if ($('#products-table tbody tr').length === 0 || $('#empty-row').length > 0) {
                e.preventDefault();
                alert('Veuillez ajouter au moins un produit');
            }
        });
    });
</script>
@endpush

@push('styles')
<style>
    .required::after {
        content: " *";
        color: red;
    }
</style>
@endpush
