<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\User;
use App\Models\Supply;
use App\Models\Inventory;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DashboardController extends Controller
{
    public function index()
    {
        try {
            // Statistiques des utilisateurs
            $userStats = [
                'admins' => User::where('role', 'admin')->count(),
                'managers' => User::where('role', 'manager')->count(),
                'billers' => User::where('role', 'biller')->count(),
                'cashiers' => User::where('role', 'cashier')->count(),
            ];

            // Statistiques financières
            $revenue = 0;
            $revenueChange = 0;
            if (Schema::hasTable('sales')) {
                // Chiffre d'affaires total
                $revenue = Sale::where('status', 'completed')
                    ->whereDate('created_at', '>=', now()->subMonth())
                    ->sum('total_amount');

                // Variation par rapport au mois précédent
                $lastMonthRevenue = Sale::where('status', 'completed')
                    ->whereDate('created_at', '>=', now()->subMonth(2))
                    ->whereDate('created_at', '<', now()->subMonth())
                    ->sum('total_amount');

                if ($lastMonthRevenue > 0) {
                    $revenueChange = (($revenue - $lastMonthRevenue) / $lastMonthRevenue) * 100;
                }
            }

            // Statistiques de stock
            $criticalStocks = 0;
            $criticalStocksPercentage = 0;
            if (Schema::hasTable('products') && Schema::hasTable('stocks')) {
                $totalProducts = Product::count();
                // Compte le nombre de stocks critiques (où la quantité est inférieure ou égale au min_stock)
                $criticalStocks = \App\Models\Stock::whereColumn('quantity', '<=', 'min_stock')->count();
                if ($totalProducts > 0) {
                    $criticalStocksPercentage = ($criticalStocks / $totalProducts) * 100;
                }
            }

            // Statistiques des créances
            $debts = 0;
            $debtDays = 0;
            if (Schema::hasTable('payments')) {
                $pendingPayments = Payment::where('status', 'pending')
                    ->whereDate('due_date', '<=', now())
                    ->get();

                $debts = $pendingPayments->sum('amount');
                if ($pendingPayments->count() > 0) {
                    $totalDays = $pendingPayments->sum(function ($payment) {
                        return now()->diffInDays($payment->due_date);
                    });
                    $debtDays = $totalDays / $pendingPayments->count();
                }
            }

            // Approvisionnements en attente
            $pendingSupplies = 0;
            if (Schema::hasTable('supplies')) {
                $pendingSupplies = Supply::where('status', 'pending')->count();
            }

            // Inventaires en attente
            $pendingInventories = 0;
            if (Schema::hasTable('inventories')) {
                $pendingInventories = Inventory::where('status', 'pending')->count();
            }

            // Alertes
            $alerts = collect();
            
            // Alertes de stock bas
            if (Schema::hasTable('products')) {
                $lowStockProducts = Product::with('shops')
                    ->whereHas('shops', function ($query) {
                        $query->wherePivot('quantity', '<=', DB::raw('min_stock'));
                    })
                    ->get();
                foreach ($lowStockProducts as $product) {
                    foreach ($product->shops as $shop) {
                        if ($shop->pivot->quantity <= $shop->pivot->min_stock) {
                            $alerts->push([
                                'type' => 'warning',
                                'message' => "Stock bas pour {$product->name} dans {$shop->name}"
                            ]);
                        }
                    }
                }
            }

            // Alertes de paiements en attente
            if (Schema::hasTable('payments')) {
                $pendingPayments = Payment::where('status', 'pending')->get();
                foreach ($pendingPayments as $payment) {
                    $alerts->push([
                        'type' => 'info',
                        'message' => "Paiement en attente de {$payment->amount} pour la facture #{$payment->invoice_id}"
                    ]);
                }
            }

            // Liste des boutiques
            $stores = Store::with('sales', 'products')->get();

            // Dernières ventes
            $lastSales = [];
            if (Schema::hasTable('sales')) {
                $lastSales = Sale::with('user')
                    ->latest()
                    ->take(10)
                    ->get();
            }

            return view('admin.dashboard', compact(
                'userStats',
                'pendingSupplies',
                'pendingInventories',
                'alerts',
                'revenue',
                'revenueChange',
                'criticalStocks',
                'criticalStocksPercentage',
                'debts',
                'debtDays',
                'stores',
                'lastSales'
            ));
        } catch (\Exception $e) {
            // Log l'erreur
            \Log::error('Erreur dans le dashboard admin : ' . $e->getMessage());
            
            // Retourne une vue avec des données par défaut
            return view('admin.dashboard', [
                'userStats' => [
                    'admins' => 0,
                    'managers' => 0,
                    'billers' => 0,
                    'cashiers' => 0,
                ],
                'pendingSupplies' => 0,
                'pendingInventories' => 0,
                'alerts' => collect(),
                'revenue' => 0,
                'revenueChange' => 0,
                'criticalStocks' => 0,
                'criticalStocksPercentage' => 0,
                'debts' => 0,
                'debtDays' => 0,
                'stores' => collect(),
                'lastSales' => collect()
            ])->with('error', 'Une erreur est survenue lors du chargement des données.');
        }
    }
}
