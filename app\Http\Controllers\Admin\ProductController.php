<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use App\Models\Shop;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index()
    {
        $products = Product::with(['shop'])
            ->withCount('orders')
            ->latest()
            ->paginate(10);

        return view('admin.products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $shops = Shop::where('is_active', true)->pluck('name', 'id');
        $categories = Category::pluck('name', 'id');
        $suppliers = Supplier::orderBy('name')->pluck('name', 'id');
        return view('admin.products.create', compact('shops', 'categories', 'suppliers'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'min_stock' => 'required|integer|min:0',
            'shop_id' => 'required|exists:shops,id',
            'is_active' => 'required|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category_id' => 'required|exists:categories,id',
            'unit' => 'required|string|max:50',
            'sku' => 'nullable|string|max:50|unique:products,sku',
            'supplier_id' => 'nullable|exists:suppliers,id'
        ]);

        $category = Category::find($validated['category_id']);

        if ($category->name === 'PAGNE') {
            // Logique spécifique pour les PAGNE
            $supplier = Supplier::find($request->supplier_id);
            $supplierPrefix = $supplier ? substr($supplier->name, 0, 5) : '';
            $datePart = now()->format('Ymd');
            $nextId = Product::max('id') + 1;
            
            $validated['sku'] = strtoupper(
                Str::slug($validated['name']) 
                . '_' . $supplierPrefix 
                . '_' . $datePart 
                . 'Z' 
                . $nextId
            );
            
            // Ne pas enregistrer le fournisseur pour les PAGNE
            unset($validated['supplier_id']);
        } else if (empty($validated['sku'])) {
            // Génération standard du SKU
            $initiales = collect(explode(' ', $validated['name']))
                ->filter(fn($mot) => strlen($mot) > 0)
                ->map(fn($mot) => strtoupper(mb_substr($mot, 0, 1)))
                ->implode('');

            $catId = $validated['category_id'] ?? '';
            $date = now()->format('dmY');
            $validated['sku'] = $initiales . $catId . $date;
        }

        // Génération du slug unique
        $slug = Str::slug($validated['name']);
        $count = Product::where('slug', $slug)->count();
        if ($count > 0) {
            $slug = $slug . '-' . ($count + 1);
        }
        $validated['slug'] = $slug;

        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('products', 'public');
        }

        $product = Product::create($validated);

        activity()
            ->causedBy(auth()->user())
            ->performedOn($product)
            ->log('product_created');

        return redirect()
            ->route('admin.products.index')
            ->with('success', 'Produit créé avec succès.');
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product)
    {
        $product->load(['shop', 'orders']);
        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        $shops = Shop::where('is_active', true)->pluck('name', 'id');
        $categories = Category::where('is_active', true)->pluck('name', 'id');
        return view('admin.products.edit', compact('product', 'shops', 'categories'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'shop_id' => 'required|exists:shops,id',
            'is_active' => 'required|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'nullable|string|max:50|unique:products,sku,' . $product->id
        ]);

        if ($request->hasFile('image')) {
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            $validated['image'] = $request->file('image')->store('products', 'public');
        }

        $product->update($validated);

        activity()
            ->causedBy(auth()->user())
            ->performedOn($product)
            ->log('product_updated');

        return redirect()
            ->route('admin.products.index')
            ->with('success', 'Produit mis à jour avec succès.');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product)
    {
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        $product->delete();

        activity()
            ->causedBy(auth()->user())
            ->performedOn($product)
            ->log('product_deleted');

        return redirect()
            ->route('admin.products.index')
            ->with('success', 'Produit supprimé avec succès.');
    }

    /**
     * Toggle the status of the specified product.
     */
    public function toggleStatus(Product $product)
    {
        $product->is_active = !$product->is_active;
        $product->save();

        activity()
            ->causedBy(auth()->user())
            ->performedOn($product)
            ->log('product_status_updated');

        return response()->json([
            'success' => true,
            'message' => 'Statut du produit mis à jour avec succès.',
            'status' => $product->is_active
        ]);
    }
}
