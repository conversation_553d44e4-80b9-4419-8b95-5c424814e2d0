<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ProductImageController extends Controller
{
    public function index() { return view('admin.product_images.index'); }
    public function create() { return view('admin.product_images.create'); }
    public function store(Request $request) { return redirect()->route('admin.product_images.index'); }
    public function show($id) { return view('admin.product_images.show', compact('id')); }
    public function edit($id) { return view('admin.product_images.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.product_images.index'); }
    public function destroy($id) { return redirect()->route('admin.product_images.index'); }
}
