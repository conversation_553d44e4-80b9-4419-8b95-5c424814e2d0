@extends('layouts.admin')
@section('title', 'Nouvelle dépense')

@section('content')
<div class="max-w-2xl mx-auto py-8 space-y-6">
    <!-- En-tête -->
    <div class="flex items-center gap-3 bg-gradient-to-r from-indigo-600 to-indigo-800 text-white p-6 rounded-xl shadow-lg mb-2">
        <i class="fas fa-wallet text-3xl"></i>
        <div>
            <h2 class="text-2xl font-bold">Nouvelle dépense</h2>
            <p class="text-indigo-100 text-sm">Enregistrez une nouvelle dépense avec justificatif et catégorie.</p>
        </div>
    </div>

    <!-- Formulaire -->
    <form method="POST" action="{{ route('admin.accounting.expenses.store') }}" enctype="multipart/form-data" class="bg-white rounded-xl shadow-lg p-8 space-y-5">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block font-semibold mb-1 text-indigo-800">Description</label>
                <input type="text" name="description" class="form-input rounded-lg w-full" required maxlength="255" value="{{ old('description') }}">
            </div>
            <div>
                <label class="block font-semibold mb-1 text-indigo-800">Montant</label>
                <input type="number" step="0.01" name="amount" class="form-input rounded-lg w-full" required value="{{ old('amount') }}">
            </div>
            <div>
                <label class="block font-semibold mb-1 text-indigo-800">Catégorie</label>
                <div class="flex gap-2 items-center">
                    <select name="category_id" id="category_id" class="form-input rounded-lg w-full">
                        <option value="">-- Sélectionner --</option>
                        @foreach($categories as $cat)
                            <option value="{{ $cat->id }}">{{ $cat->name }}</option>
                        @endforeach
                    </select>
                    <button type="button" class="btn-glass bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-full px-3 py-1" id="addCatBtn"><i class="fas fa-plus"></i></button>
                </div>
                <div id="addCatForm" class="mt-2 hidden">
                    <input type="text" id="newCatName" class="form-input form-input-sm rounded mb-1" placeholder="Nom de la catégorie">
                    <input type="text" id="newCatDesc" class="form-input form-input-sm rounded mb-1" placeholder="Description (optionnel)">
                    <button type="button" class="btn btn-success btn-sm" id="saveCatBtn">Ajouter</button>
                    <button type="button" class="btn btn-link btn-sm text-gray-500" id="cancelCatBtn">Annuler</button>
                    <div id="catMsg" class="text-xs mt-1"></div>
                </div>
            </div>
            <div>
                <label class="block font-semibold mb-1 text-indigo-800">Date</label>
                <input type="date" name="date" class="form-input rounded-lg w-full" required value="{{ old('date', date('Y-m-d')) }}">
            </div>
        </div>
        <div>
            <label class="block font-semibold mb-1 text-indigo-800">Justificatif <span class="text-xs text-gray-400">(PDF, image, max 2Mo)</span></label>
            <input type="file" name="attachment" accept=".pdf,image/*" class="form-input rounded-lg w-full">
        </div>
        <div class="flex gap-2 mt-6">
            <button type="submit" class="btn-glass bg-indigo-700 text-white hover:bg-indigo-800 px-6 py-2 rounded-lg font-bold flex items-center gap-2">
                <i class="fas fa-save"></i> Enregistrer
            </button>
            <a href="{{ route('admin.accounting.expenses.index') }}" class="btn-glass bg-white text-indigo-700 hover:bg-indigo-100 px-6 py-2 rounded-lg font-bold flex items-center gap-2">
                <i class="fas fa-arrow-left"></i> Annuler
            </a>
        </div>
    </form>
</div>
@push('styles')
<style>
.btn-glass {
    box-shadow: 0 2px 8px 0 rgba(99,102,241,0.07);
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    border: none;
}
.btn-glass:hover {
    transform: scale(1.04);
    background: #e0e7ff;
}
.form-input {
    border: 2px solid #6366f1 !important; /* Indigo-500 */
    background: #fff;
    color: #22223b;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 2px 0 rgba(99,102,241,0.03);
}
.form-input:focus {
    border-color: #4f46e5 !important; /* Indigo-700 */
    outline: none;
    box-shadow: 0 0 0 2px #a5b4fc44;
}
.form-input-sm {
    border: 2px solid #6366f1 !important;
    font-size: 0.95em;
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
<script>
document.getElementById('addCatBtn').onclick = function() {
    document.getElementById('addCatForm').classList.remove('hidden');
    this.disabled = true;
};
document.getElementById('cancelCatBtn').onclick = function() {
    document.getElementById('addCatForm').classList.add('hidden');
    document.getElementById('addCatBtn').disabled = false;
};
document.getElementById('saveCatBtn').onclick = function() {
    let name = document.getElementById('newCatName').value.trim();
    let desc = document.getElementById('newCatDesc').value.trim();
    let msg = document.getElementById('catMsg');
    if (!name) { msg.innerText = 'Le nom est requis.'; return; }
    msg.innerText = 'Ajout...';
    fetch("{{ route('admin.accounting.expenses.category.quickadd') }}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('input[name=_token]').value
        },
        body: JSON.stringify({ name: name, description: desc })
    })
    .then(r => r.json())
    .then(data => {
        let sel = document.getElementById('category_id');
        let opt = document.createElement('option');
        opt.value = data.id;
        opt.text = data.name;
        sel.appendChild(opt);
        sel.value = data.id;
        msg.innerText = 'Catégorie ajoutée !';
        setTimeout(() => {
            document.getElementById('addCatForm').classList.add('hidden');
            document.getElementById('addCatBtn').disabled = false;
            msg.innerText = '';
            document.getElementById('newCatName').value = '';
            document.getElementById('newCatDesc').value = '';
        }, 800);
    })
    .catch(() => { msg.innerText = 'Erreur lors de l\'ajout.'; });
};
</script>
@endsection
