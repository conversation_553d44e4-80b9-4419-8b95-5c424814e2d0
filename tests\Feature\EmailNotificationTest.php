<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Notifications\WelcomeNotification;
use App\Notifications\PasswordResetNotification;
use App\Notifications\AccountVerificationNotification;

class EmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    /** @test */
    public function welcome_email_is_sent_to_new_users()
    {
        // Créer un nouvel utilisateur
        $user = User::factory()->create();

        // Envoyer la notification de bienvenue
        $user->notify(new WelcomeNotification());

        // Vérifier que l'email a été envoyé au bon utilisateur
        Mail::assertSent(WelcomeNotification::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    /** @test */
    public function password_reset_email_is_sent_when_requested()
    {
        // Créer un utilisateur
        $user = User::factory()->create();

        // Envoyer la notification de réinitialisation de mot de passe
        $user->notify(new PasswordResetNotification());

        // Vérifier que l'email a été envoyé
        Mail::assertSent(PasswordResetNotification::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    /** @test */
    public function account_verification_email_is_sent_to_new_users()
    {
        // Créer un nouvel utilisateur non vérifié
        $user = User::factory()->unverified()->create();

        // Envoyer la notification de vérification
        $user->notify(new AccountVerificationNotification());

        // Vérifier que l'email a été envoyé
        Mail::assertSent(AccountVerificationNotification::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    /** @test */
    public function emails_are_not_sent_to_invalid_addresses()
    {
        // Créer un utilisateur avec une adresse email invalide
        $user = User::factory()->create([
            'email' => 'invalid-email'
        ]);

        // Tenter d'envoyer une notification
        $user->notify(new WelcomeNotification());

        // Vérifier qu'aucun email n'a été envoyé
        Mail::assertNothingSent();
    }
}
