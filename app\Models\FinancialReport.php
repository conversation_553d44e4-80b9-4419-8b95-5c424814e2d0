<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FinancialReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'report_type',
        'accounting_period_id',
        'shop_id',
        'content',
        'status',
        'generated_at',
        'validated_at'
    ];

    protected $casts = [
        'content' => 'array',
        'generated_at' => 'datetime',
        'validated_at' => 'datetime'
    ];

    public function period()
    {
        return $this->belongsTo(AccountingPeriod::class, 'accounting_period_id');
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function validate()
    {
        $this->status = 'validated';
        $this->validated_at = now();
        $this->save();
    }
}
