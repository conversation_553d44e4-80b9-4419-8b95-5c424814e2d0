@extends('layouts.app')

@section('title', 'Transferts Inter-boutiques')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Transferts Inter-boutiques</h1>
        @can('create', App\Models\Transfer::class)
        <a href="{{ route('transfers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouveau Transfert
        </a>
        @endcan
    </div>

    <div class="card">
        <div class="card-header">
            <form action="{{ route('transfers.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="shop_id" class="form-label">Boutique</label>
                    <select name="shop_id" id="shop_id" class="form-select">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}" @selected(request('shop_id') == $shop->id)>
                                {{ $shop->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="transfer_type" class="form-label">Type</label>
                    <select name="transfer_type" id="transfer_type" class="form-select">
                        <option value="">Tous les types</option>
                        <option value="sent" @selected(request('transfer_type') == 'sent')>Envoyés</option>
                        <option value="received" @selected(request('transfer_type') == 'received')>Reçus</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous les statuts</option>
                        @foreach(['pending' => 'En attente', 'validated' => 'Validé', 'completed' => 'Terminé', 'cancelled' => 'Annulé'] as $value => $label)
                            <option value="{{ $value }}" @selected(request('status') == $value)>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_range" class="form-label">Période</label>
                    <input type="text" class="form-control" id="date_range" name="date_range" 
                           value="{{ request('date_range') }}" placeholder="Sélectionner une période">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-filter"></i> Filtrer
                    </button>
                    <a href="{{ route('transfers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>De</th>
                            <th>Vers</th>
                            <th>Date</th>
                            <th>Produits</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($transfers as $transfer)
                            <tr>
                                <td>{{ $transfer->reference_number }}</td>
                                <td>{{ $transfer->fromShop->name }}</td>
                                <td>{{ $transfer->toShop->name }}</td>
                                <td>{{ $transfer->transfer_date->format('d/m/Y') }}</td>
                                <td>{{ $transfer->items_count }} produit(s)</td>
                                <td>
                                    <span class="badge bg-{{ $transfer->status_color }}">
                                        {{ $transfer->status_label }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('transfers.show', $transfer) }}" 
                                           class="btn btn-sm btn-info" title="Détails">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        @if($transfer->status === 'pending' && auth()->user()->can('validate', $transfer))
                                            <form action="{{ route('transfers.validate', $transfer) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                @method('PUT')
                                                <button type="submit" class="btn btn-sm btn-success" 
                                                        title="Valider">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        @endif

                                        @if($transfer->status === 'validated' && auth()->user()->can('receive', $transfer))
                                            <a href="{{ route('transfers.receive.form', $transfer) }}" 
                                               class="btn btn-sm btn-warning" title="Réceptionner">
                                                <i class="fas fa-box"></i>
                                            </a>
                                        @endif

                                        @if($transfer->status === 'pending' && auth()->user()->can('cancel', $transfer))
                                            <form action="{{ route('transfers.cancel', $transfer) }}" 
                                                  method="POST" class="d-inline"
                                                  onsubmit="return confirm('Êtes-vous sûr de vouloir annuler ce transfert ?')">
                                                @csrf
                                                @method('PUT')
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        title="Annuler">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Aucun transfert trouvé</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $transfers->withQueryString()->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialisation de Select2
        $('#shop_id, #transfer_type, #status').select2({
            theme: 'bootstrap-5'
        });

        // Initialisation du sélecteur de dates
        $('#date_range').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Appliquer',
                cancelLabel: 'Annuler',
                fromLabel: 'Du',
                toLabel: 'Au'
            },
            autoUpdateInput: false
        });

        $('#date_range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
        });

        $('#date_range').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    });
</script>
@endpush
