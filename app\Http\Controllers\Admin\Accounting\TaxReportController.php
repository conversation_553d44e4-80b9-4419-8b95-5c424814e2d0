<?php

namespace App\Http\Controllers\Admin\Accounting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\TaxReport;
use App\Models\Sale;
use App\Models\JournalEntryLine;
use App\Models\Account;
use Carbon\Carbon;

class TaxReportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validation de la période
        $request->validate([
            'period' => 'required|string|max:255',
        ]);

        $period = $request->input('period');

        // Exemple de format de période : '2025-04' ou '2025-Q1'
        // Ici, on suppose un format 'YYYY-MM' (mois)
        $start = Carbon::parse($period.'-01')->startOfMonth();
        $end = (clone $start)->endOfMonth();

        // Calcul automatique de la TVA collectée sur les ventes
        $total_tva_collected = Sale::whereBetween('created_at', [$start, $end])->sum('tva');

        // Recherche du compte TVA déductible (ex : code 4456 ou similaire)
        $tva_deductible_account = Account::where('name', 'like', '%TVA déductible%')->orWhere('code', '4456')->first();
        $total_tva_deductible = 0;
        if ($tva_deductible_account) {
            $total_tva_deductible = JournalEntryLine::where('account_id', $tva_deductible_account->id)
                ->whereBetween('created_at', [$start, $end])
                ->sum('debit'); // On suppose que la TVA déductible est au débit
        }

        // Calcul du total des taxes (ici, TVA collectée - TVA déductible)
        $total_taxes = $total_tva_collected - $total_tva_deductible;

        // Création du rapport fiscal
        $taxReport = TaxReport::create([
            'period' => $period,
            'total_tva_collected' => $total_tva_collected,
            'total_tva_deductible' => $total_tva_deductible,
            'total_taxes' => $total_taxes,
            'status' => 'draft',
        ]);

        return redirect()->route('admin.accounting.tax_reports.index')
            ->with('success', 'Rapport fiscal généré automatiquement.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
