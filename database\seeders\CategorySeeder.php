<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    public function run()
    {
        $categories = [
            'Poissons',
            'Viandes',
            'Produits laitiers',
            'Alimentation',
        ];

        foreach ($categories as $category) {
            $slug = Str::slug($category);
            if (!Category::where('slug', $slug)->exists()) {
                Category::create([
                    'name' => $category,
                    'slug' => $slug,
                    'description' => "Catégorie {$category}",
                    'is_active' => true,
                ]);
            }
        }
    }
}
