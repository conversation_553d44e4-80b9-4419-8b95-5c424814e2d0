@php
    // Variables: $sale, $shop, $items (à passer depuis le contrôleur)
@endphp
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Facture #{{ $sale->invoice_number }}</title>
    <style>
        @media print {
            @page {
                size: 80mm auto;
                margin: 0;
            }
            body, html { margin: 0; padding: 0; }
            .ticket { width: 80mm; max-width: 80mm; min-width: 80mm; font-size: 12px; font-family: 'Segoe UI', Arial, 'Courier New', Courier, monospace; overflow: auto; }
        }
        body {
            background: #f4f7fa;
        }
        .ticket {
            width: 80mm;
            max-width: 80mm;
            min-width: 80mm;
            margin: 24px auto;
            font-size: 12px;
            font-family: 'Segoe UI', Arial, 'Courier New', Courier, monospace;
            background: #fff;
            color: #222;
            border-radius: 12px;
            box-shadow: 0 4px 24px 0 #b7d8f7;
            overflow: auto;
            border: 2px solid #4f8be5;
            padding: 12px 10px 6px 10px;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
        }
        .header .shop-name {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
            letter-spacing: 1px;
        }
        .header .shop-info {
            font-size: 11px;
            color: #555;
        }
        .line {
            border-top: 1px dashed #4f8be5;
            margin: 8px 0 10px 0;
        }
        .facture-info {
            font-size: 12px;
            margin-bottom: 8px;
        }
        .facture-info span {
            color: #2563eb;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th {
            background: #e3edfa;
            color: #2563eb;
            font-size: 12px;
            font-weight: bold;
            padding: 2px 0;
            border-bottom: 1px solid #b7d8f7;
        }
        td {
            padding: 2px 0;
        }
        .totaux td {
            font-size: 13px;
        }
        .total {
            color: #16a34a;
            font-size: 15px;
            font-weight: bold;
        }
        .center { text-align: center; }
        .merci {
            color: #2563eb;
            font-size: 13px;
            font-weight: bold;
            margin-top: 10px;
        }
        .astuce {
            font-size:10px;
            margin-top:8px;
            color:#888;
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="header">
            <div class="shop-name">{{ $shop->name }}</div>
            <div class="shop-info">{{ $shop->address ?? '' }}</div>
            <div class="shop-info">Tél: {{ $shop->phone ?? '' }}</div>
        </div>
        <div class="line"></div>
        <div class="facture-info">
            N° Facture : <span>{{ $sale->invoice_number }}</span><br>
            Date : <span>{{ $sale->created_at->format('d/m/Y H:i') }}</span><br>
            Client : <span>{{ $sale->customer->name ?? 'Client de passage' }}</span>
        </div>
        <div class="line"></div>
        <table>
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Qté</th>
                    <th>PU</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($items as $item)
                <tr>
                    <td>{{ $item->product->name }}</td>
                    <td class="center">{{ $item->quantity }}</td>
                    <td class="center">{{ number_format($item->unit_price, 0) }}</td>
                    <td class="center">{{ number_format($item->total_price, 0) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        <div class="line"></div>
        @php
            $total_brut = 0;
            foreach($items as $item) {
                $total_brut += $item->total_price;
            }
        @endphp
        <table class="totaux">
            <tr>
                <td>Total brut</td>
                <td class="center">{{ number_format($total_brut, 0) }} FCFA</td>
            </tr>
            @if(!empty($sale->discount) && $sale->discount > 0)
            <tr>
                <td>Remise</td>
                <td class="center">-{{ number_format($sale->discount, 0) }} FCFA</td>
            </tr>
            @endif
            @if(!empty($sale->tva) && $sale->tva > 0)
                @php
                    $base = $total_brut - ($sale->discount ?? 0);
                    $montant_tva = $base * $sale->tva / 100;
                @endphp
            <tr>
                <td>TVA (18%)</td>
                <td class="center">{{ number_format($montant_tva, 0) }} FCFA</td>
            </tr>
            @endif
            <tr>
                <td class="total">Net à payer</td>
                <td class="center total">{{ number_format($sale->total_amount, 0) }} FCFA</td>
            </tr>
            <tr>
                <td>Montant remis</td>
                <td class="center">{{ number_format($sale->amount_paid ?? 0, 0) }} FCFA</td>
            </tr>
            <tr>
                <td>Monnaie rendue</td>
                <td class="center">{{ number_format(max(($sale->amount_paid ?? 0) - $sale->total_amount, 0), 0) }} FCFA</td>
            </tr>
            <tr>
                <td>Mode paiement</td>
                <td class="center">
                  @if(($sale->payment_method ?? '') === 'cash')
                    Espèces
                  @elseif(($sale->payment_method ?? '') === 'card')
                    Carte
                  @else
                    {{ ucfirst($sale->payment_method ?? '-') }}
                  @endif
                </td>
            </tr>
            <tr>
                <td>Statut</td>
                <td class="center">
                  @if(($sale->payment_status ?? '') === 'paid')
                    Payé
                  @elseif(($sale->payment_status ?? '') === 'pending')
                    En attente
                  @else
                    {{ ucfirst($sale->payment_status ?? '-') }}
                  @endif
                </td>
            </tr>
        </table>
        <div class="line"></div>
        <div class="center merci">Merci pour votre achat !</div>
        <div class="center astuce">Astuce : Sélectionnez l'imprimante "CP-80204 thermal printer" dans la boîte de dialogue d'impression.</div>
    </div>
</body>
</html>
