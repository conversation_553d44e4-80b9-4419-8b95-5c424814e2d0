<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class ProductImage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'product_id',
        'path',
        'is_primary',
        'order'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'order' => 'integer'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getUrlAttribute()
    {
        return asset('storage/' . $this->path);
    }

    protected static function boot()
    {
        parent::boot();

        // Définir l'ordre par défaut lors de la création
        static::creating(function ($image) {
            if (is_null($image->order)) {
                $image->order = $image->product->images()->max('order') + 1;
            }
        });

        // Supprimer les fichiers lors de la suppression de l'image
        static::deleting(function ($image) {
            Storage::disk('public')->delete($image->path);
        });
    }
}
