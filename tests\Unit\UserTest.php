<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    private $admin;
    private $manager;
    private $mainShop;
    private $annexShop;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer les permissions si elles n'existent pas
        $permissions = [
            'view shops',
            'create shops',
            'edit shops',
            'delete shops',
            'manage users'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Créer les rôles avec leurs permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions([
            'view shops', 'create shops', 'edit shops', 
            'delete shops', 'manage users'
        ]);

        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $managerRole->syncPermissions(['view shops']);

        // Créer les boutiques
        $this->mainShop = Shop::create([
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'is_main_shop' => true
        ]);

        $this->annexShop = Shop::create([
            'name' => 'Boutique Annexe',
            'code' => 'ANX-001',
            'address' => '456 Rue Secondaire',
            'phone' => '+987654321',
            'is_main_shop' => false,
            'parent_shop_id' => $this->mainShop->id
        ]);

        // Créer les utilisateurs
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'shop_id' => $this->mainShop->id
        ]);
        $this->admin->assignRole('admin');

        $this->manager = User::factory()->create([
            'email' => '<EMAIL>',
            'shop_id' => $this->annexShop->id
        ]);
        $this->manager->assignRole('manager');
    }

    public function test_user_belongs_to_shop()
    {
        $this->assertEquals($this->mainShop->id, $this->admin->shop->id);
        $this->assertEquals($this->annexShop->id, $this->manager->shop->id);
    }

    public function test_user_has_correct_role()
    {
        $this->assertTrue($this->admin->hasRole('admin'));
        $this->assertTrue($this->manager->hasRole('manager'));
    }

    public function test_admin_has_all_permissions()
    {
        $this->assertTrue($this->admin->can('view shops'));
        $this->assertTrue($this->admin->can('create shops'));
        $this->assertTrue($this->admin->can('edit shops'));
        $this->assertTrue($this->admin->can('delete shops'));
        $this->assertTrue($this->admin->can('manage users'));
    }

    public function test_manager_has_limited_permissions()
    {
        $this->assertTrue($this->manager->can('view shops'));
        $this->assertFalse($this->manager->can('create shops'));
        $this->assertFalse($this->manager->can('edit shops'));
        $this->assertFalse($this->manager->can('delete shops'));
        $this->assertFalse($this->manager->can('manage users'));
    }

    public function test_user_helper_methods()
    {
        $this->assertTrue($this->admin->isAdmin());
        $this->assertFalse($this->admin->isManager());

        $this->assertFalse($this->manager->isAdmin());
        $this->assertTrue($this->manager->isManager());
    }

    public function test_user_soft_delete()
    {
        $user = User::factory()->create();
        $user->delete();

        $this->assertSoftDeleted($user);
    }

    public function test_user_can_be_deactivated()
    {
        $user = User::factory()->create(['is_active' => true]);
        $this->assertTrue($user->is_active);

        $user->update(['is_active' => false]);
        $this->assertFalse($user->is_active);
    }
}
