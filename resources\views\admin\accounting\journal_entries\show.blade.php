@extends('layouts.admin')

@section('title', 'Détail de l\'écriture comptable')

<!-- Bouton Dark Mode -->
<div class="position-fixed" style="top: 30px; right: 30px; z-index: 1100;">
    <button id="toggleDarkMode" class="btn btn-dark rounded-circle shadow" title="Basculer mode sombre" style="width:52px; height:52px; font-size:1.5rem;">
        <i id="darkModeIcon" class="fas fa-moon"></i>
    </button>
</div>

@section('content')
<div class="container-fluid">
    <h1 class="h3 mb-3 fw-bold text-primary">Détail de l'écriture comptable</h1>
    <div class="mb-3">
        <a href="{{ route('admin.accounting.journal_entries.index') }}" class="btn btn-secondary btn-lg rounded-pill">Retour à la liste</a>
        <a href="{{ route('admin.accounting.journal_entries.edit', $entry) }}" class="btn btn-warning btn-lg rounded-pill">Modifier</a>
    </div>
    <div class="card mb-3 shadow-lg rounded-4 border-0">
        <div class="card-body bg-gradient p-4 rounded-4" style="background: linear-gradient(90deg, #e0e7ff 0%, #f0fdfa 100%);">
            <div class="row mb-2">
                <div class="col-md-3"><strong class="text-primary">Date :</strong> {{ $entry->date->format('d/m/Y') }}</div>
                <div class="col-md-3"><strong class="text-info">Référence :</strong> {{ $entry->reference }}</div>
                <div class="col-md-6"><strong class="text-info">Description :</strong> {{ $entry->description }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-6"><strong class="text-success">Utilisateur :</strong> {{ $entry->user ? $entry->user->name : '-' }}</div>
            </div>
            <h5 class="mt-3 text-success">Lignes d'écriture</h5>
            <div class="table-responsive">
                <table class="table table-hover align-middle rounded-3 overflow-hidden">
                    <thead style="background: linear-gradient(90deg, #818cf8 0%, #5eead4 100%); color: #fff;">
                        <tr>
                            <th>Compte</th>
                            <th>Débit</th>
                            <th>Crédit</th>
                            <th>Libellé</th>
                            <th>Pièce jointe</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($entry->lines as $line)
                            <tr>
                                <td class="fw-bold text-primary">{{ $line->account->code }} - {{ $line->account->name }}</td>
                                <td class="text-success fw-bold">{{ number_format($line->debit, 2, ',', ' ') }}</td>
                                <td class="text-danger fw-bold">{{ number_format($line->credit, 2, ',', ' ') }}</td>
                                <td>{{ $line->label }}</td>
                                <td>
                                    @if($line->attachment)
                                        <a href="{{ asset('storage/' . $line->attachment) }}" target="_blank" class="btn btn-info btn-sm rounded-pill">Voir le fichier</a>
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    body.dark-mode {
        background: #181a1b !important;
        color: #e4e4e4 !important;
    }
    .dark-mode .bg-gradient, .dark-mode .card-footer.bg-gradient {
        background: linear-gradient(90deg, #232526 0%, #414345 100%) !important;
        color: #fff !important;
    }
    .dark-mode .card, .dark-mode .card-body, .dark-mode .table, .dark-mode .table-responsive {
        background: #232526 !important;
        color: #e4e4e4 !important;
    }
    .dark-mode .form-control, .dark-mode .form-select {
        background: #232526 !important;
        color: #fff !important;
        border-color: #6366f1 !important;
    }
    .dark-mode .form-label {
        color: #38bdf8 !important;
    }
    .dark-mode .btn, .dark-mode .btn-gradient-primary {
        background: linear-gradient(90deg, #6366f1 0%, #06b6d4 100%) !important;
        color: #fff !important;
        border: none !important;
    }
    .dark-mode .table-hover tbody tr:hover {
        background: #334155 !important;
    }
    .dark-mode .shadow, .dark-mode .shadow-sm, .dark-mode .shadow-lg {
        box-shadow: 0 2px 16px rgba(0,0,0,0.7)!important;
    }
</style>
@endpush

@push('scripts')
<script>
// Animation apparition lignes
const rows = document.querySelectorAll('tbody tr');
rows.forEach((row, i) => {
    row.style.opacity = 0;
    row.style.transform = 'translateY(20px)';
    setTimeout(() => {
        row.style.transition = 'all .5s cubic-bezier(.4,2,.3,1)';
        row.style.opacity = 1;
        row.style.transform = 'translateY(0)';
    }, 100 + i * 80);
});
// Dark mode toggle
const toggleBtn = document.getElementById('toggleDarkMode');
const icon = document.getElementById('darkModeIcon');
const isDark = localStorage.getItem('darkMode') === '1';
if(isDark) {
    document.body.classList.add('dark-mode');
    icon.classList.remove('fa-moon');
    icon.classList.add('fa-sun');
}
toggleBtn.addEventListener('click', function() {
    document.body.classList.toggle('dark-mode');
    const dark = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', dark ? '1' : '0');
    icon.classList.toggle('fa-moon', !dark);
    icon.classList.toggle('fa-sun', dark);
});
</script>
@endpush
