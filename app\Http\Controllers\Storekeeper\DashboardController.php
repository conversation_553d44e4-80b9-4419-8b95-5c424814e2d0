<?php

namespace App\Http\Controllers\Storekeeper;

use App\Http\Controllers\Controller;
use App\Models\Stock;
use App\Models\Product;
use App\Models\Supply;
use App\Models\Transfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:storekeeper']);
    }

    public function index()
    {
        $shop = Auth::user()->shop;

        // Statistiques du stock
        $stats = [
            'total_products' => Product::whereHas('stocks', function($query) use ($shop) {
                $query->where('shop_id', $shop->id);
            })->count(),
            'low_stock' => Stock::where('shop_id', $shop->id)
                            ->whereColumn('quantity', '<=', 'min_stock')
                            ->count(),
            'pending_supplies' => Supply::where('shop_id', $shop->id)
                                    ->where('status', 'pending')
                                    ->count(),
            'pending_transfers' => Transfer::where(function($query) use ($shop) {
                                    $query->where('source_shop_id', $shop->id)
                                        ->orWhere('destination_shop_id', $shop->id);
                                })
                                ->where('status', 'pending')
                                ->count()
        ];

        // Produits en stock critique
        $lowStockProducts = Product::with(['stocks' => function($query) use ($shop) {
            $query->where('shop_id', $shop->id);
        }])
        ->whereHas('stocks', function($query) use ($shop) {
            $query->where('shop_id', $shop->id)
                ->whereColumn('quantity', '<=', 'min_stock');
        })
        ->take(5)
        ->get();

        // Approvisionnements en attente
        $pendingSupplies = Supply::with('supplier')
            ->where('shop_id', $shop->id)
            ->where('status', 'pending')
            ->orderBy('expected_date')
            ->take(5)
            ->get();

        // Transferts en attente
        $pendingTransfers = Transfer::with(['sourceShop', 'destinationShop'])
            ->where(function($query) use ($shop) {
                $query->where('source_shop_id', $shop->id)
                    ->orWhere('destination_shop_id', $shop->id);
            })
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Mouvements de stock récents
        $recentMovements = Stock::with(['product', 'user'])
            ->where('shop_id', $shop->id)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('storekeeper.dashboard', compact(
            'stats',
            'lowStockProducts',
            'pendingSupplies',
            'pendingTransfers',
            'recentMovements'
        ));
    }
}
