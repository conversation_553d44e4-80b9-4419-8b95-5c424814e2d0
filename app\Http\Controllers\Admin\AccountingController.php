<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Stock;
use App\Models\Store;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AccountingController extends Controller
{
    public function index()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // Récupérer les statistiques de base
        $totalRevenue = Sale::whereMonth('sales.created_at', $currentMonth)
            ->whereYear('sales.created_at', $currentYear)
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        // Calculer les dépenses à partir des ventes
        $totalExpenses = Sale::whereMonth('sales.created_at', $currentMonth)
            ->whereYear('sales.created_at', $currentYear)
            ->where('payment_status', 'paid')
            ->join('sale_items', 'sales.id', '=', 'sale_items.sale_id')
            ->sum(\DB::raw('sale_items.quantity * sale_items.cost_price'));

        $balance = $totalRevenue - $totalExpenses;

        // Récupérer les statistiques mensuelles pour les graphiques
        $monthlyStats = Sale::selectRaw('
                MONTH(sales.created_at) as month,
                SUM(CASE WHEN payment_status = "paid" THEN total_amount ELSE 0 END) as revenue,
                COUNT(*) as total_sales
            ')
            ->whereYear('sales.created_at', $currentYear)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.accounting.index', compact(
            'totalRevenue', 
            'totalExpenses', 
            'balance',
            'monthlyStats'
        ));
    }

    public function revenue(Request $request)
    {
        // Récupération des filtres
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $storeId = $request->input('store_id');

        // Query de base
        $query = Sale::with(['shop', 'customer'])
            ->when($startDate, function ($query) use ($startDate) {
                return $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->whereDate('created_at', '<=', $endDate);
            })
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('store_id', $storeId);
            });

        // Récupération des ventes paginées
        $revenues = $query->latest()->paginate(15);

        // Récupération de toutes les boutiques pour le filtre
        $stores = Store::orderBy('name')->get();

        // Statistiques globales
        $stats = [
            'total' => $query->sum('total_amount'),
            'paid' => $query->where('payment_status', 'paid')->sum('total_amount'),
            'pending' => $query->where('payment_status', 'pending')->sum('total_amount'),
            'count' => $query->count(),
        ];

        // Données pour le graphique
        $chartData = $query->select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(total_amount) as total')
        )
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        return view('admin.accounting.revenue', compact(
            'revenues',
            'stores',
            'stats',
            'chartData',
            'startDate',
            'endDate',
            'storeId'
        ));
    }

    public function expenses()
    {
        $expenses = Sale::with(['user', 'shop', 'items', 'items.product'])
            ->where('payment_status', 'paid')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.accounting.expenses', compact('expenses'));
    }

    public function balance(Request $request)
    {
        $from = $request->input('from');
        $to = $request->input('to');

        $months = \DB::table('sales')
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month')
            ->whereNull('deleted_at')
            ->groupByRaw('DATE_FORMAT(created_at, "%Y-%m")')
            ->orderByDesc('month')
            ->limit(12);

        $monthlyBalances = \DB::table(\DB::raw("({$months->toSql()}) as months"))
            ->mergeBindings($months)
            ->selectRaw('
                months.month,
                (SELECT SUM(total_amount) FROM sales WHERE DATE_FORMAT(created_at, "%Y-%m") = months.month AND deleted_at IS NULL) as total_revenue,
                (SELECT SUM(si.quantity * si.cost_price)
                    FROM sale_items si
                    JOIN sales s2 ON s2.id = si.sale_id
                    WHERE DATE_FORMAT(s2.created_at, "%Y-%m") = months.month AND s2.deleted_at IS NULL
                ) as total_expenses
            ')
            ->orderByDesc('months.month')
            ->get()
            ->map(function ($balance) {
                $balance->net_balance = $balance->total_revenue - $balance->total_expenses;
                return $balance;
            });

        return view('admin.accounting.balance', compact('monthlyBalances', 'from', 'to'));
    }

    public function reports()
    {
        return view('admin.accounting.reports');
    }

    public function export(Request $request)
    {
        // Logique d'export à implémenter selon les besoins
        return back()->with('info', 'Fonctionnalité d\'export en cours de développement.');
    }
}
