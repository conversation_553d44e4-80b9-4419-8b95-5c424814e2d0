body {
    background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
    min-height: 100vh;
}
.vibrant-bg {
    background: linear-gradient(120deg, rgba(129,140,248,0.25) 0%, rgba(236,72,153,0.18) 100%);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
}
.vibrant-table-bg {
    background: linear-gradient(90deg, rgba(34,211,238,0.12) 0%, rgba(236,72,153,0.10) 100%);
}
.text-gradient {
    background: linear-gradient(90deg, #6366f1, #06b6d4, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.text-gradient-blue { color: #2563eb; }
.text-gradient-green { color: #22d3ee; }
.text-gradient-pink { color: #ec4899; }
.glass-card, .glass-table-container, .glass-alert {
    background: rgba(255,255,255,0.32);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
    backdrop-filter: blur(8px);
    border-radius: 18px;
    border: 1px solid rgba(255,255,255,0.18);
}
.glass-btn, .neon-btn {
    background: linear-gradient(90deg, #818cf8 0%, #5eead4 100%);
    border: none;
    border-radius: 999px;
    padding: 0.6em 1.2em;
    color: #fff;
    font-weight: bold;
    box-shadow: 0 0 10px #818cf8, 0 0 30px #5eead4;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.glass-btn:hover, .neon-btn:hover {
    background: linear-gradient(90deg, #ec4899 0%, #818cf8 100%);
    color: #fff;
    box-shadow: 0 0 24px #ec4899, 0 0 40px #818cf8;
}
.vibrant-input {
    background: rgba(255,255,255,0.7);
    border: 1.5px solid #818cf8;
    border-radius: 999px;
    padding: 0.7em 1.2em;
    font-size: 1.1em;
    box-shadow: 0 1px 4px 0 rgba(31,38,135,0.08);
    transition: border 0.2s, box-shadow 0.2s;
}
.vibrant-input:focus {
    border: 2px solid #ec4899;
    box-shadow: 0 2px 8px 0 rgba(236,72,153,0.18);
    outline: none;
}
.glass-row-anim {
    animation: fadeInUp 0.6s;
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px);}
    to { opacity: 1; transform: translateY(0);}
}
