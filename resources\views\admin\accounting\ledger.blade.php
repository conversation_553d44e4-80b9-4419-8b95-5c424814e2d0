@extends('layouts.admin')
@section('content')
<div class="container max-w-4xl mx-auto py-8">
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-2 flex items-center">
            <i class="fas fa-list-ol mr-2"></i> Grand Livre
        </h2>
        <p class="text-gray-500 mb-4">Visualisez tous les mouvements d'un compte avec solde progressif.</p>
        <form method="GET" action="{{ route('admin.accounting.ledger') }}" class="flex flex-wrap gap-4 items-end bg-white shadow-md rounded-lg p-4">
            <div>
                <label class="block text-sm font-semibold text-gray-600">Compte</label>
                <select name="account_id" class="form-control rounded-lg">
                    <option value="">-- Choisir un compte --</option>
                    @foreach($accounts as $a)
                        <option value="{{ $a->id }}" @if(isset($account) && $account && $account->id == $a->id) selected @endif>{{ $a->code }} - {{ $a->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-semibold text-gray-600">Du</label>
                <input type="date" name="from" class="form-control rounded-lg" value="{{ $from }}">
            </div>
            <div>
                <label class="block text-sm font-semibold text-gray-600">Au</label>
                <input type="date" name="to" class="form-control rounded-lg" value="{{ $to }}">
            </div>
            <div>
                <button class="btn btn-primary px-6 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 text-white font-bold shadow hover:from-blue-600 hover:to-blue-800 transition-all flex items-center" type="submit">
                    <i class="fas fa-search mr-2"></i>Afficher
                </button>
            </div>
        </form>
    </div>
    @if($account)
        <div class="mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 shadow">
                <div>
                    <div class="text-lg font-semibold text-blue-900 flex items-center">
                        <i class="fas fa-book mr-2"></i>{{ $account->code }} - {{ $account->name }}
                    </div>
                    <div class="text-sm text-gray-600 mt-1">Type : <span class="font-semibold">{{ $account->type }}</span></div>
                </div>
                <div class="mt-2 md:mt-0">
                    @php $finalSolde = 0; foreach($lines as $l) { $finalSolde += ($l->debit - $l->credit); } @endphp
                    <span class="px-4 py-2 rounded-full text-white font-bold {{ $finalSolde >= 0 ? 'bg-green-500' : 'bg-red-500' }}">
                        Solde final : {{ number_format($finalSolde, 2, ',', ' ') }} €
                        <span class="ml-2 text-xs font-semibold px-2 py-1 rounded {{ $finalSolde >= 0 ? 'bg-green-700' : 'bg-red-700' }}">
                            {{ $finalSolde >= 0 ? 'Débiteur' : 'Créditeur' }}
                        </span>
                    </span>
                </div>
            </div>
        </div>
        <div class="card shadow-lg border-0">
            <div class="card-body p-0">
                <div class="overflow-x-auto">
                <table class="table table-bordered table-hover mb-0 animate-fade-in" style="min-width:700px">
                    <thead class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
                        <tr>
                            <th>Date</th>
                            <th>Libellé</th>
                            <th>Débit</th>
                            <th>Crédit</th>
                            <th>Solde</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php $solde = 0; @endphp
                        @forelse($lines as $line)
                            @php $solde += ($line->debit - $line->credit); @endphp
                            <tr class="transition hover:bg-blue-50">
                                <td>{{ $line->created_at->format('d/m/Y') }}</td>
                                <td>{{ $line->libelle }}</td>
                                <td class="text-end text-green-700 font-bold">
                                    @if($line->debit > 0)
                                        <i class="fas fa-arrow-down mr-1"></i>{{ number_format($line->debit, 2, ',', ' ') }}
                                    @endif
                                </td>
                                <td class="text-end text-red-700 font-bold">
                                    @if($line->credit > 0)
                                        <i class="fas fa-arrow-up mr-1"></i>{{ number_format($line->credit, 2, ',', ' ') }}
                                    @endif
                                </td>
                                <td class="text-end font-semibold {{ $solde >= 0 ? 'text-green-600' : 'text-red-600' }}">{{ number_format($solde, 2, ',', ' ') }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center text-gray-500 py-8">
                                    <i class="fas fa-info-circle mr-2"></i>Aucun mouvement trouvé pour ce compte sur la période.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    @endif
</div>
<style>
@keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: none; } }
.animate-fade-in { animation: fade-in 0.7s cubic-bezier(.4,0,.2,1); }
</style>
@endsection
