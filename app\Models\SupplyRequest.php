<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplyRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'from_shop_id',
        'to_shop_id',
        'reference_number',
        'request_date',
        'required_date',
        'status',
        'notes',
        'validated_by',
        'processed_by'
    ];

    protected $casts = [
        'request_date' => 'datetime',
        'required_date' => 'datetime'
    ];

    /**
     * Les différents statuts possibles d'une demande d'approvisionnement
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_VALIDATED = 'validated';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PROCESSED = 'processed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Obtenir la boutique qui demande l'approvisionnement
     */
    public function requestingShop()
    {
        return $this->belongsTo(Shop::class, 'from_shop_id');
    }

    /**
     * Obtenir la boutique qui doit fournir l'approvisionnement
     */
    public function supplyingShop()
    {
        return $this->belongsTo(Shop::class, 'to_shop_id');
    }

    /**
     * Obtenir les détails des produits demandés
     */
    public function items()
    {
        return $this->hasMany(SupplyRequestItem::class);
    }

    /**
     * Obtenir l'utilisateur qui a validé la demande
     */
    public function validator()
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    /**
     * Obtenir l'utilisateur qui a traité la demande
     */
    public function processor()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Vérifier si la demande peut être modifiée
     */
    public function isEditable()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING]);
    }

    /**
     * Vérifier si la demande peut être validée
     */
    public function canBeValidated()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Vérifier si la demande peut être traitée
     */
    public function canBeProcessed()
    {
        return $this->status === self::STATUS_VALIDATED;
    }
}
