@extends('layouts.admin')

@section('title', 'Nouvelle écriture')

@section('content')
{{-- Header glassy --}}
<div class="journal-glass-header">
    <div class="header-left">
        <a href="{{ route('admin.accounting.journal_entries.index') }}" class="glass-btn glass-back" title="Retour">
            <i class="fas fa-arrow-left"></i>
        </a>
        <span class="glass-icon"><i class="fas fa-pen-fancy"></i></span>
        <h1>Nouvelle écriture</h1>
    </div>
    <div class="header-actions">
        <button class="glass-btn glass-dark" id="toggle-darkmode" title="Mode sombre">
            <i class="fas fa-moon"></i>
        </button>
    </div>
</div>

{{-- Formulaire glassy --}}
<div class="glass-form-card">
    <form action="{{ route('admin.accounting.journal_entries.store') }}" method="POST">
        @csrf
        <div class="glass-form-group">
            <label for="date">Date</label>
            <input type="date" name="date" id="date" class="glass-input" required>
        </div>
        <div class="glass-form-group">
            <label for="reference">Référence</label>
            <input type="text" name="reference" id="reference" class="glass-input" placeholder="Ex: PIECE-2025-001" required>
        </div>
        <div class="glass-form-group">
            <label for="description">Description</label>
            <textarea name="description" id="description" class="glass-input" rows="2" placeholder="Description ou libellé"></textarea>
        </div>
        <div class="glass-form-group">
            <label for="account_id">Compte</label>
            <select name="account_id" id="account_id" class="glass-input" required>
                <option value="">Sélectionner un compte</option>
                @foreach($accounts as $account)
                    <option value="{{ $account->id }}">{{ $account->code }} - {{ $account->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="glass-form-row">
            <div class="glass-form-group">
                <label for="debit">Débit (FCFA)</label>
                <input type="number" name="debit" id="debit" class="glass-input" min="0" step="0.01">
            </div>
            <div class="glass-form-group">
                <label for="credit">Crédit (FCFA)</label>
                <input type="number" name="credit" id="credit" class="glass-input" min="0" step="0.01">
            </div>
        </div>
        <div class="glass-form-actions">
            <button type="submit" class="glass-btn glass-add"><i class="fas fa-check"></i> Valider</button>
            <a href="{{ route('admin.accounting.journal_entries.index') }}" class="glass-btn glass-cancel"><i class="fas fa-times"></i> Annuler</a>
        </div>
    </form>
</div>
@endsection

@push('styles')
<style>
body {
    background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
}
body.dark {
    background: linear-gradient(135deg, #232526 0%, #0f2027 100%);
}
.journal-glass-header {
    display: flex; justify-content: space-between; align-items: center;
    padding: 2rem 3rem; margin: 2rem auto 1rem auto; border-radius: 2rem;
    backdrop-filter: blur(12px); background: rgba(255,255,255,0.25);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.15);
    border: 1px solid rgba(255,255,255,0.18);
    max-width: 700px;
}
body.dark .journal-glass-header {
    background: rgba(30,34,53,0.35);
    color: #eee;
}
.glass-icon {
    font-size: 2rem; margin-right: 1rem; color: #6366f1;
}
.header-left { display: flex; align-items: center; gap: 1rem; }
.header-actions { display: flex; gap: 1rem; }
.glass-btn {
    border: none; border-radius: 50%; padding: 1rem; background: rgba(255,255,255,0.3);
    box-shadow: 0 2px 8px #6366f155; color: #6366f1; font-size: 1.2rem;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s;
    cursor: pointer;
    display: inline-flex; align-items: center; justify-content: center;
}
.glass-btn:hover { background: #6366f1; color: #fff; }
.glass-add { background: linear-gradient(135deg, #34d399 0%, #6366f1 100%); color: #fff; }
.glass-add:hover { box-shadow: 0 4px 16px #34d39955; }
.glass-dark { background: linear-gradient(135deg, #818cf8 0%, #0ea5e9 100%); color: #fff; }
.glass-back { background: #fff; color: #6366f1; border: 1px solid #6366f1; }
.glass-back:hover { background: #6366f1; color: #fff; }
.glass-form-card {
    max-width: 540px; margin: 2rem auto; padding: 2.5rem 2rem;
    border-radius: 2.5rem; background: rgba(255,255,255,0.25);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.15);
    backdrop-filter: blur(12px); border: 1px solid rgba(255,255,255,0.18);
}
body.dark .glass-form-card {
    background: rgba(30,34,53,0.55);
    color: #eee;
}
.glass-form-group {
    display: flex; flex-direction: column; margin-bottom: 1.6rem;
}
.glass-form-row {
    display: flex; gap: 1.2rem;
}
.glass-input {
    padding: 1rem; border-radius: 1.2rem; border: none;
    background: rgba(255,255,255,0.35); box-shadow: 0 2px 8px #6366f122;
    font-size: 1.05rem; margin-top: 0.3rem; outline: none; color: #222;
    transition: background 0.2s, box-shadow 0.2s;
}
.glass-input:focus {
    background: #6366f1; color: #fff; box-shadow: 0 4px 16px #6366f155;
}
body.dark .glass-input {
    background: rgba(30,34,53,0.35); color: #eee;
}
.glass-form-actions {
    display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;
}
.glass-btn.glass-cancel {
    background: linear-gradient(135deg, #fca5a5 0%, #818cf8 100%); color: #fff;
}
.glass-btn.glass-cancel:hover { background: #ef4444; color: #fff; }
</style>
@endpush

@push('scripts')
<script>
document.getElementById('toggle-darkmode').onclick = function() {
    document.body.classList.toggle('dark');
    localStorage.setItem('darkmode', document.body.classList.contains('dark'));
};
window.onload = function() {
    if(localStorage.getItem('darkmode') === 'true') document.body.classList.add('dark');
};
</script>
@endpush
