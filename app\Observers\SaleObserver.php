<?php

namespace App\Observers;

use App\Models\Sale;
use App\Models\User;
use App\Notifications\SaleNotification;
use Illuminate\Support\Facades\Notification;

class SaleObserver
{
    public function created(Sale $sale)
    {
        $admins = User::role('admin')->get();
        $managers = User::role('manager')->get();

        $recipients = $admins->merge($managers);

        Notification::send($recipients, new SaleNotification($sale));
    }
}
