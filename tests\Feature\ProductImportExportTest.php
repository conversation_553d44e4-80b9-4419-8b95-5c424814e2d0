<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Shop;
use App\Models\Stock;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ProductsImport;
use App\Exports\ProductsExport;
use App\Exports\StocksExport;
use App\Exports\StockMovementsExport;

class ProductImportExportTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;
    protected $shop;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create()->assignRole('admin');
        $this->category = Category::factory()->create();
        $this->shop = Shop::factory()->create();
    }

    /** @test */
    public function admin_can_download_import_template()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('products.import.template'));

        $response->assertStatus(200)
            ->assertHeader('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /** @test */
    public function admin_can_import_products()
    {
        Storage::fake('local');
        Excel::fake();

        $file = UploadedFile::fake()->create('products.xlsx');

        $response = $this->actingAs($this->admin)
            ->post(route('products.import'), [
                'file' => $file,
                'category_id' => $this->category->id
            ]);

        Excel::assertImported('products.xlsx');
        
        $response->assertRedirect(route('products.index'));
    }

    /** @test */
    public function import_validates_file_type()
    {
        Storage::fake('local');

        $file = UploadedFile::fake()->create('products.txt');

        $response = $this->actingAs($this->admin)
            ->post(route('products.import'), [
                'file' => $file,
                'category_id' => $this->category->id
            ]);

        $response->assertSessionHasErrors('file');
    }

    /** @test */
    public function admin_can_export_product_catalogue()
    {
        Excel::fake();

        $products = Product::factory(5)->create([
            'category_id' => $this->category->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.export.catalogue'));

        Excel::assertDownloaded('catalogue_produits_*.xlsx', function($export) {
            return $export instanceof ProductsExport;
        });
    }

    /** @test */
    public function admin_can_export_stocks()
    {
        Excel::fake();

        $product = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        Stock::factory()->create([
            'product_id' => $product->id,
            'shop_id' => $this->shop->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.export.stocks'));

        Excel::assertDownloaded('stocks_*.xlsx', function($export) {
            return $export instanceof StocksExport;
        });
    }

    /** @test */
    public function admin_can_export_stock_movements()
    {
        Excel::fake();

        $product = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $stock = Stock::factory()->create([
            'product_id' => $product->id,
            'shop_id' => $this->shop->id
        ]);

        StockMovement::factory(3)->create([
            'stock_id' => $stock->id,
            'user_id' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.export.movements'));

        Excel::assertDownloaded('mouvements_stock_*.xlsx', function($export) {
            return $export instanceof StockMovementsExport;
        });
    }

    /** @test */
    public function export_can_be_filtered_by_date_range()
    {
        Excel::fake();

        $response = $this->actingAs($this->admin)
            ->get(route('products.export.movements', [
                'date_start' => now()->subDays(7)->format('Y-m-d'),
                'date_end' => now()->format('Y-m-d')
            ]));

        $response->assertStatus(200);
        
        Excel::assertDownloaded('mouvements_stock_*.xlsx');
    }

    /** @test */
    public function export_stocks_can_be_filtered_by_status()
    {
        Excel::fake();

        $response = $this->actingAs($this->admin)
            ->get(route('products.export.stocks', [
                'stock_status' => 'low'
            ]));

        $response->assertStatus(200);
        
        Excel::assertDownloaded('stocks_*.xlsx');
    }

    /** @test */
    public function import_handles_duplicate_skus()
    {
        Storage::fake('local');
        Excel::fake();

        $existingProduct = Product::factory()->create([
            'category_id' => $this->category->id,
            'sku' => 'TEST-001'
        ]);

        $file = UploadedFile::fake()->create('products.xlsx');

        $response = $this->actingAs($this->admin)
            ->post(route('products.import'), [
                'file' => $file,
                'category_id' => $this->category->id
            ]);

        $response->assertSessionHas('import_errors');
    }
}
