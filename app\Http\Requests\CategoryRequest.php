<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CategoryRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('categories')->ignore($this->category)
            ],
            'description' => ['nullable', 'string'],
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) {
                    if ($this->category && $value == $this->category->id) {
                        $fail('Une catégorie ne peut pas être sa propre catégorie parente.');
                    }
                }
            ],
            'status' => ['required', 'in:active,inactive'],
            'icon' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:1024']
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => 'Le nom de la catégorie est obligatoire.',
            'name.max' => 'Le nom de la catégorie ne peut pas dépasser 255 caractères.',
            'name.unique' => 'Ce nom de catégorie existe déjà.',
            'parent_id.exists' => 'La catégorie parente sélectionnée n\'existe pas.',
            'status.required' => 'Le statut est obligatoire.',
            'status.in' => 'Le statut sélectionné n\'est pas valide.',
            'icon.image' => 'Le fichier doit être une image.',
            'icon.mimes' => 'L\'icône doit être au format JPEG, PNG ou JPG.',
            'icon.max' => 'L\'icône ne doit pas dépasser 1Mo.'
        ];
    }
}
