<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer ou récupérer le rôle admin
        $role = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web',
        ]);

        // Créer l'utilisateur admin si inexistant
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => \Illuminate\Support\Facades\Hash::make('admin@2025'),
                'email_verified_at' => now(),
                'role' => 'admin',
                'status' => 'active',
            ]
        );

        // Assigner le rôle admin à l'utilisateur si non déjà assigné
        if (! $user->hasRole('admin')) {
            $user->assignRole($role);
        }

        // Donner toutes les permissions au rôle admin (crée les permissions si besoin)
        $allPermissions = \Spatie\Permission\Models\Permission::all();
        if ($allPermissions->count() > 0) {
            $role->syncPermissions($allPermissions);
        }

        // Appeler le TaxSeeder si il existe
        if (class_exists(TaxSeeder::class)) {
            $this->call(TaxSeeder::class);
        }
    }
}
