#CAHIER DE CHARGES OREMI

Voici le nom de ma structure "OREMI FRIGO". Nous vendons les produits congelés, tout ce qui concerne les poissons, les crustacés, les viandes congelées, les produits de fan Milk de l'eau minéral et en sachet. Nous avons aussi des annexes et je souhaite développer une application web complètement responsive qui permette de faire la gestion complète de mon activité en passant par l'approvisionnement, le stockage, les livraisons, les ventes, la facturation toute son état. Je souhaite aussi avoir des notifications par mail sur toutes les opérations de connexion des différents utilisateurs, de livraison, d'approvisionnement et de vente. Je souhaite aussi avoir une gestion optimale des différents utilisateurs, donc tu vas me fournir un espace d'administration très complètes avec toutes les fonctionnalités. Dans mes utilisateurs j'aurai des admins, des gérants pour chaque boutique, le facturier, le caissier, le magasinier et le comptable. L'admin coiffe toutes la gestion des utilisateurs, des fonctionnalités dans leur ensemble. Le gérant est la deuxième personnalité dans l'application, il voit tout ce que le facturier, le caissier, le magasinier et le facturier font; en bref il les contrôle. Le facturier reçoit la commande des client, il enregistre et il recopie la terminaison du numéro de facture sur un bou de papier au client que le client va présenter à la caisse pour payer. Le caissier récupère le bou de papier chez le client et reçoit l'argent puis valide la vente; une fois la vente finalisée par le paiement le facturier reçoit la confirmation puis procède à la livraison; il y'a possibilité de vendre à crédit et le paiement aussi peut se faire par chèque donc penses aussi implémenter ces parties. Le magasinier s'occupe des approvisionnements, la mise en stock et les inventaires des différents produits qui seront gérer dans l'application; après un approvisionnement une notification est envoyée au gérant et à l'admin et c'est l'admin qui doit valider l'approvisionnement avant que la quantité soit ajoutée au stock; après un inventaire une notification est envoyée au gérant et à l'admin et c'est l'admin qui doit valider l'inventaire avant que les quantités soient régularisées au stock. Le comptable gère tout ce qui concerne les paperasses de la structure; donc gère cette partie aussi. Je souhaites aussi que tu apportes ton expertise pour ajouter toutes les fonctionnalités nécessaires pour rendre l'application très dynamique et performante surtout futuriste. Je souhaite utiliser le Framework Laravel avec toute la sécurité nécessaire surtout il faut que toute l'application soit sécurisée et complètement responsive.