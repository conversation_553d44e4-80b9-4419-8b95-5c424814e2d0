<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'shop_id' => 'required|exists:shops,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'payment_method' => 'required|in:cash,card,transfer,credit',
            'payment_status' => 'required|in:paid,pending,cancelled',
            'notes' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0'
        ];
    }

    public function messages(): array
    {
        return [
            'shop_id.required' => 'La boutique est obligatoire',
            'shop_id.exists' => 'La boutique sélectionnée n\'existe pas',
            'customer_name.required' => 'Le nom du client est obligatoire',
            'customer_name.max' => 'Le nom du client ne peut pas dépasser 255 caractères',
            'customer_phone.max' => 'Le numéro de téléphone ne peut pas dépasser 20 caractères',
            'payment_method.required' => 'Le mode de paiement est obligatoire',
            'payment_method.in' => 'Le mode de paiement doit être espèces, carte, virement ou crédit',
            'payment_status.required' => 'Le statut du paiement est obligatoire',
            'payment_status.in' => 'Le statut du paiement doit être payé, en attente ou annulé',
            'notes.max' => 'Les notes ne peuvent pas dépasser 1000 caractères',
            'items.required' => 'Au moins un produit est requis',
            'items.min' => 'Au moins un produit est requis',
            'items.*.product_id.required' => 'Le produit est obligatoire',
            'items.*.product_id.exists' => 'Le produit sélectionné n\'existe pas',
            'items.*.quantity.required' => 'La quantité est obligatoire',
            'items.*.quantity.integer' => 'La quantité doit être un nombre entier',
            'items.*.quantity.min' => 'La quantité doit être supérieure à 0',
            'items.*.unit_price.required' => 'Le prix unitaire est obligatoire',
            'items.*.unit_price.numeric' => 'Le prix unitaire doit être un nombre',
            'items.*.unit_price.min' => 'Le prix unitaire doit être supérieur ou égal à 0'
        ];
    }
}
