<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Product;
use App\Models\Category;
use App\Models\Stock;
use App\Models\Shop;
use App\Models\Brand;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductTest extends TestCase
{
    use RefreshDatabase;

    private $product;
    private $category;
    private $brand;
    private $supplier;

    protected function setUp(): void
    {
        parent::setUp();

        $this->category = Category::factory()->create();
        $this->brand = Brand::factory()->create();
        $this->supplier = Supplier::factory()->create();
        
        $this->product = Product::factory()->create([
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'supplier_id' => $this->supplier->id,
            'name' => 'Test Product',
            'sku' => 'TEST-001',
            'barcode' => '1234567890123',
            'description' => 'Test product description',
            'unit_price' => 1000,
            'selling_price' => 1500,
            'min_stock' => 10,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_belongs_to_a_category()
    {
        $this->assertInstanceOf(Category::class, $this->product->category);
        $this->assertEquals($this->category->id, $this->product->category->id);
    }

    /** @test */
    public function it_belongs_to_a_brand()
    {
        $this->assertInstanceOf(Brand::class, $this->product->brand);
        $this->assertEquals($this->brand->id, $this->product->brand->id);
    }

    /** @test */
    public function it_belongs_to_a_supplier()
    {
        $this->assertInstanceOf(Supplier::class, $this->product->supplier);
        $this->assertEquals($this->supplier->id, $this->product->supplier->id);
    }

    /** @test */
    public function it_has_many_stocks()
    {
        $shop = Shop::factory()->create();
        $stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $shop->id,
            'quantity' => 20
        ]);

        $this->assertTrue($this->product->stocks->contains($stock));
        $this->assertInstanceOf(Stock::class, $this->product->stocks->first());
    }

    /** @test */
    public function it_can_check_if_stock_is_low()
    {
        $shop = Shop::factory()->create();
        Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $shop->id,
            'quantity' => 5 // Below min_stock (10)
        ]);

        $this->assertTrue($this->product->hasLowStock());

        Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $shop->id,
            'quantity' => 15 // Above min_stock (10)
        ]);

        $this->product->refresh();
        $this->assertFalse($this->product->hasLowStock());
    }

    /** @test */
    public function it_can_calculate_total_stock()
    {
        $shop1 = Shop::factory()->create();
        $shop2 = Shop::factory()->create();

        Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $shop1->id,
            'quantity' => 20
        ]);

        Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $shop2->id,
            'quantity' => 30
        ]);

        $this->assertEquals(50, $this->product->totalStock());
    }

    /** @test */
    public function it_can_calculate_profit_margin()
    {
        $this->assertEquals(500, $this->product->profitMargin());
        $this->assertEquals(33.33, $this->product->profitMarginPercentage());
    }

    /** @test */
    public function it_generates_unique_sku()
    {
        $product1 = Product::factory()->create(['name' => 'Test Product One']);
        $product2 = Product::factory()->create(['name' => 'Test Product One']);

        $this->assertNotEquals($product1->sku, $product2->sku);
    }

    /** @test */
    public function it_validates_barcode_format()
    {
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        Product::factory()->create([
            'barcode' => '123456' // Invalid barcode length
        ]);
    }

    /** @test */
    public function it_can_be_searched_by_name_or_sku()
    {
        $searchResults = Product::search('Test')->get();
        $this->assertTrue($searchResults->contains($this->product));

        $searchResults = Product::search('TEST-001')->get();
        $this->assertTrue($searchResults->contains($this->product));
    }

    /** @test */
    public function it_can_be_filtered_by_category()
    {
        $filteredProducts = Product::byCategory($this->category->id)->get();
        $this->assertTrue($filteredProducts->contains($this->product));
    }

    /** @test */
    public function it_can_be_filtered_by_price_range()
    {
        $filteredProducts = Product::priceRange(800, 2000)->get();
        $this->assertTrue($filteredProducts->contains($this->product));

        $filteredProducts = Product::priceRange(2000, 3000)->get();
        $this->assertFalse($filteredProducts->contains($this->product));
    }
}
