<?php

namespace App\Notifications;

use App\Models\Supply;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;

class SupplyValidationNotification extends Notification
{
    use Queueable;

    protected $supply;

    /**
     * Créer une nouvelle instance de notification
     */
    public function __construct(Supply $supply)
    {
        $this->supply = $supply;
    }

    /**
     * Obtenir les canaux de livraison de la notification
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Obtenir la représentation mail de la notification
     */
    public function toMail($notifiable)
    {
        $url = route('supplies.show', $this->supply->id);

        return (new MailMessage)
            ->subject('Approvisionnement Validé - ' . $this->supply->reference_number)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('L\'approvisionnement ' . $this->supply->reference_number . ' a été validé.')
            ->line('Fournisseur : ' . $this->supply->supplier->name)
            ->line('Boutique : ' . $this->supply->shop->name)
            ->line('Montant total : ' . number_format($this->supply->total_amount, 2) . ' €')
            ->action('Voir les détails', $url)
            ->line('Vous pouvez maintenant procéder à la réception des produits.');
    }

    /**
     * Obtenir la représentation array de la notification
     */
    public function toArray($notifiable)
    {
        return [
            'supply_id' => $this->supply->id,
            'reference_number' => $this->supply->reference_number,
            'supplier_name' => $this->supply->supplier->name,
            'shop_name' => $this->supply->shop->name,
            'total_amount' => $this->supply->total_amount,
            'validator_name' => $this->supply->validator->name,
            'type' => 'supply_validation'
        ];
    }
}
