<?php

namespace Database\Factories;

use App\Models\Stock;
use App\Models\Shop;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class StockFactory extends Factory
{
    protected $model = Stock::class;

    public function definition()
    {
        return [
            'product_id' => Product::factory(),
            'shop_id' => Shop::factory(),
            'quantity' => fake()->numberBetween(1, 1000),
            'type' => fake()->randomElement(['supply', 'sale']),
            'status' => fake()->randomElement(['pending', 'completed', 'cancelled']),
            'notes' => fake()->sentence(),
        ];
    }

    public function withProduct(Product $product)
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $product->id,
        ]);
    }

    public function withShop(Shop $shop)
    {
        return $this->state(fn (array $attributes) => [
            'shop_id' => $shop->id,
        ]);
    }

    public function supply()
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'supply',
        ]);
    }

    public function sale()
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'sale',
        ]);
    }

    public function completed()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    public function cancelled()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }
}
