@extends('layouts.admin')

@section('title', 'Gestion des alertes')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Alertes</h2>
                <button id="mark-all-read" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-check-double mr-2"></i> Marquer tout comme lu
                </button>
            </div>
        </div>
    </div>

    <!-- Boutons actions sélection -->
    <div class="flex gap-2 px-6 py-3 bg-gray-50 border-b rounded-lg shadow">
        <button id="mark-selected-read" class="hidden px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition disabled:opacity-50" disabled>
            <i class="fas fa-check mr-2"></i> Marquer sélection comme lue
        </button>
        <button id="delete-selected" class="hidden px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition disabled:opacity-50" disabled>
            <i class="fas fa-trash mr-2"></i> Supprimer sélection
        </button>
    </div>

    <!-- Liste des alertes -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utilisateur
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Message
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($alerts as $alert)
                        <tr class="hover:bg-gray-50 {{ $alert->read_at ? 'bg-gray-50' : '' }}" data-id="{{ $alert->id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="alert_ids[]" value="{{ $alert->id }}" class="alert-checkbox rounded border-gray-300 text-blue-600">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $alert->created_at->format('d/m/Y H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $alert->user->name ?? 'Système' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($alert->level == 'info')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <i class="fas fa-info-circle mr-1"></i> Info
                                    </span>
                                @elseif($alert->level == 'warning')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i> Avertissement
                                    </span>
                                @elseif($alert->level == 'danger')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Critique
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="fas fa-bell mr-1"></i> Notification
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="flex items-center">
                                    @if($alert->title)
                                        <strong class="mr-1">{{ $alert->title }}:</strong>
                                    @endif
                                    {{ $alert->message }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($alert->read_at)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="fas fa-check mr-1"></i> Lu
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <i class="fas fa-envelope mr-1"></i> Non lu
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <button type="button" class="delete-alert-btn text-red-600 hover:text-red-800" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-bell-slash text-4xl mb-3 text-gray-300"></i>
                                    <p>Aucune alerte à afficher</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50">
            {{ $alerts->links() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments DOM
    const selectAllCheckbox = document.getElementById('select-all');
    const alertCheckboxes = document.querySelectorAll('.alert-checkbox');
    const markSelectedBtn = document.getElementById('mark-selected-read');
    const markAllBtn = document.getElementById('mark-all-read');
    const deleteSelectedBtn = document.getElementById('delete-selected');
    
    // Fonction pour mettre à jour les boutons d'action
    function updateActionButtons() {
        const checkedCount = document.querySelectorAll('.alert-checkbox:checked').length;
        if (checkedCount > 0) {
            markSelectedBtn.classList.remove('hidden');
            deleteSelectedBtn.classList.remove('hidden');
            markSelectedBtn.disabled = false;
            deleteSelectedBtn.disabled = false;
        } else {
            markSelectedBtn.classList.add('hidden');
            deleteSelectedBtn.classList.add('hidden');
            markSelectedBtn.disabled = true;
            deleteSelectedBtn.disabled = true;
        }
    }
    
    // Sélectionner/désélectionner tout
    selectAllCheckbox.addEventListener('change', function() {
        alertCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateActionButtons();
    });
    
    // Mettre à jour les boutons quand une case est cochée/décochée
    alertCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateActionButtons);
    });
    
    // Marquer les alertes sélectionnées comme lues
    markSelectedBtn.addEventListener('click', function() {
        const ids = Array.from(document.querySelectorAll('.alert-checkbox:checked')).map(cb => cb.value);
        if (ids.length === 0) return;
        
        Swal.fire({
            title: 'Marquer comme lu ?',
            text: 'Voulez-vous marquer ces alertes comme lues ?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Oui, marquer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch("{{ url('/admin/alerts/mark-as-read') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ alert_ids: ids })
                }).then(r => r.json()).then(data => {
                    // Mise à jour de l'interface
                    ids.forEach(id => {
                        const row = document.querySelector(`tr[data-id="${id}"]`);
                        if (row) {
                            row.classList.add('bg-gray-50');
                            const statusCell = row.querySelector('td:nth-child(6)');
                            if (statusCell) {
                                statusCell.innerHTML = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><i class="fas fa-check mr-1"></i> Lu</span>`;
                            }
                        }
                    });
                    
                    // Notification de succès
                    Swal.fire({
                        title: 'Succès!',
                        text: 'Les alertes ont été marquées comme lues.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Réinitialiser les cases à cocher
                    selectAllCheckbox.checked = false;
                    alertCheckboxes.forEach(cb => cb.checked = false);
                    updateActionButtons();
                }).catch(error => {
                    console.error('Erreur:', error);
                    Swal.fire('Erreur', 'Une erreur est survenue', 'error');
                });
            }
        });
    });
    
    // Marquer toutes les alertes comme lues
    markAllBtn.addEventListener('click', function() {
        const ids = Array.from(document.querySelectorAll('.alert-checkbox')).map(cb => cb.value);
        if (ids.length === 0) return;
        
        Swal.fire({
            title: 'Tout marquer comme lu ?',
            text: 'Voulez-vous marquer toutes les alertes comme lues ?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Oui, tout marquer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch("{{ url('/admin/alerts/mark-as-read') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ alert_ids: ids })
                }).then(r => r.json()).then(data => {
                    // Mise à jour de l'interface
                    document.querySelectorAll('tr[data-id]').forEach(row => {
                        row.classList.add('bg-gray-50');
                        const statusCell = row.querySelector('td:nth-child(6)');
                        if (statusCell) {
                            statusCell.innerHTML = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><i class="fas fa-check mr-1"></i> Lu</span>`;
                        }
                    });
                    
                    // Notification de succès
                    Swal.fire({
                        title: 'Succès!',
                        text: 'Toutes les alertes ont été marquées comme lues.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        // Recharger la page pour mettre à jour la pagination
                        window.location.reload();
                    });
                }).catch(error => {
                    console.error('Erreur:', error);
                    Swal.fire('Erreur', 'Une erreur est survenue', 'error');
                });
            }
        });
    });
    
    // Supprimer les alertes sélectionnées
    deleteSelectedBtn.addEventListener('click', function() {
        const ids = Array.from(document.querySelectorAll('.alert-checkbox:checked')).map(cb => cb.value);
        if (ids.length === 0) return;
        
        Swal.fire({
            title: 'Supprimer ?',
            text: 'Voulez-vous vraiment supprimer ces alertes ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch("{{ url('/admin/alerts/delete-multiple') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ alert_ids: ids })
                }).then(r => r.json()).then(data => {
                    // Supprimer les lignes du tableau
                    ids.forEach(id => {
                        const row = document.querySelector(`tr[data-id="${id}"]`);
                        if (row) row.remove();
                    });
                    
                    // Notification de succès
                    Swal.fire({
                        title: 'Succès!',
                        text: 'Les alertes ont été supprimées.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Réinitialiser les cases à cocher
                    selectAllCheckbox.checked = false;
                    updateActionButtons();
                    
                    // Si toutes les alertes ont été supprimées, afficher le message "Aucune alerte"
                    if (document.querySelectorAll('tr[data-id]').length === 0) {
                        const tbody = document.querySelector('tbody');
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                    <div class="flex flex-col items-center">
                                        <i class="fas fa-bell-slash text-4xl mb-3 text-gray-300"></i>
                                        <p>Aucune alerte à afficher</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }
                }).catch(error => {
                    console.error('Erreur:', error);
                    Swal.fire('Erreur', 'Une erreur est survenue', 'error');
                });
            }
        });
    });
    
    // Supprimer une alerte individuelle
    const deleteBtns = document.querySelectorAll('.delete-alert-btn');
    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('tr');
            const alertId = row.getAttribute('data-id');
            
            Swal.fire({
                title: 'Supprimer ?',
                text: 'Voulez-vous vraiment supprimer cette alerte ?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`{{ url('/admin/alerts') }}/${alertId}`, {
                        method: 'DELETE',
                        headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' }
                    }).then(r => r.json()).then(data => {
                        row.remove();
                        
                        // Notification de succès
                        Swal.fire({
                            title: 'Succès!',
                            text: 'L\'alerte a été supprimée.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        
                        // Si toutes les alertes ont été supprimées, afficher le message "Aucune alerte"
                        if (document.querySelectorAll('tr[data-id]').length === 0) {
                            const tbody = document.querySelector('tbody');
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-bell-slash text-4xl mb-3 text-gray-300"></i>
                                            <p>Aucune alerte à afficher</p>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }
                    }).catch(error => {
                        console.error('Erreur:', error);
                        Swal.fire('Erreur', 'Une erreur est survenue', 'error');
                    });
                }
            });
        });
    });
});
</script>
@endpush
