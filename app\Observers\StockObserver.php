<?php

namespace App\Observers;

use App\Models\Stock;
use App\Models\User;
use App\Notifications\StockSupplyNotification;

class StockObserver
{
    public function created(Stock $stock)
    {
        if ($stock->type === 'supply') {
            $admins = User::role('admin')->get();
            $managers = User::role('manager')->get();
            
            $recipients = $admins->merge($managers)->unique('id');
            
            foreach ($recipients as $recipient) {
                $recipient->notify(new StockSupplyNotification($stock));
            }
        }
    }
}
