<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Stock;
use App\Models\Product;
use App\Models\Shop;
use App\Models\User;
use App\Models\StockMovement;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StockTest extends TestCase
{
    use RefreshDatabase;

    private $stock;
    private $product;
    private $shop;

    protected function setUp(): void
    {
        parent::setUp();

        $this->product = Product::factory()->create();
        $this->shop = Shop::factory()->create();
        
        $this->stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->shop->id,
            'quantity' => 100,
            'min_stock' => 20,
            'location' => 'A-01-02'
        ]);
    }

    /** @test */
    public function it_belongs_to_a_product()
    {
        $this->assertInstanceOf(Product::class, $this->stock->product);
        $this->assertEquals($this->product->id, $this->stock->product->id);
    }

    /** @test */
    public function it_belongs_to_a_shop()
    {
        $this->assertInstanceOf(Shop::class, $this->stock->shop);
        $this->assertEquals($this->shop->id, $this->stock->shop->id);
    }

    /** @test */
    public function it_has_many_stock_movements()
    {
        $user = User::factory()->create();
        $movement = StockMovement::factory()->create([
            'stock_id' => $this->stock->id,
            'user_id' => $user->id,
            'quantity' => 10,
            'type' => 'in'
        ]);

        $this->assertTrue($this->stock->movements->contains($movement));
        $this->assertInstanceOf(StockMovement::class, $this->stock->movements->first());
    }

    /** @test */
    public function it_can_check_if_stock_is_low()
    {
        $this->assertFalse($this->stock->isLow());

        $this->stock->update(['quantity' => 15]); // Below min_stock (20)
        $this->assertTrue($this->stock->isLow());
    }

    /** @test */
    public function it_can_add_stock()
    {
        $user = User::factory()->create();
        $this->stock->add(50, $user->id, 'Approvisionnement');

        $this->assertEquals(150, $this->stock->fresh()->quantity);
        $this->assertDatabaseHas('stock_movements', [
            'stock_id' => $this->stock->id,
            'quantity' => 50,
            'type' => 'in'
        ]);
    }

    /** @test */
    public function it_can_remove_stock()
    {
        $user = User::factory()->create();
        $this->stock->remove(30, $user->id, 'Vente');

        $this->assertEquals(70, $this->stock->fresh()->quantity);
        $this->assertDatabaseHas('stock_movements', [
            'stock_id' => $this->stock->id,
            'quantity' => -30,
            'type' => 'out'
        ]);
    }

    /** @test */
    public function it_prevents_negative_stock()
    {
        $user = User::factory()->create();
        $this->expectException(\Exception::class);
        
        $this->stock->remove(150, $user->id, 'Vente'); // More than available
    }

    /** @test */
    public function it_can_adjust_stock()
    {
        $user = User::factory()->create();
        $this->stock->adjust(80, $user->id, 'Inventaire');

        $this->assertEquals(80, $this->stock->fresh()->quantity);
        $this->assertDatabaseHas('stock_movements', [
            'stock_id' => $this->stock->id,
            'type' => 'adjustment'
        ]);
    }

    /** @test */
    public function it_can_transfer_stock()
    {
        $user = User::factory()->create();
        $destinationShop = Shop::factory()->create();
        $destinationStock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $destinationShop->id,
            'quantity' => 0
        ]);

        $this->stock->transfer(30, $destinationStock, $user->id);

        $this->assertEquals(70, $this->stock->fresh()->quantity);
        $this->assertEquals(30, $destinationStock->fresh()->quantity);
    }

    /** @test */
    public function it_can_get_movement_history()
    {
        $user = User::factory()->create();
        
        $this->stock->add(50, $user->id, 'Approvisionnement');
        $this->stock->remove(20, $user->id, 'Vente');
        $this->stock->adjust(120, $user->id, 'Inventaire');

        $history = $this->stock->movementHistory();
        
        $this->assertEquals(3, $history->count());
    }

    /** @test */
    public function it_can_calculate_value()
    {
        $this->assertEquals(
            $this->stock->quantity * $this->product->unit_price,
            $this->stock->value()
        );
    }
}
