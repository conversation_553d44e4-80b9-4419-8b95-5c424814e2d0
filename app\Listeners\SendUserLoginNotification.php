<?php

namespace App\Listeners;

use App\Events\UserLoggedIn;
use App\Models\User;
use App\Notifications\UserLoginNotification;
use Illuminate\Support\Facades\Notification;

class SendUserLoginNotification
{
    public function handle(UserLoggedIn $event)
    {
        $admins = User::role('admin')->get();
        $managers = User::role('manager')->get();

        $recipients = $admins->merge($managers);

        Notification::send($recipients, new UserLoginNotification($event->user));
    }
}
