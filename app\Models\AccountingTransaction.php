<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountingTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'shop_id',
        'transaction_type',
        'amount',
        'reference_type',
        'reference_id',
        'description',
        'transaction_date',
        'payment_method',
        'status',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'transaction_date' => 'date',
        'amount' => 'decimal:2'
    ];

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function reference()
    {
        return $this->morphTo();
    }

    public function category()
    {
        return $this->belongsTo(AccountingCategory::class);
    }

    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }
}
