<?php

namespace App\Exports;

use App\Models\StockMovement;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StockMovementsExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    public function query()
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Boutique',
            'SKU',
            'Produit',
            'Type',
            'Quantité',
            'Stock final',
            'Utilisateur',
            'Description'
        ];
    }

    public function map($movement): array
    {
        $typeLabels = [
            'in' => 'Entrée',
            'out' => 'Sortie',
            'adjustment' => 'Ajustement',
            'transfer' => 'Transfert'
        ];

        return [
            $movement->created_at->format('d/m/Y H:i'),
            $movement->stock->shop->name,
            $movement->stock->product->sku,
            $movement->stock->product->name,
            $typeLabels[$movement->type] ?? $movement->type,
            $movement->quantity,
            $movement->final_quantity,
            $movement->user->name,
            $movement->description
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'F:G' => ['numberFormat' => ['formatCode' => '#,##0']],
        ];
    }
}
