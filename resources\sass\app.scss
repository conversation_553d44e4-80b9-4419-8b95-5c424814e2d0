// Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

// Variables
@import 'variables';

// Bootstrap
@import 'bootstrap/scss/bootstrap';

// Font Awesome
// @import '@fortawesome/fontawesome-free/scss/fontawesome';
// @import '@fortawesome/fontawesome-free/scss/regular';
// @import '@fortawesome/fontawesome-free/scss/solid';
// @import '@fortawesome/fontawesome-free/scss/brands';

// Tailwind CSS
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

// Custom styles
.navbar-brand {
    font-weight: 600;
}

.nav-link {
    font-weight: 500;
}

.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;

    .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

.btn {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
}

.table {
    th {
        font-weight: 600;
        background-color: #f8f9fa;
    }
}

.alert {
    border-radius: 0.375rem;
    border: none;
    padding: 1rem;
    margin-bottom: 1rem;
}

// Dashboard styles
.stats-card {
    background: linear-gradient(45deg, #4b6cb7, #182848);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;

    .stats-title {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .stats-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0.5rem 0;
    }

    .stats-change {
        font-size: 0.875rem;
        opacity: 0.8;

        &.positive {
            color: #4caf50;
        }

        &.negative {
            color: #f44336;
        }
    }
}

// Forms
.form-control {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

// Pagination
.pagination {
    .page-link {
        padding: 0.5rem 0.75rem;
        margin: 0 0.25rem;
        border-radius: 0.375rem;
        border: none;
        color: #4b6cb7;

        &:hover {
            background-color: #e9ecef;
        }

        &.active {
            background-color: #4b6cb7;
            color: white;
        }
    }
}
