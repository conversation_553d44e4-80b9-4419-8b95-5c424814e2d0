<?php

namespace App\Http\Controllers\Storekeeper;

use App\Http\Controllers\Controller;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Stock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Events\InventoryCompleted;

class InventoryController extends Controller
{
    public function index()
    {
        $inventories = Inventory::with('user')
            ->where('shop_id', auth()->user()->shop_id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('storekeeper.inventories.index', compact('inventories'));
    }

    public function create()
    {
        $products = Product::whereHas('stocks', function($query) {
            $query->where('shop_id', auth()->user()->shop_id);
        })->get();

        return view('storekeeper.inventories.create', compact('products'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.counted_quantity' => 'required|integer|min:0',
            'notes' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            // Créer l'inventaire
            $inventory = Inventory::create([
                'shop_id' => auth()->user()->shop_id,
                'user_id' => auth()->id(),
                'status' => 'pending',
                'notes' => $request->notes
            ]);

            // Ajouter les articles inventoriés
            foreach ($request->items as $item) {
                $stock = Stock::where('shop_id', auth()->user()->shop_id)
                    ->where('product_id', $item['product_id'])
                    ->first();

                $inventory->items()->create([
                    'product_id' => $item['product_id'],
                    'system_quantity' => $stock ? $stock->quantity : 0,
                    'counted_quantity' => $item['counted_quantity'],
                    'difference' => ($item['counted_quantity'] - ($stock ? $stock->quantity : 0))
                ]);
            }

            DB::commit();

            return redirect()
                ->route('storekeeper.inventories.show', $inventory)
                ->with('success', 'Inventaire créé avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la création de l\'inventaire.');
        }
    }

    public function show(Inventory $inventory)
    {
        $inventory->load(['items.product', 'user']);
        return view('storekeeper.inventories.show', compact('inventory'));
    }

    public function validate(Request $request, Inventory $inventory)
    {
        if ($inventory->status !== 'pending') {
            return back()->with('error', 'Cet inventaire ne peut plus être validé.');
        }

        try {
            DB::beginTransaction();

            // Mettre à jour les stocks
            foreach ($inventory->items as $item) {
                Stock::updateOrCreate(
                    [
                        'shop_id' => auth()->user()->shop_id,
                        'product_id' => $item->product_id
                    ],
                    ['quantity' => $item->counted_quantity]
                );
            }

            // Marquer l'inventaire comme complété
            $inventory->update([
                'status' => 'completed',
                'completed_at' => now(),
                'validated_by' => auth()->id()
            ]);

            DB::commit();

            // Déclencher l'événement d'inventaire complété
            event(new InventoryCompleted($inventory));

            return redirect()
                ->route('storekeeper.inventories.index')
                ->with('success', 'Inventaire validé avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la validation de l\'inventaire.');
        }
    }
}
