<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\JournalEntryLine;
use App\Models\User;

class JournalEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'date', 'reference', 'description', 'created_by'
    ];

    public function lines()
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
