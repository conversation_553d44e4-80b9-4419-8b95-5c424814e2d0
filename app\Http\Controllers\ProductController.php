<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Stock;
use App\Models\Shop;
use Illuminate\Http\Request;
use App\Http\Requests\ProductRequest;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'stocks']);

        // Filtrage par catégorie
        if ($request->has('category')) {
            $query->where('category_id', $request->category);
        }

        // Filtrage par statut de stock
        if ($request->has('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereHas('stocks', function ($q) {
                        $q->whereColumn('quantity', '<=', 'min_stock');
                    });
                    break;
                case 'out':
                    $query->whereHas('stocks', function ($q) {
                        $q->where('quantity', 0);
                    });
                    break;
                case 'available':
                    $query->whereHas('stocks', function ($q) {
                        $q->where('quantity', '>', 0);
                    });
                    break;
            }
        }

        // Recherche par nom ou SKU
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('sku', 'like', '%' . $request->search . '%')
                  ->orWhere('barcode', 'like', '%' . $request->search . '%');
            });
        }

        // Tri
        $sort = $request->sort ?? 'name';
        $direction = $request->direction ?? 'asc';
        $query->orderBy($sort, $direction);

        $products = $query->paginate(20);
        $categories = Category::orderBy('name')->get();

        return view('products.index', compact('products', 'categories'));
    }

    public function create()
    {
        $categories = Category::orderBy('name')->get();
        $shops = Shop::orderBy('name')->get();
        $suppliers = \App\Models\Supplier::orderBy('name')->get();

        return view('products.create', compact('categories', 'shops', 'suppliers'));
    }

    public function store(ProductRequest $request)
    {
        try {
            DB::beginTransaction();

            // Créer le produit
            $product = Product::create([
                'category_id' => $request->category_id,
                'name' => $request->name,
                'description' => $request->description,
                'sku' => $this->generateSKU($request->name),
                'barcode' => $request->barcode,
                'unit_price' => $request->unit_price,
                'selling_price' => $request->selling_price,
                'min_stock' => $request->min_stock,
                'status' => $request->status
            ]);

            // Gérer l'image du produit
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('products', 'public');
                $product->update(['image_path' => $path]);
            }

            // Créer les stocks initiaux pour chaque boutique
            foreach ($request->stocks as $shopId => $quantity) {
                if ($quantity > 0) {
                    Stock::create([
                        'product_id' => $product->id,
                        'shop_id' => $shopId,
                        'quantity' => $quantity,
                        'min_stock' => $request->min_stock
                    ]);
                }
            }

            DB::commit();

            return redirect()
                ->route('products.index')
                ->with('success', 'Produit créé avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de la création du produit.');
        }
    }

    public function show(Product $product)
    {
        $product->load(['category', 'stocks.shop', 'stocks.movements']);
        return view('products.show', compact('product'));
    }

    public function edit(Product $product)
    {
        $categories = Category::orderBy('name')->get();
        $shops = Shop::orderBy('name')->get();

        return view('products.edit', compact('product', 'categories', 'shops'));
    }

    public function update(ProductRequest $request, Product $product)
    {
        try {
            DB::beginTransaction();

            // Mettre à jour le produit
            $product->update([
                'category_id' => $request->category_id,
                'name' => $request->name,
                'description' => $request->description,
                'barcode' => $request->barcode,
                'unit_price' => $request->unit_price,
                'selling_price' => $request->selling_price,
                'min_stock' => $request->min_stock,
                'status' => $request->status
            ]);

            // Gérer l'image du produit
            if ($request->hasFile('image')) {
                // Supprimer l'ancienne image
                if ($product->image_path) {
                    Storage::disk('public')->delete($product->image_path);
                }
                
                $path = $request->file('image')->store('products', 'public');
                $product->update(['image_path' => $path]);
            }

            // Mettre à jour les stocks
            foreach ($request->stocks as $shopId => $quantity) {
                Stock::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'shop_id' => $shopId
                    ],
                    [
                        'min_stock' => $request->min_stock,
                        'quantity' => $quantity
                    ]
                );
            }

            DB::commit();

            return redirect()
                ->route('products.index')
                ->with('success', 'Produit mis à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de la mise à jour du produit.');
        }
    }

    public function destroy(Product $product)
    {
        try {
            // Vérifier si le produit a des ventes
            if ($product->sales()->exists()) {
                return back()->with('error', 'Ce produit ne peut pas être supprimé car il a des ventes associées.');
            }

            DB::beginTransaction();

            // Supprimer l'image
            if ($product->image_path) {
                Storage::disk('public')->delete($product->image_path);
            }

            // Supprimer les stocks et mouvements
            $product->stocks()->delete();
            $product->delete();

            DB::commit();

            return redirect()
                ->route('products.index')
                ->with('success', 'Produit supprimé avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la suppression du produit.');
        }
    }

    protected function generateSKU($name)
    {
        $prefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 3));
        $number = str_pad(Product::count() + 1, 4, '0', STR_PAD_LEFT);
        
        return $prefix . '-' . $number;
    }
}
