<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $payment;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Paiement Reçu - ' . $this->payment->sale->shop->name)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Un nouveau paiement a été reçu.')
            ->line('Détails du paiement :')
            ->line("Montant: " . number_format($this->payment->amount, 2) . " FCFA")
            ->line("Mode de paiement: {$this->payment->payment_method}")
            ->line("Boutique: {$this->payment->sale->shop->name}")
            ->line("Date: {$this->payment->created_at}")
            ->action('Voir les détails', url('/payments/' . $this->payment->id))
            ->line('Merci de votre attention !');
    }

    public function toArray($notifiable)
    {
        return [
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'payment_method' => $this->payment->payment_method,
            'shop_name' => $this->payment->sale->shop->name,
            'created_at' => $this->payment->created_at,
        ];
    }
}
