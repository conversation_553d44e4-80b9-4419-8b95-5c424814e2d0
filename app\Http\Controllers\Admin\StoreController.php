<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class StoreController extends Controller
{
    /**
     * Display a listing of the stores.
     */
    public function index()
    {
        $shops = Shop::paginate(10);
        return view('admin.shops.index', compact('shops'));
    }

    /**
     * Show the form for creating a new store.
     */
    public function create()
    {
        return view('admin.stores.create');
    }

    /**
     * Store a newly created store in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|unique:shops,code',
            'address' => 'required|string',
            'phone' => 'required|string',
            'email' => 'nullable|email',
            'is_main_shop' => 'boolean',
            'parent_shop_id' => [
                'nullable',
                'exists:shops,id',
                Rule::requiredIf(fn() => !$request->boolean('is_main_shop')),
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->boolean('is_main_shop') && $value !== null) {
                        $fail('Une boutique principale ne peut pas être une annexe.');
                    }
                }
            ]
        ]);

        $shop = Shop::create($validated);

        return redirect()
            ->route('admin.stores.index')
            ->with('success', 'Boutique créée avec succès.');
    }

    /**
     * Display the specified store.
     */
    public function show(Shop $shop)
    {
        return view('admin.stores.show', compact('shop'));
    }

    /**
     * Show the form for editing the specified store.
     */
    public function edit(Shop $shop)
    {
        return view('admin.stores.edit', compact('shop'));
    }

    /**
     * Update the specified store in storage.
     */
    public function update(Request $request, Shop $shop)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|unique:shops,code,'.$shop->id,
            'address' => 'required|string',
            'phone' => 'required|string',
            'email' => 'nullable|email',
            'is_main_shop' => 'boolean',
            'parent_shop_id' => [
                'nullable',
                'exists:shops,id',
                Rule::requiredIf(fn() => !$request->boolean('is_main_shop')),
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->boolean('is_main_shop') && $value !== null) {
                        $fail('Une boutique principale ne peut pas être une annexe.');
                    }
                }
            ]
        ]);

        $shop->update($validated);

        return redirect()->route('admin.stores.index')
            ->with('success', 'Boutique mise à jour avec succès.');
    }

    /**
     * Remove the specified store from storage.
     */
    public function destroy(Shop $shop)
    {
        $shop->delete();

        return redirect()->route('admin.stores.index')
            ->with('success', 'Boutique supprimée avec succès.');
    }
}
