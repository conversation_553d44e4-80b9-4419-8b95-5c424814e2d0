<?php

namespace App\Http\Controllers;

use App\Models\SupplyRequest;
use App\Models\Shop;
use App\Models\Stock;
use Illuminate\Http\Request;
use App\Http\Requests\SupplyRequestRequest;
use Illuminate\Support\Facades\DB;
use App\Notifications\SupplyRequestValidationNotification;
use App\Notifications\SupplyRequestProcessedNotification;

class SupplyRequestController extends Controller
{
    /**
     * Afficher la liste des demandes d'approvisionnement
     */
    public function index(Request $request)
    {
        $supplyRequests = SupplyRequest::with(['requestingShop', 'supplyingShop'])
            ->when($request->from_shop_id, function ($query, $shop_id) {
                return $query->where('from_shop_id', $shop_id);
            })
            ->when($request->to_shop_id, function ($query, $shop_id) {
                return $query->where('to_shop_id', $shop_id);
            })
            ->when($request->status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('supply-requests.index', compact('supplyRequests'));
    }

    /**
     * Afficher le formulaire de création d'une demande
     */
    public function create()
    {
        $shops = Shop::where('is_main', true)->get();
        return view('supply-requests.create', compact('shops'));
    }

    /**
     * Enregistrer une nouvelle demande
     */
    public function store(SupplyRequestRequest $request)
    {
        try {
            DB::beginTransaction();

            $supplyRequest = SupplyRequest::create([
                'from_shop_id' => $request->from_shop_id,
                'to_shop_id' => $request->to_shop_id,
                'reference_number' => $this->generateReferenceNumber(),
                'request_date' => now(),
                'required_date' => $request->required_date,
                'status' => SupplyRequest::STATUS_PENDING,
                'notes' => $request->notes
            ]);

            foreach ($request->items as $item) {
                $supplyRequest->items()->create([
                    'product_id' => $item['product_id'],
                    'requested_quantity' => $item['quantity']
                ]);
            }

            DB::commit();

            // Notifier la boutique principale
            $supplyRequest->supplyingShop->manager->notify(new SupplyRequestValidationNotification($supplyRequest));

            return redirect()
                ->route('supply-requests.show', $supplyRequest)
                ->with('success', 'Demande d\'approvisionnement créée avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la création de la demande');
        }
    }

    /**
     * Afficher les détails d'une demande
     */
    public function show(SupplyRequest $supplyRequest)
    {
        $supplyRequest->load(['requestingShop', 'supplyingShop', 'items.product']);
        return view('supply-requests.show', compact('supplyRequest'));
    }

    /**
     * Valider une demande d'approvisionnement
     */
    public function validate(SupplyRequest $supplyRequest)
    {
        if (!$supplyRequest->canBeValidated()) {
            return back()->with('error', 'Cette demande ne peut pas être validée');
        }

        try {
            DB::beginTransaction();

            $supplyRequest->update([
                'status' => SupplyRequest::STATUS_VALIDATED,
                'validated_by' => auth()->id()
            ]);

            DB::commit();

            return back()->with('success', 'Demande validée avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Erreur lors de la validation de la demande');
        }
    }

    /**
     * Traiter une demande d'approvisionnement
     */
    public function process(Request $request, SupplyRequest $supplyRequest)
    {
        if (!$supplyRequest->canBeProcessed()) {
            return back()->with('error', 'Cette demande ne peut pas être traitée');
        }

        try {
            DB::beginTransaction();

            foreach ($request->items as $itemData) {
                $item = $supplyRequest->items()->find($itemData['id']);
                if (!$item) continue;

                // Vérifier le stock disponible
                $sourceStock = Stock::where('product_id', $item->product_id)
                    ->where('shop_id', $supplyRequest->to_shop_id)
                    ->first();

                if (!$sourceStock || $sourceStock->quantity < $itemData['approved_quantity']) {
                    throw new \Exception("Stock insuffisant pour le produit {$item->product->name}");
                }

                // Mettre à jour les stocks
                $sourceStock->decrement('quantity', $itemData['approved_quantity']);

                $destinationStock = Stock::firstOrCreate([
                    'product_id' => $item->product_id,
                    'shop_id' => $supplyRequest->from_shop_id
                ]);

                $destinationStock->increment('quantity', $itemData['approved_quantity']);

                // Mettre à jour la quantité approuvée
                $item->update([
                    'approved_quantity' => $itemData['approved_quantity']
                ]);
            }

            $supplyRequest->update([
                'status' => SupplyRequest::STATUS_PROCESSED,
                'processed_by' => auth()->id()
            ]);

            // Notifier la boutique demandeuse
            $supplyRequest->requestingShop->manager->notify(
                new SupplyRequestProcessedNotification($supplyRequest)
            );

            DB::commit();

            return back()->with('success', 'Demande traitée avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Générer un numéro de référence unique
     */
    private function generateReferenceNumber()
    {
        $prefix = 'DAP';
        $year = date('Y');
        $month = date('m');
        $lastRequest = SupplyRequest::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->latest()
            ->first();

        $sequence = $lastRequest ? intval(substr($lastRequest->reference_number, -4)) + 1 : 1;

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $sequence);
    }
}
