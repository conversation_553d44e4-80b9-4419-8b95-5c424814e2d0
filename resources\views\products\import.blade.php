@extends('layouts.app')

@section('title', 'Import de produits')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Import de produits</h1>
        <div>
            <a href="{{ route('products.import.template') }}" class="btn btn-info">
                <i class="fas fa-download"></i> Télécharger le modèle
            </a>
            <a href="{{ route('products.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <!-- Instructions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Instructions d'import</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Comment importer des produits ?</h5>
                <ol class="mb-0">
                    <li>Téléchargez le modèle Excel en cliquant sur le bouton ci-dessus</li>
                    <li>Remplissez le fichier en respectant le format de chaque colonne :
                        <ul>
                            <li><strong>SKU</strong> : Code unique du produit (obligatoire)</li>
                            <li><strong>Nom</strong> : Nom du produit (obligatoire)</li>
                            <li><strong>Description</strong> : Description du produit (optionnel)</li>
                            <li><strong>Code-barres</strong> : Code-barres à 13 chiffres (optionnel)</li>
                            <li><strong>Prix d'achat</strong> : Prix d'achat unitaire (obligatoire)</li>
                            <li><strong>Prix de vente</strong> : Prix de vente unitaire (obligatoire)</li>
                            <li><strong>Stock minimum</strong> : Seuil d'alerte de stock (obligatoire)</li>
                            <li><strong>Statut</strong> : "Actif" ou "Inactif" (obligatoire)</li>
                            <li><strong>Stock boutique X</strong> : Stock initial pour chaque boutique (optionnel)</li>
                        </ul>
                    </li>
                    <li>Sélectionnez la catégorie pour tous les produits importés</li>
                    <li>Sélectionnez votre fichier Excel et cliquez sur "Importer"</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Formulaire d'import -->
    <div class="card shadow">
        <div class="card-body">
            <form action="{{ route('products.import') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="form-group">
                    <label for="category_id">Catégorie <span class="text-danger">*</span></label>
                    <select class="form-control @error('category_id') is-invalid @enderror" 
                            id="category_id" name="category_id" required>
                        <option value="">Sélectionner une catégorie</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                    {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="file">Fichier Excel <span class="text-danger">*</span></label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input @error('file') is-invalid @enderror" 
                               id="file" name="file" required
                               accept=".xlsx,.xls">
                        <label class="custom-file-label" for="file">Choisir un fichier</label>
                    </div>
                    <small class="form-text text-muted">
                        Formats acceptés : XLSX, XLS. Taille maximale : 10MB
                    </small>
                    @error('file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload"></i> Importer
                </button>
            </form>

            @if(session('import_errors'))
                <div class="alert alert-warning mt-4">
                    <h6><i class="fas fa-exclamation-triangle"></i> Erreurs d'import :</h6>
                    <ul class="mb-0">
                        @foreach(session('import_errors') as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Mise à jour du label du fichier sélectionné
document.querySelector('.custom-file-input').addEventListener('change', function(e) {
    var fileName = e.target.files[0].name;
    var label = e.target.nextElementSibling;
    label.innerHTML = fileName;
});

// Initialiser Select2
$(document).ready(function() {
    $('#category_id').select2({
        theme: 'bootstrap4',
        placeholder: 'Sélectionner une catégorie'
    });
});
</script>
@endpush
@endsection
