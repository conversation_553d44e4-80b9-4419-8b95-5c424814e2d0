@extends('layouts.admin')

@section('title', 'Éditer la catégorie')

@section('content')
<div class="max-w-2xl mx-auto mt-8 px-2 w-full">
    <div class="bg-white shadow-lg rounded-xl p-6 sm:p-10 border border-gray-100">
        <h2 class="text-2xl sm:text-3xl font-extrabold mb-6 text-blue-700 flex items-center gap-2">
            <i class="fas fa-utensils text-blue-500"></i>
            Modifier la catégorie
        </h2>
        <form method="POST" action="{{ route('categories.update', $category) }}" enctype="multipart/form-data" class="space-y-5 custom-form-borders">
            @csrf
            @method('PUT')
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label for="name" class="block text-sm font-semibold text-gray-700">Nom</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $category->name) }}" required
                           class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    @error('name')
                        <span class="text-red-600 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div>
                    <label for="parent_id" class="block text-sm font-semibold text-gray-700">Catégorie parente</label>
                    <select name="parent_id" id="parent_id" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Aucune</option>
                        @foreach($categories as $cat)
                            @if($cat->id !== $category->id)
                                <option value="{{ $cat->id }}" {{ old('parent_id', $category->parent_id) == $cat->id ? 'selected' : '' }}>{{ $cat->name }}</option>
                            @endif
                        @endforeach
                    </select>
                </div>
            </div>
            <div>
                <label for="description" class="block text-sm font-semibold text-gray-700">Description</label>
                <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('description', $category->description) }}</textarea>
                @error('description')
                    <span class="text-red-600 text-xs">{{ $message }}</span>
                @enderror
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700">Statut</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="active" {{ old('status', $category->status ?? $category->is_active ? 'active' : 'inactive') == 'active' ? 'selected' : '' }}>Actif</option>
                        <option value="inactive" {{ old('status', $category->status ?? $category->is_active ? 'active' : 'inactive') == 'inactive' ? 'selected' : '' }}>Inactif</option>
                    </select>
                </div>
                <div>
                    <label for="icon" class="block text-sm font-semibold text-gray-700">Icône</label>
                    <input type="file" name="icon" id="icon" class="mt-1 block w-full text-sm text-gray-500">
                    @if($category->icon_path)
                        <img src="{{ Storage::url($category->icon_path) }}" alt="Icône actuelle" class="h-12 w-12 mt-2 rounded-full object-cover border border-gray-200">
                    @endif
                </div>
            </div>
            <div class="flex flex-col sm:flex-row justify-end gap-3 pt-4">
                <a href="{{ route('categories.index') }}" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors text-center">Annuler</a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold flex items-center gap-2 justify-center">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Ajoute des bordures visibles aux champs de saisie du formulaire */
    .custom-form-borders input[type="text"],
    .custom-form-borders input[type="file"],
    .custom-form-borders select,
    .custom-form-borders textarea {
        border: 2px solid #2563eb !important; /* bleu, bien visible */
        box-shadow: none !important;
        background-color: #fff;
    }
    .custom-form-borders input[type="text"]:focus,
    .custom-form-borders input[type="file"]:focus,
    .custom-form-borders select:focus,
    .custom-form-borders textarea:focus {
        border-color: #1d4ed8 !important; /* bleu plus foncé au focus */
        outline: none !important;
    }
</style>
@endpush
