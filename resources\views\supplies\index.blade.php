@extends('layouts.app')

@section('title', 'Gestion des Approvisionnements')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Gestion des Approvisionnements</h1>
        @can('create', App\Models\Supply::class)
        <a href="{{ route('supplies.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouvel Approvisionnement
        </a>
        @endcan
    </div>

    <div class="card">
        <div class="card-header">
            <form action="{{ route('supplies.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="shop_id" class="form-label">Boutique</label>
                    <select name="shop_id" id="shop_id" class="form-select">
                        <option value="">Toutes les boutiques</option>
                        @foreach($shops as $shop)
                            <option value="{{ $shop->id }}" @selected(request('shop_id') == $shop->id)>
                                {{ $shop->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous les statuts</option>
                        @foreach(['pending' => 'En attente', 'validated' => 'Validé', 'received' => 'Reçu', 'partially_received' => 'Reçu partiellement'] as $value => $label)
                            <option value="{{ $value }}" @selected(request('status') == $value)>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_range" class="form-label">Période</label>
                    <input type="text" class="form-control" id="date_range" name="date_range" 
                           value="{{ request('date_range') }}" placeholder="Sélectionner une période">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-secondary me-2">
                        <i class="fas fa-filter"></i> Filtrer
                    </button>
                    <a href="{{ route('supplies.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Boutique</th>
                            <th>Fournisseur</th>
                            <th>Date Commande</th>
                            <th>Montant Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($supplies as $supply)
                            <tr>
                                <td>{{ $supply->reference_number }}</td>
                                <td>{{ $supply->shop->name }}</td>
                                <td>{{ $supply->supplier->name }}</td>
                                <td>{{ $supply->order_date->format('d/m/Y') }}</td>
                                <td>{{ number_format($supply->total_amount, 2) }} €</td>
                                <td>
                                    <span class="badge bg-{{ $supply->status_color }}">
                                        {{ $supply->status_label }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('supplies.show', $supply) }}" 
                                           class="btn btn-sm btn-info" title="Détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($supply->canBeValidated() && auth()->user()->can('validate', $supply))
                                            <form action="{{ route('supplies.validate', $supply) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                @method('PUT')
                                                <button type="submit" class="btn btn-sm btn-success" 
                                                        title="Valider">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if($supply->canBeReceived() && auth()->user()->can('receive', $supply))
                                            <a href="{{ route('supplies.receive.form', $supply) }}" 
                                               class="btn btn-sm btn-warning" title="Réceptionner">
                                                <i class="fas fa-box"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Aucun approvisionnement trouvé</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                {{ $supplies->withQueryString()->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialisation du sélecteur de dates
        $('#date_range').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Appliquer',
                cancelLabel: 'Annuler',
                fromLabel: 'Du',
                toLabel: 'Au'
            },
            autoUpdateInput: false
        });

        $('#date_range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
        });

        $('#date_range').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    });
</script>
@endpush
