<?php

namespace App\Providers;

use App\Events\UserLoggedIn;
use App\Listeners\SendUserLoginNotification;
use App\Models\User;
use App\Models\Sale;
use App\Models\Stock;
use App\Models\Delivery;
use App\Observers\UserObserver;
use App\Observers\SaleObserver;
use App\Observers\StockObserver;
use App\Observers\DeliveryObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        UserLoggedIn::class => [
            SendUserLoginNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        User::observe(UserObserver::class);
        Sale::observe(SaleObserver::class);
        Stock::observe(StockObserver::class);
        Delivery::observe(DeliveryObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
