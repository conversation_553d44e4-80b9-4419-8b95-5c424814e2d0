<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Storekeeper\DashboardController;
use App\Http\Controllers\Storekeeper\ProductController;
use App\Http\Controllers\Storekeeper\SupplyController;
use App\Http\Controllers\Storekeeper\TransferController;
use App\Http\Controllers\Storekeeper\InventoryController;

Route::middleware(['auth', 'role:storekeeper'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('storekeeper.dashboard');
    
    // Gestion des produits
    Route::resource('products', ProductController::class)->only(['index', 'show']);
    Route::get('/products/{product}/history', [ProductController::class, 'history'])
        ->name('storekeeper.products.history');
    
    // Gestion des approvisionnements
    Route::resource('supplies', SupplyController::class)->names([
        'index' => 'storekeeper.supplies.index',
        'create' => 'storekeeper.supplies.create',
        'store' => 'storekeeper.supplies.store',
        'show' => 'storekeeper.supplies.show',
        'edit' => 'storekeeper.supplies.edit',
        'update' => 'storekeeper.supplies.update',
        'destroy' => 'storekeeper.supplies.destroy',
    ]);
    Route::post('/supplies/{supply}/receive', [SupplyController::class, 'receive'])
        ->name('storekeeper.supplies.receive');
    
    // Gestion des transferts
    Route::resource('transfers', TransferController::class)->names([
        'index' => 'storekeeper.transfers.index',
        'create' => 'storekeeper.transfers.create',
        'store' => 'storekeeper.transfers.store',
        'show' => 'storekeeper.transfers.show',
        'edit' => 'storekeeper.transfers.edit',
        'update' => 'storekeeper.transfers.update',
        'destroy' => 'storekeeper.transfers.destroy',
    ]);
    Route::post('/transfers/{transfer}/send', [TransferController::class, 'send'])
        ->name('storekeeper.transfers.send');
    Route::post('/transfers/{transfer}/receive', [TransferController::class, 'receive'])
        ->name('storekeeper.transfers.receive');
    
    // Gestion des inventaires
    Route::resource('inventories', InventoryController::class)->names([
        'index' => 'storekeeper.inventories.index',
        'create' => 'storekeeper.inventories.create',
        'store' => 'storekeeper.inventories.store',
        'show' => 'storekeeper.inventories.show',
    ]);
    Route::post('/inventories/{inventory}/validate', [InventoryController::class, 'validate'])
        ->name('storekeeper.inventories.validate');
    
    // Rapports
    Route::get('/reports/stock', [ProductController::class, 'stockReport'])
        ->name('storekeeper.reports.stock');
    Route::get('/reports/movements', [ProductController::class, 'movementsReport'])
        ->name('storekeeper.reports.movements');
});
