<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Database\Seeders\AccountSeeder;
use App\Models\Account;

class FullAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 1. Plan comptable général (comptes et sous-comptes classiques)
        $this->call(AccountSeeder::class);

        // 2. Comptes analytiques (exemple)
        $departments = [
            ['code' => '7000', 'name' => 'Département Commercial', 'type' => 'autre', 'description' => 'Gestion analytique - Commercial'],
            ['code' => '7010', 'name' => 'Département Production', 'type' => 'autre', 'description' => 'Gestion analytique - Production'],
            ['code' => '7020', 'name' => 'Département RH', 'type' => 'autre', 'description' => 'Gestion analytique - Ressources Humaines'],
        ];
        foreach ($departments as $data) {
            Account::firstOrCreate([
                'code' => $data['code'],
            ], $data);
        }

        // 3. Comptes par magasin (exemple)
        $shops = [
            ['code' => '8000', 'name' => 'Magasin Paris', 'type' => 'autre', 'description' => 'Compte analytique Magasin Paris'],
            ['code' => '8010', 'name' => 'Magasin Lyon', 'type' => 'autre', 'description' => 'Compte analytique Magasin Lyon'],
        ];
        foreach ($shops as $data) {
            Account::firstOrCreate([
                'code' => $data['code'],
            ], $data);
        }

        // 4. Comptes de projets (exemple)
        $projects = [
            ['code' => '9000', 'name' => 'Projet Alpha', 'type' => 'autre', 'description' => 'Suivi analytique Projet Alpha'],
            ['code' => '9010', 'name' => 'Projet Beta', 'type' => 'autre', 'description' => 'Suivi analytique Projet Beta'],
        ];
        foreach ($projects as $data) {
            Account::firstOrCreate([
                'code' => $data['code'],
            ], $data);
        }
    }
}
