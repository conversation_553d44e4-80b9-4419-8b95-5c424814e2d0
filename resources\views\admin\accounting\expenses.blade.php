@extends('layouts.admin')

@section('title', 'Dépenses')

@section('content')
<div class="container max-w-4xl mx-auto py-8">
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-indigo-800 mb-2 flex items-center">
            <i class="fas fa-receipt mr-2"></i> <PERSON><PERSON><PERSON><PERSON>
        </h2>
        <p class="text-gray-500 mb-4">Suivez l’ensemble de vos dépenses, filtrez par période ou recherchez une opération précise.</p>
        <form method="GET" action="" class="flex flex-wrap gap-4 items-end bg-white shadow-md rounded-lg p-4 mb-4">
            <div>
                <label class="block text-sm font-semibold text-gray-600">Recherche</label>
                <input type="text" name="q" class="form-control rounded-lg" placeholder="Description ou montant..." value="{{ request('q') }}">
            </div>
            <div>
                <label class="block text-sm font-semibold text-gray-600">Du</label>
                <input type="date" name="from" class="form-control rounded-lg" value="{{ request('from') }}">
            </div>
            <div>
                <label class="block text-sm font-semibold text-gray-600">Au</label>
                <input type="date" name="to" class="form-control rounded-lg" value="{{ request('to') }}">
            </div>
            <div>
                <button class="btn btn-primary px-6 py-2 rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-700 text-white font-bold shadow hover:from-indigo-600 hover:to-indigo-800 transition-all flex items-center" type="submit">
                    <i class="fas fa-search mr-2"></i>Filtrer
                </button>
            </div>
        </form>
    </div>
    <div class="card shadow-lg border-0">
        <div class="card-body p-0">
            <div class="overflow-x-auto">
            @if(isset($expenses) && count($expenses) > 0)
                <table class="table table-bordered table-hover mb-0 animate-fade-in" style="min-width:600px">
                    <thead class="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white">
                        <tr>
                            <th>#</th>
                            <th>Description</th>
                            <th>Montant</th>
                            <th>Date</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($expenses as $expense)
                            <tr class="transition hover:bg-indigo-50">
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $expense->description ?? '-' }}</td>
                                <td class="text-end text-red-700 font-bold">
                                    <i class="fas fa-arrow-down mr-1"></i>{{ number_format($expense->amount ?? 0, 2, ',', ' ') }} F CFA
                                </td>
                                <td>{{ $expense->created_at ? $expense->created_at->format('d/m/Y') : '-' }}</td>
                                <td>
                                    @if(($expense->amount ?? 0) > 100000)
                                        <span class="px-2 py-1 rounded-full bg-red-100 text-red-800 text-xs font-bold">Grosse dépense</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-info-circle mr-2"></i>Aucune dépense enregistrée.
                </div>
            @endif
            </div>
        </div>
    </div>
</div>
<style>
@keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: none; } }
.animate-fade-in { animation: fade-in 0.7s cubic-bezier(.4,0,.2,1); }
</style>
@endsection
