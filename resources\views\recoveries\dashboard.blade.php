@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Tableau de Bord des Recouvrements</h2>
                    <a href="{{ route('recoveries.index') }}" class="btn btn-secondary">Retour à la liste</a>
                </div>

                <div class="card-body">
                    <div class="row mb-4">
                        <!-- Statistiques générales -->
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total en attente</h5>
                                    <h2>{{ number_format($stats['total_pending'], 2) }} €</h2>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Recouvrements en retard</h5>
                                    <h2>{{ $stats['overdue_count'] }}</h2>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Recouvré ce mois</h5>
                                    <h2>{{ number_format($stats['this_month_recovered'], 2) }} €</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recouvrements critiques -->
                    <div class="card mt-4">
                        <div class="card-header bg-danger text-white">
                            <h3 class="card-title">Recouvrements Critiques (> 30 jours)</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Référence</th>
                                            <th>Boutique</th>
                                            <th>Montant</th>
                                            <th>Jours de retard</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['critical_recoveries'] as $recovery)
                                            <tr>
                                                <td>{{ $recovery->reference_number }}</td>
                                                <td>{{ $recovery->shop->name }}</td>
                                                <td>{{ number_format($recovery->remaining_amount, 2) }} €</td>
                                                <td>{{ $recovery->days_overdue }} jours</td>
                                                <td>
                                                    <a href="{{ route('recoveries.edit', $recovery) }}" class="btn btn-warning btn-sm">Gérer</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
