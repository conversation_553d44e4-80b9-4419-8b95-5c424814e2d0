<?php

namespace Database\Factories;

use App\Models\Delivery;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;

class DeliveryFactory extends Factory
{
    protected $model = Delivery::class;

    public function definition()
    {
        return [
            'sale_id' => Sale::factory(),
            'status' => fake()->randomElement(['pending', 'in_progress', 'completed', 'cancelled']),
            'delivery_date' => fake()->dateTimeBetween('now', '+1 week'),
            'delivery_address' => fake()->address(),
            'notes' => fake()->sentence(),
        ];
    }

    public function withSale(Sale $sale)
    {
        return $this->state(fn (array $attributes) => [
            'sale_id' => $sale->id,
        ]);
    }

    public function pending()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }
}
