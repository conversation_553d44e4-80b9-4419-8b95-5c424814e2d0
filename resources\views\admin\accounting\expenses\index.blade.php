@extends('layouts.admin')
@section('title', 'Liste des dépenses')
@section('content')
<div class="container max-w-5xl mx-auto py-8">
    @if(session('success'))
        <div class="alert alert-success mb-4 animate-fade-in"> <i class="fas fa-check-circle mr-2"></i>{{ session('success') }} </div>
    @endif
    @if($errors->any())
        <div class="alert alert-danger mb-4 animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i>Erreur&nbsp;:
            <ul class="mb-0 mt-1">
                @foreach($errors->all() as $err)
                    <li>{{ $err }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Cartes de résumé -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-gradient-to-r from-indigo-500 to-indigo-700 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
            <i class="fas fa-wallet fa-2x"></i>
            <div>
                <div class="font-bold text-xl">{{ $totalMonth ?? 0 }} FCFA</div>
                <div class="text-sm">Dépenses ce mois</div>
            </div>
        </div>
        <div class="bg-gradient-to-r from-pink-400 to-pink-600 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
            <i class="fas fa-list fa-2x"></i>
            <div>
                <div class="font-bold text-xl">{{ $categories->count() ?? 0 }}</div>
                <div class="text-sm">Catégories</div>
            </div>
        </div>
        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-2xl shadow-lg p-6 flex items-center gap-4">
            <i class="fas fa-arrow-up fa-2x"></i>
            <div>
                <div class="font-bold text-xl">{{ $maxExpense ?? 0 }} FCFA</div>
                <div class="text-sm">Plus grosse dépense</div>
            </div>
        </div>
    </div>

    <div class="mb-8 flex flex-wrap justify-between items-center gap-4">
        <div>
            <h2 class="text-3xl font-bold text-indigo-800 flex items-center mb-1">
                <i class="fas fa-list-alt mr-2"></i> Dépenses
            </h2>
            <p class="text-gray-500">Consultez, filtrez et gérez toutes vos dépenses.</p>
        </div>
        <a href="{{ route('admin.accounting.expenses.create') }}" class="btn btn-primary px-6 py-2 rounded-lg font-bold flex items-center">
            <i class="fas fa-plus mr-2"></i>Nouvelle dépense
        </a>
    </div>

    <form method="GET" action="" class="flex flex-wrap gap-4 items-end bg-white shadow-md rounded-lg p-4 mb-4">
        <div>
            <label class="block text-sm font-semibold text-gray-600">Recherche</label>
            <input type="text" name="q" class="form-control rounded-lg" placeholder="Description ou montant..." value="{{ request('q') }}">
        </div>
        <div>
            <label class="block text-sm font-semibold text-gray-600">Catégorie</label>
            <select name="category_id" class="form-control rounded-lg">
                <option value="">-- Toutes --</option>
                @foreach($categories as $cat)
                    <option value="{{ $cat->id }}" @if(request('category_id') == $cat->id) selected @endif>{{ $cat->name }}</option>
                @endforeach
            </select>
        </div>
        <div>
            <label class="block text-sm font-semibold text-gray-600">Du</label>
            <input type="date" name="from" class="form-control rounded-lg" value="{{ request('from') }}">
        </div>
        <div>
            <label class="block text-sm font-semibold text-gray-600">Au</label>
            <input type="date" name="to" class="form-control rounded-lg" value="{{ request('to') }}">
        </div>
        <div>
            <button class="btn btn-primary px-6 py-2 rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-700 text-white font-bold shadow hover:from-indigo-600 hover:to-indigo-800 transition-all flex items-center" type="submit">
                <i class="fas fa-search mr-2"></i>Filtrer
            </button>
        </div>
    </form>

    <div class="flex gap-2 mb-4">
        <a href="#" onclick="return exportExpenses('excel');" class="btn btn-outline-success px-4 py-2 rounded-lg font-bold flex items-center"><i class="fas fa-file-excel mr-2"></i>Exporter Excel</a>
        <a href="#" onclick="return exportExpenses('pdf');" class="btn btn-outline-danger px-4 py-2 rounded-lg font-bold flex items-center"><i class="fas fa-file-pdf mr-2"></i>Exporter PDF</a>
    </div>

    <!-- Tableau glassmorphism -->
    <div class="overflow-x-auto rounded-2xl shadow-2xl bg-white/60 backdrop-blur-md">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-white/80">
                <tr>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">#</th>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Description</th>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Montant</th>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Catégorie</th>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Date</th>
                    <th class="px-6 py-3 text-xs font-bold text-gray-700 uppercase">Justificatif</th>
                    <th class="px-6 py-3"></th>
                </tr>
            </thead>
            <tbody>
                @foreach($expenses as $expense)
                <tr class="hover:bg-indigo-50 transition shadow-sm">
                    <td>{{ $loop->iteration }}</td>
                    <td class="font-semibold text-gray-800">{{ $expense->description }}</td>
                    <td>
                        <span class="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full font-bold">
                            {{ number_format($expense->amount, 0, ',', ' ') }} FCFA
                        </span>
                    </td>
                    <td>
                        <span class="px-3 py-1 rounded-full text-xs font-bold bg-pink-100 text-pink-700">
                            {{ $expense->category->name ?? '-' }}
                        </span>
                    </td>
                    <td>
                        @php $date = $expense->date instanceof \Carbon\Carbon ? $expense->date : \Carbon\Carbon::parse($expense->date); @endphp
                        {{ $date->format('d/m/Y') }}
                    </td>
                    <td>
                        @if($expense->attachment)
                            <a href="{{ asset('storage/'.$expense->attachment) }}" target="_blank" class="text-green-600 hover:underline">
                                <i class="fas fa-file-alt"></i>
                            </a>
                        @else
                            <span class="text-gray-400">—</span>
                        @endif
                    </td>
                    <td class="flex gap-2">
                        <a href="{{ route('admin.accounting.expenses.edit', $expense) }}" class="bg-yellow-100 text-yellow-800 rounded-full p-2 hover:bg-yellow-200 transition" title="Éditer">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.accounting.expenses.destroy', $expense) }}" method="POST">
                            @csrf @method('DELETE')
                            <button type="button" class="bg-red-100 text-red-800 rounded-full p-2 hover:bg-red-200 transition delete-expense-btn" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        <div class="mt-4 px-2">{{ $expenses->links() }}</div>
    </div>

    <!-- Bouton flottant ajout -->
    <a href="{{ route('admin.accounting.expenses.create') }}" class="fixed bottom-8 right-8 z-50 bg-indigo-600 text-white rounded-full shadow-lg p-5 hover:bg-indigo-700 transition flex items-center justify-center text-3xl">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Pagination stylée -->
    <div class="px-6 py-4 bg-gray-50 flex justify-between items-center">
        {{ $expenses->links() }}
        <button onclick="window.scrollTo({top:0,behavior:'smooth'})" class="fixed bottom-24 right-8 z-40 bg-indigo-600 text-white rounded-full shadow-lg p-3 hover:bg-indigo-700 transition">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>
</div>
<style>
@keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: none; } }
.animate-fade-in { animation: fade-in 0.7s cubic-bezier(.4,0,.2,1); }
</style>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function fetchStats() {
    const params = new URLSearchParams(window.location.search);
    fetch("{{ route('admin.accounting.expenses.stats') }}?" + params.toString())
        .then(r => r.json())
        .then(stats => {
            let html = `<div class='bg-white shadow-md rounded-lg p-6 mb-6 flex flex-wrap gap-8 items-center'>
                <div>
                    <div class='text-xs text-gray-500 mb-1'>Total dépenses</div>
                    <div class='text-2xl font-bold text-red-700'>${Number(stats.total).toLocaleString('fr-FR', {minimumFractionDigits:2})} F CFA</div>
                </div>
                <div>
                    <div class='text-xs text-gray-500 mb-1'>Top catégories</div>
                    <ul class='list-disc pl-5'>`;
            stats.topCategories.forEach(c => {
                html += `<li><span class='font-semibold'>${c.name}</span> : <span class='text-red-700'>${Number(c.total).toLocaleString('fr-FR', {minimumFractionDigits:2})} F CFA</span></li>`;
            });
            html += `</ul></div><div style='min-width:220px;'>
                <div class='text-xs text-gray-500 mb-1'>Évolution mensuelle</div>
                <canvas id='evoChart' height='60'></canvas>
            </div></div>`;
            document.getElementById('statsCard').innerHTML = html;
            // Chart
            if(stats.evolution.length) {
                let ctx = document.getElementById('evoChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: stats.evolution.map(e => e.month),
                        datasets: [{
                            label: 'Dépenses',
                            data: stats.evolution.map(e => e.total),
                            backgroundColor: '#e53e3e',
                        }]
                    },
                    options: {
                        plugins: { legend: { display: false } },
                        scales: { x: { grid: {display:false}}, y: { beginAtZero: true, grid: {display:false} } },
                        responsive: true,
                    }
                });
            }
        });
}
document.addEventListener('DOMContentLoaded', fetchStats);
function exportExpenses(type) {
    const params = new URLSearchParams(window.location.search);
    let url = type === 'excel' ? "{{ route('admin.accounting.expenses.export.excel') }}" : "{{ route('admin.accounting.expenses.export.pdf') }}";
    window.open(url + '?' + params.toString(), '_blank');
    return false;
}
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.delete-expense-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const form = this.closest('form');
            Swal.fire({
                title: 'Supprimer ?',
                text: 'Cette action est irréversible.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });
});
</script>
@endpush
