@extends('layouts.app')

@section('title', 'Nouveau mouvement de stock')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nouveau mouvement de stock</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('stock.movements.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">Produit <span class="text-danger">*</span></label>
                                    <select class="form-control @error('product_id') is-invalid @enderror" 
                                            id="product_id" name="product_id" required>
                                        <option value="">Sélectionner un produit</option>
                                        @foreach($products as $product)
                                            <option value="{{ $product->id }}" 
                                                    data-stocks='@json($product->stocks)'
                                                    {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                                {{ $product->name }} ({{ $product->sku }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('product_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shop_id">Boutique <span class="text-danger">*</span></label>
                                    <select class="form-control @error('shop_id') is-invalid @enderror" 
                                            id="shop_id" name="shop_id" required>
                                        <option value="">Sélectionner une boutique</option>
                                        @foreach($shops as $shop)
                                            <option value="{{ $shop->id }}" 
                                                    {{ old('shop_id') == $shop->id ? 'selected' : '' }}>
                                                {{ $shop->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('shop_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="type">Type de mouvement <span class="text-danger">*</span></label>
                                    <select class="form-control @error('type') is-invalid @enderror" 
                                            id="type" name="type" required>
                                        <option value="">Sélectionner un type</option>
                                        <option value="in" {{ old('type') == 'in' ? 'selected' : '' }}>Entrée</option>
                                        <option value="out" {{ old('type') == 'out' ? 'selected' : '' }}>Sortie</option>
                                        <option value="adjustment" {{ old('type') == 'adjustment' ? 'selected' : '' }}>Ajustement</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="quantity">Quantité <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('quantity') is-invalid @enderror" 
                                           id="quantity" name="quantity" value="{{ old('quantity') }}" 
                                           min="0" step="1" required>
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Stock actuel</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="current_stock" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">unités</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer le mouvement
                            </button>
                            <a href="{{ route('stock.movements.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialiser Select2
    $('#product_id, #shop_id').select2({
        theme: 'bootstrap4',
        placeholder: 'Sélectionner...'
    });

    // Mettre à jour le stock actuel quand le produit ou la boutique change
    function updateCurrentStock() {
        var productId = $('#product_id').val();
        var shopId = $('#shop_id').val();
        
        if (productId && shopId) {
            var stocks = $('#product_id option:selected').data('stocks');
            var stock = stocks.find(s => s.shop_id == shopId);
            
            if (stock) {
                $('#current_stock').val(stock.quantity);
            } else {
                $('#current_stock').val('0');
            }
        } else {
            $('#current_stock').val('');
        }
    }

    $('#product_id, #shop_id').on('change', updateCurrentStock);
    updateCurrentStock();

    // Validation du formulaire
    $('form').on('submit', function(e) {
        var type = $('#type').val();
        var quantity = parseInt($('#quantity').val());
        var currentStock = parseInt($('#current_stock').val());

        if (type === 'out' && quantity > currentStock) {
            e.preventDefault();
            alert('Stock insuffisant pour cette sortie !');
        }
    });
});
</script>
@endpush
@endsection
