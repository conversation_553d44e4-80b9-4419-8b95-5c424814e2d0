<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shop extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'address',
        'phone',
        'email',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get the users associated with the shop
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the sales associated with the shop
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the stocks associated with the shop
     */
    public function stocks()
    {
        return $this->hasMany(Stock::class);
    }

    /**
     * Get the products associated with the shop
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'stocks')
            ->withPivot(['quantity', 'min_stock'])
            ->withTimestamps();
    }
}
