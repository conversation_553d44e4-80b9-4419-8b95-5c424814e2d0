<?php

namespace App\Http\Controllers\Biller;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreSaleRequest;
use App\Events\SaleCreated;

class SaleController extends Controller
{
    public function index()
    {
        $sales = Sale::with(['customer', 'items.product'])
            ->where('shop_id', auth()->user()->shop_id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('biller.sales.index', compact('sales'));
    }

    public function create()
    {
        $customers = Customer::orderBy('name')->get();
        $products = Product::whereHas('stocks', function($query) {
            $query->where('shop_id', auth()->user()->shop_id)
                ->where('quantity', '>', 0);
        })->get();

        return view('biller.sales.create', compact('customers', 'products'));
    }

    public function store(StoreSaleRequest $request)
    {
        try {
            DB::beginTransaction();

            $sale = Sale::create([
                'shop_id' => auth()->user()->shop_id,
                'customer_id' => $request->customer_id,
                'user_id' => auth()->id(),
                'payment_type' => $request->payment_type,
                'status' => $request->payment_type === 'credit' ? 'pending' : 'completed',
                'notes' => $request->notes
            ]);

            foreach ($request->items as $item) {
                $sale->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total' => $item['quantity'] * $item['price']
                ]);

                // Mettre à jour le stock
                $stock = Stock::where('shop_id', auth()->user()->shop_id)
                    ->where('product_id', $item['product_id'])
                    ->first();
                
                $stock->decrement('quantity', $item['quantity']);
            }

            // Créer le paiement si ce n'est pas un crédit
            if ($request->payment_type !== 'credit') {
                $sale->payments()->create([
                    'amount' => $sale->total_amount,
                    'payment_method' => $request->payment_type,
                    'status' => 'completed',
                    'user_id' => auth()->id()
                ]);
            }

            DB::commit();

            // Déclencher l'événement de création de vente
            event(new SaleCreated($sale));

            return redirect()
                ->route('biller.sales.show', $sale)
                ->with('success', 'Facture créée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la création de la facture.');
        }
    }

    public function show(Sale $sale)
    {
        $sale->load(['customer', 'items.product', 'payments']);
        return view('biller.sales.show', compact('sale'));
    }

    public function print(Sale $sale)
    {
        $sale->load(['customer', 'items.product', 'shop']);
        return view('biller.sales.print', compact('sale'));
    }

    public function printDelivery(Sale $sale)
    {
        $sale->load(['customer', 'items.product', 'shop']);
        return view('biller.sales.print-delivery', compact('sale'));
    }

    public function addPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:cash,check,bank_transfer',
            'reference' => 'required_if:payment_method,check,bank_transfer'
        ]);

        try {
            DB::beginTransaction();

            $payment = $sale->payments()->create([
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'status' => $request->payment_method === 'check' ? 'pending' : 'completed',
                'user_id' => auth()->id()
            ]);

            // Mettre à jour le statut de la vente si le montant total est payé
            if ($sale->payments()->where('status', 'completed')->sum('amount') >= $sale->total_amount) {
                $sale->update(['status' => 'completed']);
            }

            DB::commit();

            return redirect()
                ->route('biller.sales.show', $sale)
                ->with('success', 'Paiement enregistré avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de l\'enregistrement du paiement.');
        }
    }

    public function printReceipt(Sale $sale, Payment $payment)
    {
        return view('biller.sales.receipt', compact('sale', 'payment'));
    }
}
