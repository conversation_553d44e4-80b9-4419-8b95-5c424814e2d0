<div class="bg-white overflow-hidden shadow-xl rounded-lg relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br opacity-5 from-{{ $color }}-500 to-{{ $color }}-600">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'30\' height=\'30\' viewBox=\'0 0 30 30\' fill=\'none\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M1.22676 0C1.91374 0 2.45351 0.539773 2.45351 1.22676C2.45351 1.91374 1.91374 2.45351 1.22676 2.45351C0.539773 2.45351 0 1.91374 0 1.22676C0 0.539773 0.539773 0 1.22676 0Z\' fill=\'rgba(0,0,0,0.07)\'/%3E%3C/svg%3E')"></div>
    </div>
    
    <div class="px-4 py-5 sm:p-6 relative">
        <div class="flex items-center">
            <div class="flex-shrink-0 rounded-md p-3 bg-{{ $color }}-500 bg-opacity-10">
                <i class="{{ $icon }} text-{{ $color }}-600 text-xl"></i>
            </div>
            <div class="ml-4 flex-1">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ $title }}
                </dt>
                <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                        {{ $value }}
                    </div>
                    
                    @if($percentage)
                    <div class="ml-2 flex items-baseline text-sm font-semibold {{ $trend === 'up' ? 'text-green-600' : 'text-red-600' }}">
                        <i class="fas fa-{{ $trend === 'up' ? 'arrow-up' : 'arrow-down' }} mr-0.5 flex-shrink-0 self-center"></i>
                        <span class="sr-only">{{ $trend === 'up' ? 'Augmentation' : 'Diminution' }} de</span>
                        {{ $percentage }}%
                    </div>
                    @endif
                </dd>
                @if($subtitle)
                <p class="mt-1 text-sm text-gray-500">{{ $subtitle }}</p>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Bottom Border -->
    <div class="absolute bottom-0 inset-x-0 h-1 bg-{{ $color }}-500"></div>
</div>