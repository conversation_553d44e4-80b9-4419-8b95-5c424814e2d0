<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'description' => ['nullable', 'string'],
            'barcode' => [
                'nullable',
                'string',
                'size:13',
                'unique:products,barcode' . ($this->product ? ',' . $this->product->id : '')
            ],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'selling_price' => ['required', 'numeric', 'min:0', 'gte:unit_price'],
            'min_stock' => ['required', 'integer', 'min:0'],
            'status' => ['required', 'in:active,inactive'],
            'image' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
            'stocks' => ['required', 'array'],
            'stocks.*' => ['required', 'integer', 'min:0']
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => 'Le nom du produit est obligatoire.',
            'name.max' => 'Le nom du produit ne peut pas dépasser 255 caractères.',
            'category_id.required' => 'La catégorie est obligatoire.',
            'category_id.exists' => 'La catégorie sélectionnée n\'existe pas.',
            'barcode.size' => 'Le code-barres doit contenir exactement 13 caractères.',
            'barcode.unique' => 'Ce code-barres est déjà utilisé par un autre produit.',
            'unit_price.required' => 'Le prix d\'achat est obligatoire.',
            'unit_price.numeric' => 'Le prix d\'achat doit être un nombre.',
            'unit_price.min' => 'Le prix d\'achat doit être supérieur ou égal à 0.',
            'selling_price.required' => 'Le prix de vente est obligatoire.',
            'selling_price.numeric' => 'Le prix de vente doit être un nombre.',
            'selling_price.min' => 'Le prix de vente doit être supérieur ou égal à 0.',
            'selling_price.gte' => 'Le prix de vente doit être supérieur ou égal au prix d\'achat.',
            'min_stock.required' => 'Le stock minimum est obligatoire.',
            'min_stock.integer' => 'Le stock minimum doit être un nombre entier.',
            'min_stock.min' => 'Le stock minimum doit être supérieur ou égal à 0.',
            'status.required' => 'Le statut est obligatoire.',
            'status.in' => 'Le statut sélectionné n\'est pas valide.',
            'image.image' => 'Le fichier doit être une image.',
            'image.mimes' => 'L\'image doit être au format JPEG, PNG ou JPG.',
            'image.max' => 'L\'image ne doit pas dépasser 2Mo.',
            'stocks.required' => 'Le stock initial est obligatoire.',
            'stocks.array' => 'Le stock initial doit être un tableau.',
            'stocks.*.required' => 'Le stock initial est obligatoire pour toutes les boutiques.',
            'stocks.*.integer' => 'Le stock initial doit être un nombre entier.',
            'stocks.*.min' => 'Le stock initial doit être supérieur ou égal à 0.'
        ];
    }
}
