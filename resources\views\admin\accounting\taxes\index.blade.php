@extends('layouts.admin')

@section('title', 'Gestion des taxes')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-extrabold text-blue-900 flex items-center gap-2">
            <i class="fas fa-percentage text-blue-500"></i>
            Gestion des taxes
        </h1>
        <a href="{{ route('admin.accounting.taxes.create') }}"
           class="inline-flex items-center px-5 py-2 bg-gradient-to-r from-blue-600 to-blue-400 text-white rounded-lg shadow hover:scale-105 transition-transform font-semibold">
            <i class="fas fa-plus mr-2"></i> Nouvelle taxe
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    <div class="mt-6 bg-white rounded-xl shadow-lg overflow-x-auto">
        <table class="min-w-full divide-y divide-blue-200">
            <thead class="bg-blue-50 sticky top-0 z-10">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Nom</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Taux (%)</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-center text-xs font-bold text-blue-700 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-blue-100">
                @forelse($taxes as $tax)
                <tr class="hover:bg-blue-50 transition">
                    <td class="px-6 py-4 text-sm text-gray-900 font-semibold">
                        <i class="fas fa-tag text-blue-400 mr-1"></i>
                        {{ $tax->name }}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900">{{ $tax->rate }}</td>
                    <td class="px-6 py-4">
                        @if($tax->type === 'TVA')
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-bold bg-blue-100 text-blue-700">TVA</span>
                        @elseif($tax->type === 'impot')
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-bold bg-green-100 text-green-700">Impôt</span>
                        @else
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-bold bg-gray-200 text-gray-700">Autre</span>
                        @endif
                    </td>
                    <td class="px-6 py-4 text-gray-700">{{ $tax->description }}</td>
                    <td class="px-6 py-4 text-center">
                        <div class="flex items-center justify-center gap-2">
                            <a href="{{ route('admin.accounting.taxes.edit', $tax) }}"
                               class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-blue-50 hover:bg-blue-200 text-blue-600 transition"
                               title="Éditer">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('admin.accounting.taxes.destroy', $tax) }}" method="POST" class="d-inline delete-tax-form">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-red-50 hover:bg-red-200 text-red-600 transition delete-tax-btn"
                                        title="Supprimer">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="text-center py-6 text-gray-400">Aucune taxe enregistrée.</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
<!-- FontAwesome CDN (si pas déjà inclus) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"/>
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.delete-tax-form').forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: 'Cette action supprimera définitivement la taxe.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui, supprimer',
                    cancelButtonText: 'Annuler'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });
    });
</script>
@endpush
@endsection
