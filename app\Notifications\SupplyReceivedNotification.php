<?php

namespace App\Notifications;

use App\Models\Supply;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;

class SupplyReceivedNotification extends Notification
{
    use Queueable;

    protected $supply;

    /**
     * Créer une nouvelle instance de notification
     */
    public function __construct(Supply $supply)
    {
        $this->supply = $supply;
    }

    /**
     * Obtenir les canaux de livraison de la notification
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Obtenir la représentation mail de la notification
     */
    public function toMail($notifiable)
    {
        $url = route('supplies.show', $this->supply->id);
        $status = $this->supply->status === 'received' ? 'entièrement' : 'partiellement';

        return (new MailMessage)
            ->subject('Approvisionnement Reçu - ' . $this->supply->reference_number)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('L\'approvisionnement ' . $this->supply->reference_number . ' a été ' . $status . ' reçu.')
            ->line('Boutique : ' . $this->supply->shop->name)
            ->line('Date de réception : ' . $this->supply->received_date->format('d/m/Y H:i'))
            ->line('Reçu par : ' . $this->supply->receiver->name)
            ->when($this->supply->notes, function ($mail) {
                return $mail->line('Notes : ' . $this->supply->notes);
            })
            ->action('Voir les détails', $url);
    }

    /**
     * Obtenir la représentation array de la notification
     */
    public function toArray($notifiable)
    {
        return [
            'supply_id' => $this->supply->id,
            'reference_number' => $this->supply->reference_number,
            'shop_name' => $this->supply->shop->name,
            'received_date' => $this->supply->received_date,
            'receiver_name' => $this->supply->receiver->name,
            'status' => $this->supply->status,
            'notes' => $this->supply->notes,
            'type' => 'supply_received'
        ];
    }
}
