<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class RecoveryController extends Controller
{
    public function index() { return view('admin.recoveries.index'); }
    public function create() { return view('admin.recoveries.create'); }
    public function store(Request $request) { return redirect()->route('admin.recoveries.index'); }
    public function show($id) { return view('admin.recoveries.show', compact('id')); }
    public function edit($id) { return view('admin.recoveries.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('admin.recoveries.index'); }
    public function destroy($id) { return redirect()->route('admin.recoveries.index'); }
}
