@extends('layouts.admin')

@section('title', "Chiffre d'Affaires")

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="flex flex-col sm:flex-row justify-between items-center bg-gradient-to-r from-blue-600 to-blue-800 p-6 rounded-xl shadow-lg mb-4">
        <div class="flex items-center gap-3">
            <i class="fas fa-chart-line text-3xl text-white animate-pulse"></i>
            <h1 class="text-3xl font-bold text-white">Chiffre d'Affaires</h1>
        </div>
        <div class="flex gap-2 mt-4 sm:mt-0">
            <button class="btn-glass bg-white text-blue-700 hover:bg-blue-100"><i class="fas fa-file-pdf text-red-600"></i> PDF</button>
            <button class="btn-glass bg-white text-green-700 hover:bg-green-100"><i class="fas fa-file-excel text-green-600"></i> Excel</button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-blue-50 rounded-xl shadow p-6 mb-6">
        <form action="{{ route('admin.accounting.revenue') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4" method="GET">
            <div>
                <label for="start_date" class="block text-sm font-medium text-blue-900">Date de début</label>
                <input type="date" class="form-input rounded-md w-full" id="start_date" name="start_date" value="{{ request('start_date') }}">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-blue-900">Date de fin</label>
                <input type="date" class="form-input rounded-md w-full" id="end_date" name="end_date" value="{{ request('end_date') }}">
            </div>
            <div>
                <label for="store" class="block text-sm font-medium text-blue-900">Boutique</label>
                <select class="form-select rounded-md w-full" id="store" name="store_id">
                    <option value="">Toutes les boutiques</option>
                    @foreach($stores as $store)
                        <option value="{{ $store->id }}" {{ request('store_id') == $store->id ? 'selected' : '' }}>{{ $store->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex gap-2 items-end">
                <button type="submit" class="btn-glass bg-blue-700 text-white hover:bg-blue-800 w-full">Filtrer</button>
                <a href="{{ route('admin.accounting.revenue') }}" class="btn-glass bg-white text-blue-700 hover:bg-blue-100 w-full">Réinitialiser</a>
            </div>
        </form>
    </div>

    <!-- Résumé -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="stat-card bg-gradient-to-tr from-blue-500 to-blue-700 relative">
            <div class="stat-icon absolute top-3 right-3 text-6xl opacity-10"><i class="fas fa-coins"></i></div>
            <div class="stat-label text-white/80 mt-8 text-sm">Total</div>
            <div class="stat-value text-3xl font-extrabold text-white mt-2">{{ number_format($stats['total'], 0, ',', ' ') }} <span class="badge-currency bg-yellow-300 text-yellow-900 px-2 py-1 rounded text-xs ml-2 font-bold">FCFA</span></div>
        </div>
        <div class="stat-card bg-gradient-to-tr from-green-400 to-green-600 relative">
            <div class="stat-icon absolute top-3 right-3 text-6xl opacity-10"><i class="fas fa-cash-register"></i></div>
            <div class="stat-label text-white/80 mt-8 text-sm">Encaissé</div>
            <div class="stat-value text-3xl font-extrabold text-white mt-2">{{ number_format($stats['paid'], 0, ',', ' ') }} <span class="badge-currency bg-yellow-300 text-yellow-900 px-2 py-1 rounded text-xs ml-2 font-bold">FCFA</span></div>
        </div>
        <div class="stat-card bg-gradient-to-tr from-orange-400 to-orange-600 relative">
            <div class="stat-icon absolute top-3 right-3 text-6xl opacity-10"><i class="fas fa-hourglass-half"></i></div>
            <div class="stat-label text-white/80 mt-8 text-sm">Crédits</div>
            <div class="stat-value text-3xl font-extrabold text-white mt-2">{{ number_format($stats['pending'], 0, ',', ' ') }} <span class="badge-currency bg-yellow-300 text-yellow-900 px-2 py-1 rounded text-xs ml-2 font-bold">FCFA</span></div>
        </div>
        <div class="stat-card bg-gradient-to-tr from-purple-400 to-purple-600 relative">
            <div class="stat-icon absolute top-3 right-3 text-6xl opacity-10"><i class="fas fa-users"></i></div>
            <div class="stat-label text-white/80 mt-8 text-sm">Ventes</div>
            <div class="stat-value text-3xl font-extrabold text-white mt-2">{{ $stats['count'] }}</div>
        </div>
    </div>

    <!-- Tableau -->
    <div class="bg-white rounded-xl shadow p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto">
                <thead class="bg-blue-700 text-white">
                    <tr>
                        <th class="py-2 px-3">Date</th>
                        <th class="py-2 px-3">Référence</th>
                        <th class="py-2 px-3">Boutique</th>
                        <th class="py-2 px-3">Client</th>
                        <th class="py-2 px-3">Montant</th>
                        <th class="py-2 px-3">Mode de Paiement</th>
                        <th class="py-2 px-3">Statut</th>
                        <th class="py-2 px-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($revenues as $revenue)
                        <tr class="hover:bg-blue-50 transition">
                            <td>{{ $revenue->created_at->format('d/m/Y H:i') }}</td>
                            <td>{{ $revenue->reference_number }}</td>
                            <td>{{ $revenue->shop->name ?? '-' }}</td>
                            <td>{{ $revenue->customer->name ?? 'Client Anonyme' }}</td>
                            <td>{{ number_format($revenue->total_amount, 0, ',', ' ') }} <span class="badge-currency bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded text-xs ml-1">FCFA</span></td>
                            <td>{{ $revenue->payment_method }}</td>
                            <td>
                                <span class="inline-block px-3 py-1 rounded-full text-xs font-bold {{ $revenue->payment_status === 'paid' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700' }}">
                                    {{ $revenue->payment_status === 'paid' ? 'Payé' : 'En attente' }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ route('admin.sales.show', $revenue->id) }}" class="btn-action bg-blue-100 hover:bg-blue-200 text-blue-700 ml-2" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center py-6 text-gray-400">Aucune vente trouvée</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $revenues->links() }}
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stat-card {
    border-radius: 1rem;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.07);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
}
.stat-icon {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    font-size: 3.5rem;
    opacity: 0.10;
    pointer-events: none;
}
.stat-label {
    margin-top: 2rem;
    font-size: 0.95rem;
    color: rgba(255,255,255,0.8);
}
.stat-value {
    margin-top: 0.5rem;
    font-size: 2rem;
    font-weight: 800;
    color: #fff;
}
.badge-currency {
    background: #fde047;
    color: #854d0e;
    padding: 0.15rem 0.45rem;
    border-radius: 0.25rem;
    font-size: 0.85em;
    margin-left: 0.5rem;
    font-weight: bold;
}
.btn-glass {
    padding: 0.5rem 1.25rem;
    border-radius: 0.6rem;
    box-shadow: 0 2px 8px 0 rgba(59,130,246,0.07);
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    border: none;
}
.btn-glass:hover {
    transform: scale(1.04);
    background: #e0e7ff;
}
.btn-action {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 9999px;
    transition: background 0.2s;
}
.btn-action:hover {
    background: #dbeafe;
}
</style>
@endpush
