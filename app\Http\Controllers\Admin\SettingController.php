<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        $settings = $this->getSettings();
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the specified settings.
     */
    public function update(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'site_name' => 'required|string|max:255',
                'site_description' => 'nullable|string|max:1000',
                'contact_email' => 'required|email',
                'contact_phone' => 'nullable|string|max:20',
                'address' => 'nullable|string|max:500',
                'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'favicon' => 'nullable|image|mimes:ico,png|max:1024',
                'footer_text' => 'nullable|string|max:1000',
                'social_links' => 'nullable|array',
                'social_links.*' => 'nullable|url',
                'maintenance_mode' => 'boolean',
                'timezone' => 'required|string|timezone',
                'date_format' => 'required|string',
                'per_page' => 'required|integer|min:5|max:100'
            ]);

            if ($validator->fails()) {
                return back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $settings = $this->getSettings();

            // Gérer le logo
            if ($request->hasFile('logo')) {
                if (isset($settings['logo']) && Storage::exists($settings['logo'])) {
                    Storage::delete($settings['logo']);
                }
                $settings['logo'] = $request->file('logo')->store('public/settings');
            }

            // Gérer le favicon
            if ($request->hasFile('favicon')) {
                if (isset($settings['favicon']) && Storage::exists($settings['favicon'])) {
                    Storage::delete($settings['favicon']);
                }
                $settings['favicon'] = $request->file('favicon')->store('public/settings');
            }

            // Mettre à jour les paramètres
            $settings = array_merge($settings, $request->except(['logo', 'favicon']));
            
            // Sauvegarder les paramètres
            $this->saveSettings($settings);

            // Journaliser l'activité
            activity()
                ->causedBy(auth()->user())
                ->log('settings_updated');

            return redirect()
                ->route('admin.settings.index')
                ->with('success', 'Paramètres mis à jour avec succès.');

        } catch (\Exception $e) {
            return back()
                ->with('error', 'Une erreur est survenue lors de la mise à jour des paramètres.')
                ->withInput();
        }
    }

    /**
     * Get all settings.
     */
    private function getSettings(): array
    {
        return Cache::remember('site_settings', 24 * 60, function () {
            return [
                'site_name' => config('app.name'),
                'site_description' => '',
                'contact_email' => config('mail.from.address'),
                'contact_phone' => '',
                'address' => '',
                'logo' => null,
                'favicon' => null,
                'footer_text' => '',
                'social_links' => [
                    'facebook' => '',
                    'twitter' => '',
                    'instagram' => '',
                    'linkedin' => ''
                ],
                'maintenance_mode' => false,
                'timezone' => config('app.timezone'),
                'date_format' => 'd/m/Y H:i',
                'per_page' => 15
            ];
        });
    }

    /**
     * Save settings to cache and file.
     */
    private function saveSettings(array $settings): void
    {
        Cache::put('site_settings', $settings, 24 * 60);

        $path = storage_path('app/settings.json');
        file_put_contents($path, json_encode($settings, JSON_PRETTY_PRINT));
    }

    /**
     * Clear the settings cache.
     */
    public function clearCache()
    {
        try {
            Cache::forget('site_settings');
            
            activity()
                ->causedBy(auth()->user())
                ->log('settings_cache_cleared');

            return redirect()
                ->route('admin.settings.index')
                ->with('success', 'Cache des paramètres effacé avec succès.');

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de l\'effacement du cache.');
        }
    }
}
