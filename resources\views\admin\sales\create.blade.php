@extends('layouts.admin')

@section('content')
@if(session('success'))
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        Swal.fire({
            icon: 'success',
            title: 'Succès',
            text: @json(session('success')),
            confirmButtonText: 'OK'
        });
    </script>
@endif
@if(session('error'))
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: @json(session('error')),
            confirmButtonText: 'OK'
        });
    </script>
@endif

<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Nouvelle Vente</h2>
                <a href="{{ route('admin.sales.index') }}" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <form action="{{ route('admin.sales.store') }}" method="POST" class="p-6 space-y-6" id="saleForm">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Sélection client avec bouton + -->
                <div>
                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Client</label>
                    <div class="flex items-center space-x-2">
                        <select name="customer_id" id="customer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            <option value="">Sélectionner un client</option>
                            @foreach($customers as $id => $name)
                                <option value="{{ $id }}" {{ old('customer_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                        <button type="button" id="openCustomerModalBtn" class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white hover:bg-blue-700 mt-1" title="Ajouter un client">
                            <span class="text-xl font-bold">+</span>
                        </button>
                    </div>
                    @error('customer_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- Boutique -->
                <div>
                    <label for="shop_id" class="block text-sm font-medium text-gray-700">Boutique</label>
                    <select name="shop_id" id="shop_id" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            required>
                        <option value="">Sélectionner une boutique</option>
                        @foreach($shops as $id => $name)
                            <option value="{{ $id }}" {{ old('shop_id') == $id ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                    @error('shop_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- Remise globale -->
                <div>
                    <label for="discount" class="block text-sm font-medium text-gray-700">Remise globale (FCFA)</label>
                    <input type="number" min="0" name="discount" id="discount" value="{{ old('discount') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" placeholder="0 (optionnel)">
                    @error('discount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- TVA globale -->
                <div>
                    <label class="block text-sm font-medium text-gray-700" for="tva_enabled">
                        <input type="checkbox" id="tva_enabled" name="tva_enabled" value="1" {{ old('tva_enabled', '1') ? 'checked' : '' }}>
                        Activer la TVA
                    </label>
                    <input type="number" min="0" max="100" step="0.01" name="tva" id="tva" value="{{ old('tva', 18) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" placeholder="TVA (%)" {{ old('tva_enabled', '1') ? '' : 'disabled' }}>
                    @error('tva')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- Méthode de paiement -->
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700">Méthode de paiement</label>
                    <select name="payment_method" id="payment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        <option value="">Sélectionner</option>
                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Espèces</option>
                        <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>Carte</option>
                        <option value="mobile" {{ old('payment_method') == 'mobile' ? 'selected' : '' }}>Mobile Money</option>
                    </select>
                    @error('payment_method')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <!-- Statut du paiement -->
                <div>
                    <label for="payment_status" class="block text-sm font-medium text-gray-700">Statut du paiement</label>
                    <select name="payment_status" id="payment_status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        <option value="pending" {{ old('payment_status') == 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="paid" {{ old('payment_status') == 'paid' ? 'selected' : '' }}>Payé</option>
                    </select>
                    @error('payment_status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Champ Montant remis (affiché si espèces) -->
            <div id="montantRemisContainer" class="mt-4" style="display:none;">
                <label for="amount_paid" class="block text-sm font-medium text-gray-700">Montant remis</label>
                <input type="number" min="0" step="any" name="amount_paid" id="amount_paid" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" autocomplete="off" placeholder="Saisir le montant donné par le client">
                <span id="monnaieRendue" class="block mt-2 text-green-700 font-semibold"></span>
            </div>

            <!-- Sélecteur produit + mini-formulaire -->
            <div class="mt-8">
                <label for="productSelect" class="block text-sm font-medium text-gray-700">Produit</label>
                <select id="productSelect" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">-- Choisir un produit --</option>
                    @foreach($products as $p)
                        <option value="{{ $p['id'] }}" data-stock="{{ $p['stock'] }}" data-price="{{ $p['price'] }}" data-unit="{{ $p['unit'] ?? '' }}">{{ $p['name'] }} ({{ $p['sku'] }}) - Stock: {{ $p['stock'] }} {{ $p['unit'] }}</option>
                    @endforeach
                </select>
                <div id="productFormContainer" class="mt-4"></div>
            </div>

            <!-- Panier dynamique sous forme de tableau -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold mb-4 text-blue-800">Panier</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white rounded shadow text-sm" id="cartTable">
                        <thead class="bg-blue-100">
                            <tr>
                                <th class="px-3 py-2 text-left">Produit</th>
                                <th class="px-3 py-2 text-center">Quantité</th>
                                <th class="px-3 py-2 text-center">Prix unitaire</th>
                                <th class="px-3 py-2 text-center">Unité</th>
                                <th class="px-3 py-2 text-center">Remise</th>
                                <th class="px-3 py-2 text-center">TVA</th>
                                <th class="px-3 py-2 text-right">Sous-total</th>
                                <th class="px-3 py-2 text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody id="cartTableBody">
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="7" class="text-right font-bold py-2">Total</td>
                                <td class="text-right font-bold py-2" id="totalAmount">0 FCFA</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="flex flex-col items-end mt-4 space-y-2" id="dynamicTotalBox">
                    <div><span class="font-semibold">Total brut :</span> <span id="totalBrutDisplay">0 FCFA</span></div>
                    <div><span class="font-semibold">Remise globale :</span> <span id="discountDisplay">0 FCFA</span></div>
                    <div><span class="font-semibold">TVA :</span> <span id="tvaDisplay">0 %</span></div>
                    <div class="text-lg font-bold"><span class="font-semibold">Total TTC :</span> <span id="totalTtcDisplay">0 FCFA</span></div>
                </div>
                <div class="flex justify-end mt-4 space-x-2">
                    <button type="button" id="clearCartBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Vider le panier</button>
                    <button type="submit" id="validateSaleBtn" class="px-6 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 text-lg font-semibold disabled:opacity-50" disabled>Valider la vente</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Modal création client -->
<div id="customerModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-auto p-6 relative">
        <button type="button" id="closeCustomerModalBtn" class="absolute top-2 right-2 text-gray-400 hover:text-red-600 text-2xl">&times;</button>
        <h3 class="text-lg font-semibold mb-4 text-blue-800">Ajouter un client</h3>
        <form id="customerCreateForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Nom</label>
                <input type="text" name="name" id="customerName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" name="email" id="customerEmail" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Téléphone</label>
                <input type="text" name="phone" id="customerPhone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500">
            </div>
            <div id="customerModalError" class="text-red-600 text-sm mb-2"></div>
            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Créer</button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Déclarer products UNE SEULE FOIS, tout en haut
const products = @json($products);
console.log('DEBUG: JS ZIAD refonte chargé');
console.log('DEBUG: Liste des produits', products);
if (products.length > 0) {
    console.log('DEBUG: Structure produit exemple', products[0]);
}
if (!Array.isArray(products) || products.length === 0) {
    alert('Aucun produit n\'est disponible pour la vente. Veuillez vérifier la configuration des produits côté serveur.');
}
let cart = [];
let selectedProduct = null;

// Sélecteur produit classique + mini-formulaire dynamique
const productSelect = document.getElementById('productSelect');
const productFormContainer = document.getElementById('productFormContainer');
productSelect.addEventListener('change', function() {
    const id = parseInt(this.value);
    if (!id) {
        productFormContainer.innerHTML = '';
        selectedProduct = null;
        return;
    }
    selectedProduct = products.find(p => p.id === id);
    showProductForm(selectedProduct);
});

function showProductForm(product) {
    const safePrice = parseFloat(product.price) || 0;
    productFormContainer.innerHTML = `
        <div class="bg-gray-50 p-4 rounded shadow flex flex-col md:flex-row md:items-end md:space-x-4 space-y-2 md:space-y-0 animate-fade-in">
            <div>
                <h3 class="font-bold text-lg">${product.name} (${product.sku})</h3>
                <div class="text-sm text-gray-500">Stock: ${product.stock} ${product.unit}</div>
                <div class="text-sm font-semibold text-blue-600">${safePrice.toLocaleString()} FCFA</div>
            </div>
            <div class="flex-1">
                <div class="flex items-end space-x-4">
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700">Quantité</label>
                        <input type="number" min="1" max="${product.stock}" value="1" id="quantity" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" required>
                    </div>
                    <div>
                        <label for="unit" class="block text-sm font-medium text-gray-700">Unité</label>
                        <select id="unit" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500">
                            <option value="${product.unit}">${product.unit}</option>
                            <option value="pièce">Pièce</option>
                            <option value="paquet">Paquet</option>
                            <option value="boîte">Boîte</option>
                            <option value="carton">Carton</option>
                            <option value="bouteille">Bouteille</option>
                        </select>
                    </div>
                    <button type="button" onclick="addToCart(${product.id}, '${product.name.replace("'", "\\'")}', ${product.price}, '${product.unit}')" 
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Ajouter
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Panier dynamique sous forme de tableau
const cartTableBody = document.getElementById('cartTableBody');
const totalAmountSpan = document.getElementById('totalAmount');
const validateSaleBtn = document.getElementById('validateSaleBtn');
const clearCartBtn = document.getElementById('clearCartBtn');
function renderCart() {
    cartTableBody.innerHTML = cart.map((p, idx) => {
        const safePrice = parseFloat(p.price) || 0;
        return `
            <tr>
                <td class="px-4 py-2">${p.name} (${p.sku})</td>
                <td class="px-4 py-2 text-right">${p.quantity}</td>
                <td class="px-4 py-2 text-right">${p.unit}</td>
                <td class="px-4 py-2 text-right">${safePrice.toLocaleString()} FCFA</td>
                <td class="px-4 py-2 text-right">${(safePrice * p.quantity).toLocaleString()} FCFA</td>
                <td class="px-4 py-2 text-center">
                    <button type="button" onclick="removeFromCart(${idx})" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    updateDynamicTotal();
    updateValidateButton(); // Mettre à jour l'état du bouton Valider
}
function updateTotal() {
    const total = cart.reduce((sum, p) => sum + ((parseFloat(p.price) || 0) * p.quantity - (parseFloat(p.discount) || 0) + (parseFloat(p.tva) || 0)), 0);
    totalAmountSpan.textContent = total.toFixed(2) + ' FCFA';
}
function attachCartEvents() {
    // Suppression
    cartTableBody.querySelectorAll('.remove-product').forEach(btn => {
        btn.addEventListener('click', function() {
            const idx = parseInt(this.dataset.idx);
            cart.splice(idx, 1);
            renderCart();
        });
    });
    // Edition quantité
    cartTableBody.querySelectorAll('.cart-qty').forEach(input => {
        input.addEventListener('change', function() {
            const idx = parseInt(this.dataset.idx);
            let val = parseInt(this.value);
            if (isNaN(val) || val < 1) val = 1;
            if (val > cart[idx].stock) val = cart[idx].stock;
            cart[idx].quantity = val;
            renderCart();
        });
    });
    // Edition remise
    cartTableBody.querySelectorAll('.cart-discount').forEach(input => {
        input.addEventListener('change', function() {
            const idx = parseInt(this.dataset.idx);
            let val = parseFloat(this.value);
            if (isNaN(val) || val < 0) val = 0;
            cart[idx].discount = val;
            renderCart();
        });
    });
    // Edition TVA
    cartTableBody.querySelectorAll('.cart-tva').forEach(input => {
        input.addEventListener('change', function() {
            const idx = parseInt(this.dataset.idx);
            let val = parseFloat(this.value);
            if (isNaN(val) || val < 0) val = 0;
            cart[idx].tva = val;
            renderCart();
        });
    });
}
clearCartBtn.onclick = function() {
    cart = [];
    renderCart();
};
renderCart();

function getDiscountValue() {
    const discountInput = document.getElementById('discount');
    return parseFloat(discountInput.value) || 0;
}
function getTvaValue() {
    const tvaInput = document.getElementById('tva');
    const tvaEnabled = document.getElementById('tva_enabled').checked;
    return tvaEnabled && tvaInput.value !== '' ? parseFloat(tvaInput.value) : 0;
}
function updateDynamicTotal() {
    let totalBrut = cart.reduce((sum, p) => sum + ((parseFloat(p.price) || 0) * p.quantity - (parseFloat(p.discount) || 0) + (parseFloat(p.tva) || 0)), 0);
    let discount = getDiscountValue();
    let tvaPercent = getTvaValue();
    let tvaMontant = 0;
    let totalTtc = totalBrut;
    if (discount > 0 && tvaPercent > 0) {
        tvaMontant = (totalBrut - discount) * (tvaPercent / 100);
        totalTtc = (totalBrut - discount) + tvaMontant;
    } else if (discount > 0) {
        totalTtc = totalBrut - discount;
    } else if (tvaPercent > 0) {
        tvaMontant = totalBrut * (tvaPercent / 100);
        totalTtc = totalBrut + tvaMontant;
    }
    document.getElementById('totalBrutDisplay').textContent = totalBrut.toFixed(2) + ' FCFA';
    document.getElementById('discountDisplay').textContent = discount.toFixed(2) + ' FCFA';
    document.getElementById('tvaDisplay').textContent = tvaPercent.toFixed(2) + ' %';
    document.getElementById('totalTtcDisplay').textContent = totalTtc.toFixed(2) + ' FCFA';
}
// Mettre à jour le total dynamique à chaque modification du panier, de la remise ou de la TVA
['input','change'].forEach(evt => {
    document.getElementById('discount').addEventListener(evt, updateDynamicTotal);
    document.getElementById('tva').addEventListener(evt, updateDynamicTotal);
    document.getElementById('tva_enabled').addEventListener(evt, updateDynamicTotal);
});
const origRenderCart = renderCart;
renderCart = function() {
    origRenderCart();
    updateDynamicTotal();
};
// Initial update
updateDynamicTotal();

// Gestion modale client
const openCustomerModalBtn = document.getElementById('openCustomerModalBtn');
const customerModal = document.getElementById('customerModal');
const closeCustomerModalBtn = document.getElementById('closeCustomerModalBtn');
openCustomerModalBtn.onclick = () => customerModal.classList.remove('hidden');
closeCustomerModalBtn.onclick = () => customerModal.classList.add('hidden');
customerModal.onclick = function(e) {
    if (e.target === customerModal) customerModal.classList.add('hidden');
};

// Soumission AJAX du formulaire de création client
const customerCreateForm = document.getElementById('customerCreateForm');
const customerModalError = document.getElementById('customerModalError');
const customerSelect = document.getElementById('customer_id');
customerCreateForm.onsubmit = async function(e) {
    e.preventDefault();
    customerModalError.textContent = '';
    const data = {
        name: document.getElementById('customerName').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        phone: document.getElementById('customerPhone').value.trim(),
        _token: '{{ csrf_token() }}'
    };
    try {
        const response = await fetch("{{ route('admin.customers.ajax-store') }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': data._token
            },
            body: JSON.stringify(data)
        });
        const res = await response.json();
        if (!res.success) {
            customerModalError.textContent = (res.errors || ['Erreur inconnue']).join('\n');
            return;
        }
        // Ajoute le client au select et sélectionne-le
        const opt = document.createElement('option');
        opt.value = res.customer.id;
        opt.textContent = res.customer.name;
        opt.selected = true;
        customerSelect.appendChild(opt);
        customerModal.classList.add('hidden');
        customerCreateForm.reset();
    } catch (err) {
        customerModalError.textContent = "Erreur réseau ou serveur. Veuillez réessayer.";
    }
};

// TVA enable/disable
const tvaEnabledCheckbox = document.getElementById('tva_enabled');
const tvaInput = document.getElementById('tva');
tvaEnabledCheckbox.addEventListener('change', function() {
    if (this.checked) {
        tvaInput.disabled = false;
        tvaInput.value = tvaInput.value || 18;
    } else {
        tvaInput.disabled = true;
        tvaInput.value = '';
    }
});

// Champ Montant remis
const paymentMethod = document.getElementById('payment_method');
const montantRemisContainer = document.getElementById('montantRemisContainer');
const amountPaidInput = document.getElementById('amount_paid');
const monnaieRendueSpan = document.getElementById('monnaieRendue');
const netAPayerSelector = document.querySelector('[name="total_amount"], #net_a_payer'); // Adapter si besoin

function getNetAPayer() {
    // Essaie de récupérer le net à payer dynamiquement (adapter le sélecteur si besoin)
    let val = 0;
    // Essaye d'utiliser la valeur dynamique affichée dans la page
    const totalTtcDisplay = document.getElementById('totalTtcDisplay');
    if (totalTtcDisplay && totalTtcDisplay.textContent) {
        val = parseFloat(totalTtcDisplay.textContent.replace(/[^\d.,]/g, '').replace(',', '.')) || 0;
    } else if (netAPayerSelector && netAPayerSelector.value) {
        val = parseFloat(netAPayerSelector.value) || 0;
    }
    return val;
}

function updateMonnaieRendue() {
    const montantRemis = parseFloat(amountPaidInput.value) || 0;
    const netAPayer = getNetAPayer();
    const monnaie = Math.max(montantRemis - netAPayer, 0);
    monnaieRendueSpan.textContent = montantRemis > 0 ? `Monnaie à rendre : ${monnaie.toLocaleString('fr-FR', {minimumFractionDigits: 2, maximumFractionDigits: 2})} FCFA` : '';
}

if (amountPaidInput) {
    amountPaidInput.addEventListener('input', updateMonnaieRendue);
}

// Afficher/masquer le champ Montant remis selon la méthode de paiement
if (paymentMethod) {
    paymentMethod.addEventListener('change', function() {
        if (this.value === 'cash') {
            montantRemisContainer.style.display = '';
        } else {
            montantRemisContainer.style.display = 'none';
            if (amountPaidInput) amountPaidInput.value = '';
            if (monnaieRendueSpan) monnaieRendueSpan.textContent = '';
        }
    });
    // Initialiser à l'ouverture
    if (paymentMethod.value === 'cash') {
        montantRemisContainer.style.display = '';
    }
}

// Mettre à jour la monnaie rendue si le total change dynamiquement
['input', 'change'].forEach(evt => {
    if (amountPaidInput) amountPaidInput.addEventListener(evt, updateMonnaieRendue);
    const totalTtcDisplay = document.getElementById('totalTtcDisplay');
    if (totalTtcDisplay) totalTtcDisplay.addEventListener(evt, updateMonnaieRendue);
});

// Initial update
updateMonnaieRendue();

function addToCart(productId, productName, productPrice, productUnit) {
    const quantityInput = document.getElementById('quantity');
    const unitInput = document.getElementById('unit');
    
    const quantity = parseInt(quantityInput.value) || 1;
    const unit = unitInput.value || productUnit;
    
    // Trouver le produit complet dans la liste des produits
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    // Vérifier si le produit est déjà dans le panier
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        // Mettre à jour la quantité si le produit existe déjà
        existingItem.quantity += quantity;
    } else {
        // Ajouter un nouvel item au panier
        cart.push({
            id: productId,
            name: productName,
            sku: product.sku,
            price: productPrice,
            unit: unit,
            quantity: quantity,
            stock: product.stock
        });
    }
    
    renderCart();
    productFormContainer.innerHTML = '';
    productSelect.value = '';
}

function removeFromCart(index) {
    if (confirm('Voulez-vous vraiment retirer ce produit du panier ?')) {
        cart.splice(index, 1);
        renderCart();
    }
}

function updateValidateButton() {
    const validateBtn = document.getElementById('validateSaleBtn');
    const customerId = document.getElementById('customer_id').value;
    const shopId = document.getElementById('shop_id').value;
    const paymentStatus = document.getElementById('payment_status').value;
    
    // Activer le bouton seulement si:
    // - Il y a des produits dans le panier
    // - Tous les champs requis sont remplis
    validateBtn.disabled = cart.length === 0 || !customerId || !shopId || !paymentStatus;
}

// Mettre à jour l'état du bouton Valider à chaque modification du panier
['input', 'change'].forEach(evt => {
    document.getElementById('customer_id').addEventListener(evt, updateValidateButton);
    document.getElementById('shop_id').addEventListener(evt, updateValidateButton);
    document.getElementById('payment_status').addEventListener(evt, updateValidateButton);
});

// Initialisation au chargement
document.addEventListener('DOMContentLoaded', function() {
    updateValidateButton();
});

// Gestion de la soumission du formulaire
document.getElementById('saleForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    console.log('Début de la soumission du formulaire');
    
    const validateBtn = document.getElementById('validateSaleBtn');
    validateBtn.disabled = true;
    validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> En cours...';
    
    try {
        // Vérifier le token CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || 
                         document.querySelector('input[name="_token"]')?.value;
        
        if (!csrfToken) {
            throw new Error('Token CSRF non trouvé');
        }
        
        console.log('CSRF Token:', csrfToken);
        
        // Préparer les données
        const formData = new FormData(this);
        formData.append('products', JSON.stringify(cart.map(item => ({
            id: item.id,
            quantity: item.quantity,
            unit: item.unit,
            price: item.price
        }))));
        
        console.log('Données du formulaire préparées:', formData);
        
        // Envoyer les données
        const response = await fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken
            }
        });
        
        console.log('Réponse du serveur:', response);
        
        const result = await response.json();
        console.log('Résultat:', result);
        
        if (response.ok) {
            window.location.href = "{{ route('admin.sales.index') }}";
        } else {
            throw new Error(result.message || 'Erreur lors de la validation');
        }
    } catch (error) {
        console.error('Erreur lors de la soumission:', error);
        alert('Erreur: ' + error.message);
        validateBtn.disabled = false;
        validateBtn.textContent = 'Valider la vente';
    }
});

// Traitement de la réponse AJAX
$.ajax({
    url: form.attr('action'),
    method: 'POST',
    data: form.serialize(),
    dataType: 'json',
    headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
        if (typeof response === 'object' && response !== null && 'success' in response) {
            if (response.success) {
                window.location.href = '/admin/sales/' + response.sale_id;
            } else {
                alert(response.message || 'Erreur lors du traitement');
            }
        } else {
            throw new Error('Réponse serveur invalide');
        }
    },
    error: function(xhr) {
        let errorMsg = 'Erreur technique';
        
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMsg = xhr.responseJSON.message;
        } else if (xhr.statusText) {
            errorMsg = xhr.statusText;
        }
        
        alert(errorMsg);
        console.error('Erreur AJAX:', xhr.responseText);
    }
});
</script>
@endpush

@push('styles')
<style>
@keyframes fade-in {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
.animate-fade-in { animation: fade-in 0.5s; }
</style>
@endpush
@endsection
