<!DOCTYPE html>
<html lang="fr" class="h-full bg-gray-100">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - @yield('title', 'Administration')</title>
    
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon"/>

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.0/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    @vite(['resources/css/app.css'])
    
    @stack('styles')
</head>
<body class="h-full">
    <div x-data="{ sidebarOpen: false }">
        <!-- Bouton burger mobile -->
        <div class="lg:hidden flex items-center px-4 py-3 bg-white shadow">
            <button @click="sidebarOpen = true" class="text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 rounded">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                <span class="sr-only">Ouvrir le menu</span>
            </button>
            <span class="ml-4 text-lg font-semibold text-blue-700">ZIAD SARL</span>
        </div>
        <!-- Off-canvas menu for mobile -->
        <div x-show="sidebarOpen" class="relative z-50 lg:hidden" role="dialog" aria-modal="true">
            <div x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" 
                 x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                 class="fixed inset-0 bg-gray-900/80"></div>

            <div class="fixed inset-0 flex">
                <div x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform"
                     x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0"
                     x-transition:leave="transition ease-in-out duration-300 transform"
                     x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full"
                     class="relative mr-16 flex w-full max-w-xs flex-1">
                    
                    <div x-show="sidebarOpen" x-transition:enter="ease-in-out duration-300"
                         x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                         x-transition:leave="ease-in-out duration-300"
                         x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                         class="absolute left-full top-0 flex w-16 justify-center pt-5">
                        <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                            <span class="sr-only">Fermer le menu</span>
                            <i class="fas fa-times h-6 w-6 text-white"></i>
                        </button>
                    </div>

                    <!-- Sidebar component for mobile -->
                    <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-blue-600 px-6 pb-4 border-none">
                        <div class="flex flex-col items-center justify-center py-4 border-b border-blue-700">
                            @php($user = auth()->user())
                            @if($user && $user->avatar_path)
                                <img src="{{ $user->avatar_url }}" alt="Avatar" class="h-16 w-16 rounded-full object-cover border-2 border-white shadow mb-2">
                            @else
                                <div class="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-700 text-white flex items-center justify-center text-2xl font-bold mb-2">
                                    {{ strtoupper(substr($user->name ?? 'U', 0, 1)) }}
                                </div>
                            @endif
                            <span class="text-white font-semibold text-base">{{ $user->name ?? '' }}</span>
                            <span class="text-blue-200 text-xs">{{ $user->email ?? '' }}</span>
                        </div>
                        <div class="flex h-16 shrink-0 items-center border-none">
                            <span class="text-white text-2xl font-semibold">ZIAD SARL</span>
                        </div>
                        <nav class="flex flex-1 flex-col border-none">
                            <ul role="list" class="flex flex-1 flex-col gap-y-7 border-none">
                                <li>
                                    <ul role="list" class="-mx-2 space-y-1 border-none">
                                        <!-- Dashboard -->
                                        <li>
                                            <a href="{{ route('admin.dashboard') }}"
                                               class="{{ request()->routeIs('admin.dashboard') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-chart-line h-6 w-6 shrink-0"></i>
                                                Tableau de bord
                                            </a>
                                        </li>

                                        <!-- Journal d'activités -->
                                        <li>
                                            <a href="{{ route('admin.activities.index') }}"
                                               class="{{ request()->routeIs('admin.activities.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-history h-6 w-6 shrink-0"></i>
                                                Journal d'activités
                                            </a>
                                        </li>

                                        <!-- Monitoring -->
                                        <li>
                                            <a href="{{ route('admin.monitoring.index') }}"
                                               class="{{ request()->routeIs('admin.monitoring.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-desktop h-6 w-6 shrink-0"></i>
                                                Monitoring en temps réel
                                            </a>
                                        </li>

                                        <!-- Alertes -->
                                        <li>
                                            <a href="{{ route('admin.alerts.index') }}"
                                               class="{{ request()->routeIs('admin.alerts.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-bell h-6 w-6 shrink-0"></i>
                                                Alertes système
                                            </a>
                                        </li>

                                        <!-- Utilisateurs -->
                                        <li>
                                            <a href="{{ route('admin.users.index') }}"
                                               class="{{ request()->routeIs('admin.users.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-users h-6 w-6 shrink-0"></i>
                                                Utilisateurs
                                            </a>
                                        </li>

                                        <!-- Boutiques -->
                                        <li>
                                            <a href="{{ route('admin.shops.index') }}"
                                               class="{{ request()->routeIs('admin.shops.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-store h-6 w-6 shrink-0"></i>
                                                Boutiques
                                            </a>
                                        </li>

                                        <!-- Paramètres système -->
                                        <li>
                                            <a href="{{ route('admin.settings.index') }}"
                                               class="{{ request()->routeIs('admin.settings.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-cogs h-6 w-6 shrink-0"></i>
                                                Paramètres système
                                            </a>
                                        </li>

                                        <!-- Menu déroulant Activités -->
                                        <li x-data="{ open: {{ request()->routeIs('admin.products.*') || request()->routeIs('admin.stocks.*') || request()->routeIs('admin.sales.*') || request()->routeIs('categories.*') ? 'true' : 'false' }} }">
                                            <button @click="open = !open" type="button" class="w-full flex items-center gap-x-3 rounded-md p-3 text-base leading-6 font-semibold text-white hover:text-blue-200 hover:bg-blue-700/80 transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none group">
                                                <i class="fas fa-briefcase h-6 w-6 shrink-0"></i>
                                                <span class="flex-1 text-left">Activités</span>
                                                <i :class="{'fa-chevron-down': !open, 'fa-chevron-up': open}" class="fas ml-auto transition-transform"></i>
                                            </button>
                                            <ul x-show="open" x-transition class="pl-6 space-y-1 mt-1">
                                                <li>
                                                    <a href="{{ route('categories.index') }}"
                                                       class="{{ request()->routeIs('categories.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                        <i class="fas fa-tags h-5 w-5 shrink-0"></i>
                                                        Catégories
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{{ route('admin.products.index') }}"
                                                       class="{{ request()->routeIs('admin.products.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                        <i class="fas fa-box h-5 w-5 shrink-0"></i>
                                                        Produits
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{{ route('admin.stocks.index') }}"
                                                       class="{{ request()->routeIs('admin.stocks.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                        <i class="fas fa-warehouse h-5 w-5 shrink-0"></i>
                                                        Approvisionements
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{{ route('admin.sales.index') }}"
                                                       class="{{ request()->routeIs('admin.sales.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                        <i class="fas fa-shopping-cart h-5 w-5 shrink-0"></i>
                                                        Ventes
                                                    </a>
                                                </li>
                                            </ul>
                                        </li>

                                        <!-- Comptabilité -->
                                        <li>
                                            <a href="{{ route('admin.accounting.index') }}"
                                               class="{{ request()->routeIs('admin.accounting.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-calculator h-6 w-6 shrink-0"></i>
                                                Comptabilité
                                            </a>
                                        </li>
                                               class="{{ request()->routeIs('categories.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                <i class="fas fa-tags h-6 w-6 shrink-0"></i>
                                                Catégories
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wrapper pour la sidebar et le contenu principal -->
        <div class="min-h-full">
            <!-- Sidebar -->
            <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col bg-blue-600 border-none">
                <div class="flex grow flex-col gap-y-5 overflow-y-auto px-6 pb-4 h-screen border-none">
                    <div class="flex h-16 shrink-0 items-center border-none">
                        <span class="text-white text-2xl font-semibold">ZIAD SARL</span>
                    </div>
                    <div class="flex flex-col items-center justify-center py-4 border-b border-blue-700">
                        @php($user = auth()->user())
                        @if($user && $user->avatar_path)
                            <img src="{{ $user->avatar_url }}" alt="Avatar" class="h-16 w-16 rounded-full object-cover border-2 border-white shadow mb-2">
                        @else
                            <div class="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-700 text-white flex items-center justify-center text-2xl font-bold mb-2">
                                {{ strtoupper(substr($user->name ?? 'U', 0, 1)) }}
                            </div>
                        @endif
                        <span class="text-white font-semibold text-base">{{ $user->name ?? '' }}</span>
                        <span class="text-blue-200 text-xs">{{ $user->email ?? '' }}</span>
                    </div>
                    <nav class="flex flex-1 flex-col border-none">
                        <ul role="list" class="flex flex-1 flex-col gap-y-7 border-none">
                            <li>
                                <ul role="list" class="space-y-2 border-none">
                                    <!-- Dashboard -->
                                    <li>
                                        <a href="{{ route('admin.dashboard') }}"
                                           class="{{ request()->routeIs('admin.dashboard') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-chart-line h-6 w-6 shrink-0"></i>
                                            Tableau de bord
                                        </a>
                                    </li>
                                    <!-- Journal d'activités -->
                                    <li>
                                        <a href="{{ route('admin.activities.index') }}"
                                           class="{{ request()->routeIs('admin.activities.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-history h-6 w-6 shrink-0"></i>
                                            Journal d'activités
                                        </a>
                                    </li>
                                    <!-- Monitoring -->
                                    <li>
                                        <a href="{{ route('admin.monitoring.index') }}"
                                           class="{{ request()->routeIs('admin.monitoring.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-desktop h-6 w-6 shrink-0"></i>
                                            Monitoring en temps réel
                                        </a>
                                    </li>
                                    <!-- Alertes -->
                                    <li>
                                        <a href="{{ route('admin.alerts.index') }}"
                                           class="{{ request()->routeIs('admin.alerts.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-bell h-6 w-6 shrink-0"></i>
                                            Alertes système
                                        </a>
                                    </li>
                                    <!-- Utilisateurs -->
                                    <li>
                                        <a href="{{ route('admin.users.index') }}"
                                           class="{{ request()->routeIs('admin.users.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-users h-6 w-6 shrink-0"></i>
                                            Utilisateurs
                                        </a>
                                    </li>
                                    <!-- Boutiques -->
                                    <li>
                                        <a href="{{ route('admin.shops.index') }}"
                                           class="{{ request()->routeIs('admin.shops.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-store h-6 w-6 shrink-0"></i>
                                            Boutiques
                                        </a>
                                    </li>
                                    <!-- Paramètres système -->
                                    <li>
                                        <a href="{{ route('admin.settings.index') }}"
                                           class="{{ request()->routeIs('admin.settings.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-cogs h-6 w-6 shrink-0"></i>
                                            Paramètres système
                                        </a>
                                    </li>
                                    <!-- Menu déroulant Activités -->
                                    <li x-data="{ open: {{ request()->routeIs('admin.products.*') || request()->routeIs('admin.stocks.*') || request()->routeIs('admin.sales.*') || request()->routeIs('categories.*') ? 'true' : 'false' }} }">
                                        <button @click="open = !open" type="button" class="w-full flex items-center gap-x-3 rounded-md p-3 text-base leading-6 font-semibold text-white hover:text-blue-200 hover:bg-blue-700/80 transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none group">
                                            <i class="fas fa-briefcase h-6 w-6 shrink-0"></i>
                                            <span class="flex-1 text-left">Activités</span>
                                            <i :class="{'fa-chevron-down': !open, 'fa-chevron-up': open}" class="fas ml-auto transition-transform"></i>
                                        </button>
                                        <ul x-show="open" x-transition class="pl-6 space-y-1 mt-1">
                                            <li>
                                                <a href="{{ route('categories.index') }}"
                                                   class="{{ request()->routeIs('categories.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                    <i class="fas fa-tags h-5 w-5 shrink-0"></i>
                                                    Catégories
                                                </a>
                                            </li>
                                            <li>
                                                <a href="{{ route('admin.products.index') }}"
                                                   class="{{ request()->routeIs('admin.products.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                    <i class="fas fa-box h-5 w-5 shrink-0"></i>
                                                    Produits
                                                </a>
                                            </li>
                                            <li>
                                                <a href="{{ route('admin.stocks.index') }}"
                                                   class="{{ request()->routeIs('admin.stocks.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                    <i class="fas fa-warehouse h-5 w-5 shrink-0"></i>
                                                    Approvisionements
                                                </a>
                                            </li>
                                            <li>
                                                <a href="{{ route('admin.sales.index') }}"
                                                   class="{{ request()->routeIs('admin.sales.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 flex gap-x-3 rounded-md p-3 text-base leading-6 font-medium transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                                    <i class="fas fa-shopping-cart h-5 w-5 shrink-0"></i>
                                                    Ventes
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <!-- Comptabilité -->
                                    <li>
                                        <a href="{{ route('admin.accounting.index') }}"
                                           class="{{ request()->routeIs('admin.accounting.*') ? 'bg-blue-700' : '' }} text-white hover:text-blue-200 hover:bg-blue-700/80 group flex gap-x-3 rounded-md p-3 text-base leading-6 font-semibold transition-all duration-200 border-none shadow-none focus:outline-none focus:border-none">
                                            <i class="fas fa-calculator h-6 w-6 shrink-0"></i>
                                            Comptabilité
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="lg:pl-72 pl-0 min-h-screen flex flex-col">
                <!-- Barre de navigation -->
                <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
                    <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" @click="sidebarOpen = true">
                        <span class="sr-only">Ouvrir le menu latéral</span>
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                        <form class="relative flex flex-1" action="#" method="GET">
                            <label for="search-field" class="sr-only">Rechercher</label>
                            <i class="fas fa-search absolute inset-y-0 left-0 flex items-center pl-3"></i>
                            <input id="search-field" class="block h-full w-full border-0 py-0 pl-8 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" 
                                   placeholder="Rechercher" type="search" name="search">
                        </form>
                    </div>
                    <div class="flex items-center gap-x-4 lg:gap-x-6">
                        <button type="button" class="rounded-md text-gray-500 hover:text-gray-600">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button type="button" class="rounded-md text-gray-500 hover:text-gray-600">
                            <i class="fas fa-cog"></i>
                        </button>
                        <!-- Profile dropdown -->
                        <div x-data="{ open: false }" class="relative">
                            <button @click="open = !open" @click.away="open = false" type="button" class="flex items-center rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                <span class="sr-only">Ouvrir le menu utilisateur</span>
                                
                                <!-- Avatar ou initiales -->
                                <div class="mr-1">
                                    @if(Auth::user()->avatar_path)
                                        <img src="{{ Auth::user()->avatar_url }}" alt="Avatar" class="h-8 w-8 rounded-full border-2 border-gray-200">
                                    @else
                                        <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-xs">
                                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                        </div>
                                    @endif
                                </div>
                                
                                <span class="ml-1 text-sm font-medium text-gray-700 hidden md:block">{{ Auth::user()->name }}</span>
                                <i class="fas fa-chevron-down text-xs ml-1 text-gray-500"></i>
                            </button>
                            
                            <!-- Dropdown menu -->
                            <div x-show="open" 
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" 
                                 role="menu" 
                                 aria-orientation="vertical" 
                                 aria-labelledby="user-menu-button" 
                                 tabindex="-1"
                                 style="display: none;">
                                
                                <div class="px-4 py-3 border-b border-gray-100">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 mr-3">
                                            @if(Auth::user()->avatar_path)
                                                <img src="{{ Auth::user()->avatar_url }}" alt="Avatar" class="h-10 w-10 rounded-full">
                                            @else
                                                <div class="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold">
                                                    {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ Auth::user()->name }}</div>
                                            <div class="text-xs text-gray-500 truncate">{{ Auth::user()->email }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <a href="{{ route('profile.show') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">
                                    <i class="fas fa-user mr-2"></i> Mon profil
                                </a>
                                
                                <a href="{{ route('profile.password') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">
                                    <i class="fas fa-key mr-2"></i> Modifier mot de passe
                                </a>
                                
                                <div class="border-t border-gray-100 my-1"></div>
                                
                                <form method="POST" action="{{ route('logout') }}" class="block">
                                    @csrf
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100" role="menuitem" tabindex="-1">
                                        <i class="fas fa-sign-out-alt mr-2"></i> Déconnexion
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contenu principal -->
                <main class="flex-1">
                    <div class="px-4 sm:px-6 lg:px-8">
                        @if (session('success'))
                            <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 3000)"
                                 class="rounded-md bg-green-50 p-4 mb-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-800">
                                            {{ session('success') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if (session('error'))
                            <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 3000)"
                                 class="rounded-md bg-red-50 p-4 mb-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-circle text-red-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-red-800">
                                            {{ session('error') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @yield('content')
                    </div>
                </main>
                <footer class="bg-white border-t border-gray-200 text-center py-4 text-gray-500 text-sm shadow-inner w-full fixed bottom-0 left-0 z-50">
                    &copy; 2025 ZIAD. Tous droits réservés. Développée par <span class="font-semibold text-blue-700">MOMK-Solutions</span>
                </footer>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.0/dist/sweetalert2.min.js"></script>
    @vite(['resources/js/app.js'])
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    @stack('scripts')
    @yield('scripts')
    @push('styles')
    <style>
        /* Ultra-forcé : supprime toutes les bordures, même blanches, sur toutes les tables Bootstrap */
        table.table, table.table * {
            border: none !important;
            border-top: none !important;
            border-bottom: none !important;
            border-left: none !important;
            border-right: none !important;
            box-shadow: none !important;
            background: transparent !important;
        }
        table.table thead th {
            border: none !important;
            background: transparent !important;
            font-weight: 600;
            color: #495057;
        }
        table.table tbody tr {
            transition: background 0.2s;
            border-radius: 8px;
        }
        table.table tbody tr:hover {
            background: #f8fafc !important;
            box-shadow: none !important;
        }
        table.table td {
            vertical-align: middle;
        }
        .card.border-0, .card.border-0 * {
            border: none !important;
            box-shadow: none !important;
        }
    </style>
    @endpush
    @stack('scripts')
    @stack('scripts')
</body>
</html>
