<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountingCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'code',
        'description'
    ];

    public function transactions()
    {
        return $this->hasMany(AccountingTransaction::class);
    }

    public function scopeRevenues($query)
    {
        return $query->where('type', 'revenue');
    }

    public function scopeExpenses($query)
    {
        return $query->where('type', 'expense');
    }
}
