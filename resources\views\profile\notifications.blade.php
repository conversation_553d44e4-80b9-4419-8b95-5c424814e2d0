@extends('layouts.app')

@section('title', 'Préférences de notification')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif

            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Préférences de notification</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('profile.notifications.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="custom-control custom-switch mb-3">
                            <input type="checkbox" class="custom-control-input" 
                                   id="email_notifications" name="email_notifications"
                                   {{ $user->notification_preferences->email_notifications ? 'checked' : '' }}>
                            <label class="custom-control-label" for="email_notifications">
                                Recevoir les notifications par email
                            </label>
                            <small class="form-text text-muted">
                                Les notifications importantes seront toujours envoyées par email.
                            </small>
                        </div>

                        <div class="custom-control custom-switch mb-3">
                            <input type="checkbox" class="custom-control-input" 
                                   id="sales_notifications" name="sales_notifications"
                                   {{ $user->notification_preferences->sales_notifications ? 'checked' : '' }}>
                            <label class="custom-control-label" for="sales_notifications">
                                Notifications des ventes
                            </label>
                            <small class="form-text text-muted">
                                Recevoir une notification pour chaque nouvelle vente.
                            </small>
                        </div>

                        <div class="custom-control custom-switch mb-3">
                            <input type="checkbox" class="custom-control-input" 
                                   id="stock_notifications" name="stock_notifications"
                                   {{ $user->notification_preferences->stock_notifications ? 'checked' : '' }}>
                            <label class="custom-control-label" for="stock_notifications">
                                Alertes de stock
                            </label>
                            <small class="form-text text-muted">
                                Recevoir une notification quand un produit atteint son stock minimum.
                            </small>
                        </div>

                        <div class="custom-control custom-switch mb-3">
                            <input type="checkbox" class="custom-control-input" 
                                   id="payment_notifications" name="payment_notifications"
                                   {{ $user->notification_preferences->payment_notifications ? 'checked' : '' }}>
                            <label class="custom-control-label" for="payment_notifications">
                                Notifications de paiement
                            </label>
                            <small class="form-text text-muted">
                                Recevoir une notification pour chaque nouveau paiement.
                            </small>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les préférences
                            </button>
                            <a href="{{ route('profile.show') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour au profil
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
