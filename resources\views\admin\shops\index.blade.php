@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Gestion des Boutiques</h2>
                <a href="{{ route('admin.shops.create') }}" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Ajouter une boutique
                </a>
            </div>
        </div>
    </div>

    <!-- Liste des boutiques -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Nom
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Email
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Téléphone
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utilisateurs
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($shops as $shop)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ $shop->name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $shop->email }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $shop->phone }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $shop->users_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $shop->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $shop->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{{ route('admin.shops.show', $shop) }}" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.shops.edit', $shop) }}" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if($shop->users_count === 0)
                                    <button type="button" 
                                            class="delete-shop text-red-600 hover:text-red-900"
                                            data-shop-id="{{ $shop->id }}"
                                            onclick="deleteShop({{ $shop->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50">
            {{ $shops->links() }}
        </div>
    </div>
</div>

<!-- Modal de confirmation -->
<div id="confirm-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-4" id="modal-title"></h3>
        <div class="mt-4 flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors" id="modal-cancel">
                Annuler
            </button>
            <button type="button" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" id="modal-confirm">
                Confirmer
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion du statut
    document.querySelectorAll('.status-toggle').forEach(button => {
        button.addEventListener('click', function() {
            const shopId = this.dataset.shopId;
            const originalText = this.textContent;
            const originalClasses = this.className;
            this.disabled = true;
            this.textContent = 'Chargement...';
            this.className = 'status-toggle px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800';
            fetch(`{{ url('admin/shops') }}/${shopId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw response;
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    this.textContent = data.status === 'active' ? 'Actif' : 'Inactif';
                    this.className = `status-toggle px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${data.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
                    showToast(data.message, 'success');
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                this.textContent = originalText;
                this.className = originalClasses;
                if (error instanceof Response) {
                    error.json().then(data => {
                        showToast(data.message || "Une erreur est survenue", 'error');
                    });
                } else {
                    showToast(error.message || "Une erreur est survenue", 'error');
                }
            })
            .finally(() => {
                this.disabled = false;
            });
        });
    });

    // Suppression d'une boutique avec SweetAlert2
    window.deleteShop = function(shopId) {
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Cette action est irréversible !",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`{{ url('admin/shops') }}/${shopId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) throw response;
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        Swal.fire('Supprimé !', data.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('Erreur', data.message, 'error');
                    }
                })
                .catch(error => {
                    if (error instanceof Response) {
                        error.json().then(data => {
                            Swal.fire('Erreur', data.message || "Une erreur est survenue", 'error');
                        });
                    } else {
                        Swal.fire('Erreur', error.message || "Une erreur est survenue", 'error');
                    }
                });
            }
        });
    };

    // Toast personnalisé (inchangé)
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            info: 'bg-blue-500',
            warning: 'bg-yellow-500',
        };
        toast.className = `fixed top-5 right-5 z-50 px-4 py-2 rounded shadow text-white text-sm ${colors[type] || colors.info}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3500);
    }
});
</script>
@endpush
