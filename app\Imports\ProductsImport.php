<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Shop;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ProductsImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError
{
    use SkipsErrors;

    protected $categoryId;
    protected $successCount = 0;
    protected $errorCount = 0;
    protected $errorMessages = [];
    protected $shops;

    public function __construct($categoryId)
    {
        $this->categoryId = $categoryId;
        $this->shops = Shop::all()->keyBy('id');
    }

    public function model(array $row)
    {
        try {
            // Créer ou mettre à jour le produit
            $product = Product::updateOrCreate(
                ['sku' => $row['sku']],
                [
                    'category_id' => $this->categoryId,
                    'name' => $row['nom'],
                    'description' => $row['description'] ?? null,
                    'barcode' => $row['code_barres'] ?? null,
                    'unit_price' => $row['prix_achat'],
                    'selling_price' => $row['prix_vente'],
                    'min_stock' => $row['stock_minimum'],
                    'status' => strtolower($row['statut']) === 'actif' ? 'active' : 'inactive'
                ]
            );

            // Gérer les stocks pour chaque boutique
            foreach ($this->shops as $shop) {
                $stockKey = 'stock_boutique_' . $shop->id;
                if (isset($row[$stockKey])) {
                    $product->stocks()->updateOrCreate(
                        ['shop_id' => $shop->id],
                        [
                            'quantity' => $row[$stockKey],
                            'min_stock' => $row['stock_minimum']
                        ]
                    );
                }
            }

            $this->successCount++;
            return $product;

        } catch (\Exception $e) {
            $this->errorCount++;
            $this->errorMessages[] = "Ligne {$row['nom']} : " . $e->getMessage();
            return null;
        }
    }

    public function rules(): array
    {
        return [
            'sku' => 'required|string|max:50',
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string',
            'code_barres' => 'nullable|string|size:13',
            'prix_achat' => 'required|numeric|min:0',
            'prix_vente' => 'required|numeric|min:0',
            'stock_minimum' => 'required|integer|min:0',
            'statut' => 'required|in:Actif,Inactif,actif,inactif',
            '*.stock_boutique_*' => 'nullable|integer|min:0'
        ];
    }

    public function customValidationMessages()
    {
        return [
            'sku.required' => 'Le SKU est obligatoire.',
            'nom.required' => 'Le nom est obligatoire.',
            'prix_achat.required' => 'Le prix d\'achat est obligatoire.',
            'prix_vente.required' => 'Le prix de vente est obligatoire.',
            'stock_minimum.required' => 'Le stock minimum est obligatoire.',
            'statut.required' => 'Le statut est obligatoire.',
            'statut.in' => 'Le statut doit être "Actif" ou "Inactif".'
        ];
    }

    public function getResults(): array
    {
        return [
            'success' => $this->successCount,
            'errors' => $this->errorCount,
            'error_messages' => $this->errorMessages
        ];
    }
}
