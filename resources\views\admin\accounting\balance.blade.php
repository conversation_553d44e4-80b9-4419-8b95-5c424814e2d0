@extends('layouts.admin')
@section('content')
<div class="container max-w-4xl mx-auto py-8">
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-blue-800 mb-2 flex items-center">
            <i class="fas fa-balance-scale mr-2"></i> Balance Mensuelle
        </h2>
        <p class="text-gray-500 mb-4">Visualisez l’évolution des revenus, dépenses et soldes nets mois par mois.</p>
        <form method="GET" action="{{ route('admin.accounting.balance') }}" class="flex flex-wrap gap-4 items-end bg-white shadow-md rounded-lg p-4">
            <div>
                <label class="block text-sm font-semibold text-gray-600">Du</label>
                <input type="date" name="from" class="form-control rounded-lg" value="{{ $from }}">
            </div>
            <div>
                <label class="block text-sm font-semibold text-gray-600">Au</label>
                <input type="date" name="to" class="form-control rounded-lg" value="{{ $to }}">
            </div>
            <div>
                <button class="btn btn-primary px-6 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 text-white font-bold shadow hover:from-blue-600 hover:to-blue-800 transition-all flex items-center" type="submit">
                    <i class="fas fa-search mr-2"></i>Afficher
                </button>
            </div>
        </form>
    </div>
    <div class="card shadow-lg border-0">
        <div class="card-body p-0">
            <div class="overflow-x-auto">
            <table class="table table-bordered table-hover mb-0 animate-fade-in" style="min-width:700px">
                <thead class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
                    <tr>
                        <th>Mois</th>
                        <th>Dépenses</th>
                        <th>Revenus</th>
                        <th>Solde Net</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($monthlyBalances as $balance)
                        <tr class="transition hover:bg-blue-50">
                            <td>{{ \Carbon\Carbon::createFromFormat('Y-m', $balance->month)->translatedFormat('F Y') }}</td>
                            <td class="text-end text-red-700 font-bold">
                                <i class="fas fa-arrow-down mr-1"></i>{{ number_format($balance->total_expenses, 2, ',', ' ') }} FCFA
                            </td>
                            <td class="text-end text-green-700 font-bold">
                                <i class="fas fa-arrow-up mr-1"></i>{{ number_format($balance->total_revenue, 2, ',', ' ') }} FCFA
                            </td>
                            <td class="text-end font-semibold {{ $balance->net_balance >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($balance->net_balance, 2, ',', ' ') }} FCFA
                            </td>
                            <td>
                                @if($balance->net_balance >= 0)
                                    <span class="px-3 py-1 rounded-full bg-green-100 text-green-800 text-xs font-bold">Excédent</span>
                                @else
                                    <span class="px-3 py-1 rounded-full bg-red-100 text-red-800 text-xs font-bold">Déficit</span>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center text-gray-500 py-8">
                                <i class="fas fa-info-circle mr-2"></i>Aucune donnée disponible pour cette période.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>
<style>
@keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: none; } }
.animate-fade-in { animation: fade-in 0.7s cubic-bezier(.4,0,.2,1); }
</style>
@endsection
