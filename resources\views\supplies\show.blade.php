@extends('layouts.app')

@section('title', 'Détails de l\'approvisionnement')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            Approvisionnement {{ $supply->reference_number }}
            <span class="badge bg-{{ $supply->status_color }}">{{ $supply->status_label }}</span>
        </h1>
        <div>
            @if($supply->canBeValidated() && auth()->user()->can('validate', $supply))
                <form action="{{ route('supplies.validate', $supply) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PUT')
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Valider
                    </button>
                </form>
            @endif
            @if($supply->canBeReceived() && auth()->user()->can('receive', $supply))
                <a href="{{ route('supplies.receive.form', $supply) }}" class="btn btn-warning">
                    <i class="fas fa-box"></i> Réceptionner
                </a>
            @endif
            <a href="{{ route('supplies.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations Générales</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Référence</dt>
                        <dd class="col-sm-8">{{ $supply->reference_number }}</dd>

                        <dt class="col-sm-4">Boutique</dt>
                        <dd class="col-sm-8">{{ $supply->shop->name }}</dd>

                        <dt class="col-sm-4">Fournisseur</dt>
                        <dd class="col-sm-8">{{ $supply->supplier->name }}</dd>

                        <dt class="col-sm-4">Date commande</dt>
                        <dd class="col-sm-8">{{ $supply->order_date->format('d/m/Y') }}</dd>

                        <dt class="col-sm-4">Livraison prévue</dt>
                        <dd class="col-sm-8">{{ $supply->expected_delivery_date->format('d/m/Y') }}</dd>

                        @if($supply->received_date)
                            <dt class="col-sm-4">Date réception</dt>
                            <dd class="col-sm-8">{{ $supply->received_date->format('d/m/Y') }}</dd>
                        @endif

                        <dt class="col-sm-4">Montant total</dt>
                        <dd class="col-sm-8">{{ number_format($supply->total_amount, 2) }} €</dd>
                    </dl>
                </div>
            </div>

            @if($supply->notes)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Notes</h5>
                    </div>
                    <div class="card-body">
                        {{ $supply->notes }}
                    </div>
                </div>
            @endif
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Produits</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité commandée</th>
                                    @if($supply->status === 'received' || $supply->status === 'partially_received')
                                        <th>Quantité reçue</th>
                                    @endif
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($supply->items as $item)
                                    <tr>
                                        <td>{{ $item->product->name }}</td>
                                        <td>{{ $item->ordered_quantity }}</td>
                                        @if($supply->status === 'received' || $supply->status === 'partially_received')
                                            <td>
                                                {{ $item->received_quantity }}
                                                @if($item->notes)
                                                    <i class="fas fa-info-circle" data-bs-toggle="tooltip" 
                                                       title="{{ $item->notes }}"></i>
                                                @endif
                                            </td>
                                        @endif
                                        <td>{{ number_format($item->unit_price, 2) }} €</td>
                                        <td>{{ number_format($item->total_price, 2) }} €</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="{{ $supply->status === 'received' || $supply->status === 'partially_received' ? '4' : '3' }}" 
                                        class="text-end">
                                        <strong>Total</strong>
                                    </td>
                                    <td>
                                        <strong>{{ number_format($supply->total_amount, 2) }} €</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Historique</h5>
                </div>
                <div class="card-body">
                    <ul class="timeline">
                        <li class="timeline-item">
                            <strong>Commande créée</strong>
                            <span class="text-muted ms-2">
                                {{ $supply->created_at->format('d/m/Y H:i') }}
                            </span>
                        </li>

                        @if($supply->validated_by)
                            <li class="timeline-item">
                                <strong>Validé par</strong> {{ $supply->validator->name }}
                                <span class="text-muted ms-2">
                                    {{ $supply->updated_at->format('d/m/Y H:i') }}
                                </span>
                            </li>
                        @endif

                        @if($supply->received_by)
                            <li class="timeline-item">
                                <strong>Réceptionné par</strong> {{ $supply->receiver->name }}
                                <span class="text-muted ms-2">
                                    {{ $supply->received_date->format('d/m/Y H:i') }}
                                </span>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .timeline {
        list-style: none;
        padding: 0;
    }

    .timeline-item {
        position: relative;
        padding-left: 24px;
        margin-bottom: 1rem;
    }

    .timeline-item::before {
        content: "";
        position: absolute;
        left: 0;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #0d6efd;
    }

    .timeline-item:not(:last-child)::after {
        content: "";
        position: absolute;
        left: 5px;
        top: 18px;
        bottom: -12px;
        width: 2px;
        background-color: #dee2e6;
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    });
</script>
@endpush
