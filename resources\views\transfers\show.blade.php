@extends('layouts.app')

@section('title', 'Détails du transfert')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            Transfert {{ $transfer->reference_number }}
            <span class="badge bg-{{ $transfer->status_color }}">{{ $transfer->status_label }}</span>
        </h1>
        <div>
            @if($transfer->status === 'pending' && auth()->user()->can('validate', $transfer))
                <form action="{{ route('transfers.validate', $transfer) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PUT')
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Valider
                    </button>
                </form>
            @endif

            @if($transfer->status === 'validated' && auth()->user()->can('receive', $transfer))
                <a href="{{ route('transfers.receive.form', $transfer) }}" class="btn btn-warning">
                    <i class="fas fa-box"></i> Réceptionner
                </a>
            @endif

            @if($transfer->status === 'pending' && auth()->user()->can('cancel', $transfer))
                <form action="{{ route('transfers.cancel', $transfer) }}" method="POST" class="d-inline"
                      onsubmit="return confirm('Êtes-vous sûr de vouloir annuler ce transfert ?')">
                    @csrf
                    @method('PUT')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </form>
            @endif

            <a href="{{ route('transfers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Référence</dt>
                        <dd class="col-sm-8">{{ $transfer->reference_number }}</dd>

                        <dt class="col-sm-4">De</dt>
                        <dd class="col-sm-8">{{ $transfer->fromShop->name }}</dd>

                        <dt class="col-sm-4">Vers</dt>
                        <dd class="col-sm-8">{{ $transfer->toShop->name }}</dd>

                        <dt class="col-sm-4">Date transfert</dt>
                        <dd class="col-sm-8">{{ $transfer->transfer_date->format('d/m/Y') }}</dd>

                        @if($transfer->completed_date)
                            <dt class="col-sm-4">Date réception</dt>
                            <dd class="col-sm-8">{{ $transfer->completed_date->format('d/m/Y') }}</dd>
                        @endif
                    </dl>
                </div>
            </div>

            @if($transfer->notes)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Notes</h5>
                    </div>
                    <div class="card-body">
                        {{ $transfer->notes }}
                    </div>
                </div>
            @endif
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Produits</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité envoyée</th>
                                    @if($transfer->status === 'completed')
                                        <th>Quantité reçue</th>
                                    @endif
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transfer->items as $item)
                                    <tr>
                                        <td>{{ $item->product->name }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        @if($transfer->status === 'completed')
                                            <td>{{ $item->received_quantity }}</td>
                                        @endif
                                        <td>
                                            @if($item->notes)
                                                {{ $item->notes }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Historique</h5>
                </div>
                <div class="card-body">
                    <ul class="timeline">
                        <li class="timeline-item">
                            <strong>Transfert créé</strong> par {{ $transfer->creator->name }}
                            <span class="text-muted ms-2">
                                {{ $transfer->created_at->format('d/m/Y H:i') }}
                            </span>
                        </li>

                        @if($transfer->validator)
                            <li class="timeline-item">
                                <strong>Validé</strong> par {{ $transfer->validator->name }}
                                <span class="text-muted ms-2">
                                    {{ $transfer->validated_at->format('d/m/Y H:i') }}
                                </span>
                            </li>
                        @endif

                        @if($transfer->status === 'completed')
                            <li class="timeline-item">
                                <strong>Réceptionné</strong> par {{ $transfer->receiver->name }}
                                <span class="text-muted ms-2">
                                    {{ $transfer->completed_date->format('d/m/Y H:i') }}
                                </span>
                            </li>
                        @endif

                        @if($transfer->status === 'cancelled')
                            <li class="timeline-item text-danger">
                                <strong>Annulé</strong> par {{ $transfer->canceller->name }}
                                <span class="text-muted ms-2">
                                    {{ $transfer->cancelled_at->format('d/m/Y H:i') }}
                                </span>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .timeline {
        list-style: none;
        padding: 0;
    }

    .timeline-item {
        position: relative;
        padding-left: 24px;
        margin-bottom: 1rem;
    }

    .timeline-item::before {
        content: "";
        position: absolute;
        left: 0;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #0d6efd;
    }

    .timeline-item:not(:last-child)::after {
        content: "";
        position: absolute;
        left: 5px;
        top: 18px;
        bottom: -12px;
        width: 2px;
        background-color: #dee2e6;
    }

    .timeline-item.text-danger::before {
        background-color: #dc3545;
    }
</style>
@endpush
