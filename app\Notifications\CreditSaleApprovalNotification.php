<?php

namespace App\Notifications;

use App\Models\Sale;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CreditSaleApprovalNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $sale;

    public function __construct(Sale $sale)
    {
        $this->sale = $sale;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Approbation Requise - Vente à Crédit')
            ->greeting('Bonjour ' . $notifiable->name)
            ->line('Une nouvelle vente à crédit nécessite votre approbation.')
            ->line('Détails de la vente :')
            ->line("Client: {$this->sale->customer->name}")
            ->line("Montant: " . number_format($this->sale->total_amount, 2) . " FCFA")
            ->line("Boutique: {$this->sale->shop->name}")
            ->line("Date: {$this->sale->created_at}")
            ->action('Approuver/Rejeter', url('/sales/' . $this->sale->id . '/approve'))
            ->line('Veuillez examiner cette demande de crédit et prendre une décision.');
    }

    public function toArray($notifiable)
    {
        return [
            'sale_id' => $this->sale->id,
            'customer_name' => $this->sale->customer->name,
            'amount' => $this->sale->total_amount,
            'shop_name' => $this->sale->shop->name,
            'created_at' => $this->sale->created_at,
        ];
    }
}
