<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use App\Models\Product;
use App\Models\Supply;
use App\Models\SupplyRequest;
use App\Models\Stock;
use App\Models\Supplier;
use App\Notifications\LowStockNotification;
use App\Notifications\SupplyValidationNotification;
use App\Notifications\SupplyReceivedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;

class SupplyManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $mainShop;
    protected $branchShop;
    protected $supplier;
    protected $product;
    protected $stock;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic test data
        $this->admin = User::factory()->create()->assignRole('admin');
        $this->mainShop = Shop::factory()->create(['is_main' => true]);
        $this->branchShop = Shop::factory()->create(['is_main' => false]);
        $this->supplier = Supplier::factory()->create();
        $this->product = Product::factory()->create();
        $this->stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->mainShop->id,
            'quantity' => 50,
            'min_stock' => 20
        ]);
    }

    /** @test */
    public function main_shop_can_create_supply_order()
    {
        Notification::fake();

        $response = $this->actingAs($this->admin)
            ->postJson('/api/supplies', [
                'supplier_id' => $this->supplier->id,
                'shop_id' => $this->mainShop->id,
                'products' => [
                    [
                        'product_id' => $this->product->id,
                        'quantity' => 100,
                        'unit_price' => 1000
                    ]
                ]
            ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('supplies', [
            'supplier_id' => $this->supplier->id,
            'shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function branch_shop_can_request_supply_from_main_shop()
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/api/supply-requests', [
                'from_shop_id' => $this->branchShop->id,
                'to_shop_id' => $this->mainShop->id,
                'products' => [
                    [
                        'product_id' => $this->product->id,
                        'quantity' => 50
                    ]
                ]
            ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('supply_requests', [
            'from_shop_id' => $this->branchShop->id,
            'to_shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function system_sends_low_stock_notification()
    {
        Notification::fake();

        // Update stock to below minimum
        $this->stock->update(['quantity' => 10]);

        // Assert notification was sent
        Notification::assertSentTo(
            $this->admin,
            LowStockNotification::class,
            function ($notification) {
                return $notification->stock->id === $this->stock->id;
            }
        );
    }

    /** @test */
    public function admin_can_validate_supply_order()
    {
        $supply = Supply::factory()->create([
            'supplier_id' => $this->supplier->id,
            'shop_id' => $this->mainShop->id,
            'status' => 'pending'
        ]);

        $response = $this->actingAs($this->admin)
            ->putJson("/api/supplies/{$supply->id}/validate");

        $response->assertStatus(200);
        $this->assertDatabaseHas('supplies', [
            'id' => $supply->id,
            'status' => 'validated'
        ]);
    }

    /** @test */
    public function shop_can_confirm_supply_reception()
    {
        $supply = Supply::factory()->create([
            'supplier_id' => $this->supplier->id,
            'shop_id' => $this->mainShop->id,
            'status' => 'validated'
        ]);

        $response = $this->actingAs($this->admin)
            ->putJson("/api/supplies/{$supply->id}/receive", [
                'received_quantity' => 100,
                'notes' => 'All products received in good condition'
            ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('supplies', [
            'id' => $supply->id,
            'status' => 'received'
        ]);

        // Check if stock was updated
        $this->assertDatabaseHas('stocks', [
            'product_id' => $this->product->id,
            'shop_id' => $this->mainShop->id,
            'quantity' => 150  // Original 50 + 100 received
        ]);
    }
}
