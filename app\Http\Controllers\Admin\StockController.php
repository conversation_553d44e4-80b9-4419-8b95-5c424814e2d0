<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Stock;
use App\Models\StockDetail;
use App\Models\Supplier;
use Illuminate\Http\Request;

class StockController extends Controller
{
    public function index()
    {
        $stocks = Stock::with(['supplier', 'shop', 'details.product'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.stocks.index', compact('stocks'));
    }

    public function create()
    {
        $products = Product::select('id', 'name', 'sku')->orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        return view('admin.stocks.create', compact('products', 'suppliers'));
    }

    public function store(Request $request)
    {
        // Si un panier est envoyé
        if ($request->has('cart')) {
            $cart = json_decode($request->input('cart'), true);
            if (!is_array($cart) || count($cart) === 0) {
                return back()->with('error', 'Le panier est vide ou invalide.');
            }
            $supplier = $request->input('supplier');
            $note = $request->input('note');
            $date = $request->input('date');
            $shop_id = $request->input('shop_id') ?? 1;

            // Création de l'en-tête d'approvisionnement
            $stock = \App\Models\Stock::create([
                'supplier_id' => $supplier,
                'shop_id' => $shop_id,
                'supply_date' => $date,
                'notes' => $note,
            ]);

            // Ajout des détails
            foreach ($cart as $item) {
                $validator = \Validator::make($item, [
                    'product_id' => 'required|exists:products,id',
                    'quantity' => 'required|integer|min:0',
                    'unit_price' => 'required|numeric|min:0',
                    'unit' => 'required|string|max:50',
                    'min_stock' => 'required|integer|min:0',
                ]);
                if ($validator->fails()) {
                    continue;
                }
                $total_price = ((float)$item['quantity']) * ((float)$item['unit_price']);
                $stock->details()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $total_price,
                    'min_stock' => $item['min_stock'],
                ]);
            }

            return redirect()->route('admin.stocks.index')
                ->with('success', 'Approvisionnement ajouté avec succès.');
        }

        // Mode classique (un seul produit)
        return back()->with('error', 'Veuillez utiliser le panier pour l’approvisionnement.');
    }

    public function edit(Stock $stock)
    {
        $products = Product::all();
        return view('admin.stocks.edit', compact('stock', 'products'));
    }

    public function update(Request $request, Stock $stock)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:0',
            'min_stock' => 'required|integer|min:0',
            'unit_price' => 'required|numeric|min:0',
        ]);

        $stock->update($validated);

        return redirect()->route('admin.stocks.index')
            ->with('success', 'Stock mis à jour avec succès.');
    }

    public function show($id)
    {
        $stock = Stock::with(['supplier', 'shop', 'details.product'])->findOrFail($id);
        return view('admin.stocks.show', compact('stock'));
    }

    public function destroy(Stock $stock)
    {
        // Pour chaque détail, déduire la quantité du stock du produit
        foreach ($stock->details as $detail) {
            $product = $detail->product;
            if ($product) {
                // On recharge le produit depuis la base pour éviter un cache éventuel
                $freshProduct = \App\Models\Product::find($product->id);
                if ($freshProduct) {
                    $freshProduct->stock = max(0, (int)$freshProduct->stock - (int)$detail->quantity);
                    $freshProduct->save();
                }
            }
        }
        $stock->delete();

        return redirect()->route('admin.stocks.index')
            ->with('success', 'Stock supprimé avec succès. Les quantités ont été déduites du stock des produits.');
    }
}
