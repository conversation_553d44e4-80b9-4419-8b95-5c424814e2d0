<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\Account;
use App\Models\User;

class JournalEntrySeeder extends Seeder
{
    public function run()
    {
        // Utilisateur et comptes de test
        $user = User::first();
        $accounts = Account::take(2)->get();

        // 3 écritures de test
        for ($i = 1; $i <= 3; $i++) {
            $entry = JournalEntry::create([
                'date' => now()->subDays($i),
                'reference' => 'PIECE-2025-00' . $i,
                'description' => 'Écriture de test n°' . $i,
                'user_id' => $user ? $user->id : 1,
                'account_id' => $accounts[0]->id ?? 1,
            ]);

            // Lignes associées (débit/crédit)
            JournalEntryLine::create([
                'journal_entry_id' => $entry->id,
                'account_id' => $accounts[0]->id ?? 1,
                'debit' => 10000 * $i,
                'credit' => 0,
                'label' => 'Débit test',
            ]);
            JournalEntryLine::create([
                'journal_entry_id' => $entry->id,
                'account_id' => $accounts[1]->id ?? 2,
                'debit' => 0,
                'credit' => 10000 * $i,
                'label' => 'Crédit test',
            ]);
        }
    }
}
