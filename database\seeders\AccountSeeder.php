<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Account;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Plan comptable de base pour une activité commerciale
        $accounts = [
            // Actif
            ['code' => '1000', 'name' => 'Banques', 'type' => 'actif', 'parent_id' => null, 'description' => 'Comptes bancaires'],
            // Banques africaines principales
            ['code' => '1010', 'name' => 'BOA (Bank Of Africa)', 'type' => 'actif', 'parent_id' => null, 'description' => 'Bank Of Africa, opérations courantes', 'parent_code' => '1000'],
            ['code' => '1011', 'name' => 'BOA - Compte courant', 'type' => 'actif', 'parent_id' => null, 'description' => 'BOA Compte courant', 'parent_code' => '1010'],
            ['code' => '1012', 'name' => 'BOA - Compte devises', 'type' => 'actif', 'parent_id' => null, 'description' => 'BOA Compte en devises', 'parent_code' => '1010'],
            ['code' => '1020', 'name' => 'ORA BANK', 'type' => 'actif', 'parent_id' => null, 'description' => 'ORA BANK, opérations courantes', 'parent_code' => '1000'],
            ['code' => '1021', 'name' => 'ORA BANK - Compte courant', 'type' => 'actif', 'parent_id' => null, 'description' => 'ORA BANK Compte courant', 'parent_code' => '1020'],
            ['code' => '1022', 'name' => 'ORA BANK - Compte devises', 'type' => 'actif', 'parent_id' => null, 'description' => 'ORA BANK Compte en devises', 'parent_code' => '1020'],
            ['code' => '1030', 'name' => 'BSIC', 'type' => 'actif', 'parent_id' => null, 'description' => 'Banque Sahélo-Saharienne, opérations courantes', 'parent_code' => '1000'],
            ['code' => '1031', 'name' => 'BSIC - Compte courant', 'type' => 'actif', 'parent_id' => null, 'description' => 'BSIC Compte courant', 'parent_code' => '1030'],
            ['code' => '1032', 'name' => 'BSIC - Compte devises', 'type' => 'actif', 'parent_id' => null, 'description' => 'BSIC Compte en devises', 'parent_code' => '1030'],
            ['code' => '1040', 'name' => 'Société Générale', 'type' => 'actif', 'parent_id' => null, 'description' => 'Société Générale, opérations courantes', 'parent_code' => '1000'],
            ['code' => '1041', 'name' => 'Société Générale - Compte courant', 'type' => 'actif', 'parent_id' => null, 'description' => 'SG Compte courant', 'parent_code' => '1040'],
            ['code' => '1042', 'name' => 'Société Générale - Compte devises', 'type' => 'actif', 'parent_id' => null, 'description' => 'SG Compte en devises', 'parent_code' => '1040'],
            ['code' => '1050', 'name' => 'ECOBANK', 'type' => 'actif', 'parent_id' => null, 'description' => 'ECOBANK, opérations courantes', 'parent_code' => '1000'],
            ['code' => '1051', 'name' => 'ECOBANK - Compte courant', 'type' => 'actif', 'parent_id' => null, 'description' => 'ECOBANK Compte courant', 'parent_code' => '1050'],
            ['code' => '1052', 'name' => 'ECOBANK - Compte devises', 'type' => 'actif', 'parent_id' => null, 'description' => 'ECOBANK Compte en devises', 'parent_code' => '1050'],
            ['code' => '1060', 'name' => 'Autres banques', 'type' => 'actif', 'parent_id' => null, 'description' => 'Banques diverses', 'parent_code' => '1000'],
            ['code' => '1100', 'name' => 'Caisse', 'type' => 'actif', 'parent_id' => null, 'description' => 'Caisse en espèces'],
            ['code' => '1200', 'name' => 'Clients', 'type' => 'actif', 'parent_id' => null, 'description' => 'Créances clients'],
            ['code' => '1201', 'name' => 'Clients France', 'type' => 'actif', 'parent_id' => null, 'description' => 'Clients nationaux', 'parent_code' => '1200'],
            ['code' => '1202', 'name' => 'Clients Export', 'type' => 'actif', 'parent_id' => null, 'description' => 'Clients internationaux', 'parent_code' => '1200'],
            ['code' => '1300', 'name' => 'Stocks de marchandises', 'type' => 'actif', 'parent_id' => null, 'description' => 'Stocks disponibles'],
            ['code' => '1301', 'name' => 'Stocks matières premières', 'type' => 'actif', 'parent_id' => null, 'description' => 'Matières premières', 'parent_code' => '1300'],
            ['code' => '1302', 'name' => 'Stocks produits finis', 'type' => 'actif', 'parent_id' => null, 'description' => 'Produits finis', 'parent_code' => '1300'],
            // Passif
            ['code' => '2000', 'name' => 'Fournisseurs', 'type' => 'passif', 'parent_id' => null, 'description' => 'Dettes fournisseurs'],
            ['code' => '2001', 'name' => 'Fournisseurs France', 'type' => 'passif', 'parent_id' => null, 'description' => 'Fournisseurs nationaux', 'parent_code' => '2000'],
            ['code' => '2002', 'name' => 'Fournisseurs Export', 'type' => 'passif', 'parent_id' => null, 'description' => 'Fournisseurs internationaux', 'parent_code' => '2000'],
            ['code' => '2100', 'name' => 'Emprunts', 'type' => 'passif', 'parent_id' => null, 'description' => 'Emprunts bancaires'],
            ['code' => '2200', 'name' => 'TVA à payer', 'type' => 'passif', 'parent_id' => null, 'description' => 'TVA collectée à reverser'],
            // Capitaux propres
            ['code' => '3000', 'name' => 'Capital social', 'type' => 'capitaux propres', 'parent_id' => null, 'description' => 'Capital de l’entreprise'],
            ['code' => '3100', 'name' => 'Réserves', 'type' => 'capitaux propres', 'parent_id' => null, 'description' => 'Réserves légales et statutaires'],
            // Produits
            ['code' => '4000', 'name' => 'Ventes de marchandises', 'type' => 'produit', 'parent_id' => null, 'description' => 'Chiffre d’affaires ventes'],
            ['code' => '4001', 'name' => 'Ventes France', 'type' => 'produit', 'parent_id' => null, 'description' => 'Ventes nationales', 'parent_code' => '4000'],
            ['code' => '4002', 'name' => 'Ventes Export', 'type' => 'produit', 'parent_id' => null, 'description' => 'Ventes internationales', 'parent_code' => '4000'],
            ['code' => '4100', 'name' => 'Prestations de services', 'type' => 'produit', 'parent_id' => null, 'description' => 'Revenus prestations'],
            // Charges
            ['code' => '5000', 'name' => 'Achats de marchandises', 'type' => 'charge', 'parent_id' => null, 'description' => 'Achats destinés à la revente'],
            ['code' => '5001', 'name' => 'Achats France', 'type' => 'charge', 'parent_id' => null, 'description' => 'Achats nationaux', 'parent_code' => '5000'],
            ['code' => '5002', 'name' => 'Achats Import', 'type' => 'charge', 'parent_id' => null, 'description' => 'Achats importés', 'parent_code' => '5000'],
            ['code' => '5100', 'name' => 'Frais de personnel', 'type' => 'charge', 'parent_id' => null, 'description' => 'Salaires et charges sociales'],
            ['code' => '5200', 'name' => 'Loyers', 'type' => 'charge', 'parent_id' => null, 'description' => 'Loyers des locaux'],
            ['code' => '5300', 'name' => 'Frais généraux', 'type' => 'charge', 'parent_id' => null, 'description' => 'Frais administratifs, fournitures, etc.'],
            ['code' => '5400', 'name' => 'TVA déductible', 'type' => 'charge', 'parent_id' => null, 'description' => 'TVA sur achats'],
        ];

        // Création des comptes principaux
        $accountIds = [];
        foreach ($accounts as $data) {
            $parentId = null;
            if (isset($data['parent_code'])) {
                $parent = Account::where('code', $data['parent_code'])->first();
                $parentId = $parent ? $parent->id : null;
            }
            $insertData = array_merge(
                array_diff_key($data, ['parent_code' => 1]),
                ['parent_id' => $parentId]
            );
            $account = Account::firstOrCreate([
                'code' => $data['code'],
            ], $insertData);
            $accountIds[$data['code']] = $account->id;
        }
    }
}
