@extends('layouts.admin')

@section('title', 'Ajouter un approvisionnement')

@section('content')
<div class="container mx-auto px-6 py-8" x-data="approvisionnement()">
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Ajouter un approvisionnement</h1>
        <a href="{{ route('admin.stocks.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            <i class="fas fa-arrow-left mr-2"></i>Retour
        </a>
    </div>

    <div class="mt-8">
        <!-- Champs supplémentaires pour l'approvisionnement -->
        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="supplier">
                    Fournisseur
                </label>
                <div class="flex items-center gap-2">
                    <select x-model="supplier" id="supplier" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Sélectionnez un fournisseur</option>
                        @foreach($suppliers as $s)
                            <option value="{{ $s->id }}">{{ $s->name }}</option>
                        @endforeach
                    </select>
                    <button type="button" @click="showAddSupplierModal = true" class="bg-green-500 hover:bg-green-600 text-white rounded px-2 py-1 text-lg font-bold">+</button>
                </div>
                <!-- MODALE d'ajout de fournisseur -->
                <div x-show="showAddSupplierModal" style="display: none;" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
                    <div class="bg-white rounded-lg shadow-lg w-full max-w-lg p-6 relative">
                        <button type="button" @click="showAddSupplierModal = false" class="absolute top-2 right-2 text-gray-400 hover:text-red-600 text-2xl">&times;</button>
                        <h2 class="text-xl font-bold mb-4">Nouveau fournisseur</h2>
                        <form @submit.prevent="submitSupplierModal">
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Nom *</label>
                                <input type="text" x-model="modalSupplier.name" class="border rounded w-full px-2 py-1" required>
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Contact</label>
                                <input type="text" x-model="modalSupplier.contact_name" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Email</label>
                                <input type="email" x-model="modalSupplier.email" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Téléphone</label>
                                <input type="text" x-model="modalSupplier.phone" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Adresse</label>
                                <input type="text" x-model="modalSupplier.address" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Ville</label>
                                <input type="text" x-model="modalSupplier.city" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Pays</label>
                                <input type="text" x-model="modalSupplier.country" class="border rounded w-full px-2 py-1">
                            </div>
                            <div class="mb-3">
                                <label class="block text-gray-700 mb-1">Numéro fiscal</label>
                                <input type="text" x-model="modalSupplier.tax_number" class="border rounded w-full px-2 py-1">
                            </div>
                            <div x-show="modalSupplierError" class="text-red-500 text-xs mb-2" x-text="modalSupplierError"></div>
                            <div class="flex justify-end gap-2">
                                <button type="button" @click="showAddSupplierModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 rounded px-4 py-2">Annuler</button>
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2">Enregistrer</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="date">
                    Date d'approvisionnement
                </label>
                <input type="date" x-model="date" id="date" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="note">
                    Note
                </label>
                <textarea x-model="note" id="note" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
            </div>
        </div>

        <!-- Formulaire d'ajout au panier -->
        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="product_id">
                    Produit
                </label>
                <select x-model="product_id" id="product_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Sélectionnez un produit</option>
                    @foreach($products as $product)
                        <option value="{{ $product->id }}">{{ $product->name }} ({{ $product->sku }})</option>
                    @endforeach
                </select>
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="quantity">
                    Quantité
                </label>
                <input type="number" x-model="quantity" min="0" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="unit">
                    Unité
                </label>
                <select x-model="unit" id="unit" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Sélectionnez une unité</option>
                    <option value="pièce">Pièce</option>
                    <option value="paquet">Paquet</option>
                    <option value="boîte">Boîte</option>
                    <option value="carton">Carton</option>
                    <option value="bouteille">Bouteille</option>
                    <option value="litre">Litre</option>
                    <option value="kilogramme">Kilogramme</option>
                    <option value="sachet">Sachet</option>
                    <option value="seau">Seau</option>
                    <option value="fût">Fût</option>
                </select>
            </div>

            <div class="mb-4" x-show="false">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="min_stock">
                    Stock minimum
                </label>
                <input type="number" x-model="min_stock" min="0" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>

            <div class="mb-6">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="unit_price">
                    Prix unitaire (FCFA)
                </label>
                <input type="number" x-model="unit_price" min="0" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>

            <button type="button" @click="addToCart()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Ajouter au panier
            </button>
        </div>

        <!-- Affichage du panier -->
        <div class="bg-gray-100 rounded-lg p-6 mb-6 shadow" x-show="cart.length > 0">
            <h2 class="text-xl font-semibold mb-4 flex items-center gap-2">
                <i class="fas fa-shopping-cart text-blue-500"></i> Panier d'approvisionnement
            </h2>
            <table class="min-w-full mb-4 bg-white rounded shadow overflow-hidden">
                <thead class="bg-blue-100">
                    <tr>
                        <th class="px-4 py-2 text-left">Produit</th>
                        <th class="px-4 py-2 text-right">Quantité</th>
                        <th class="px-4 py-2 text-right">Unité</th>
                        <th class="px-4 py-2 text-right">Prix unitaire</th>
                        <th class="px-4 py-2 text-right">Stock minimum</th>
                        <th class="px-4 py-2 text-right">Sous-total</th>
                        <th class="px-4 py-2 text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(item, index) in cart" :key="item.product_id">
                        <tr class="hover:bg-blue-50">
                            <td class="border px-4 py-2" x-text="item.product_name"></td>
                            <td class="border px-4 py-2 text-right">
                                <input type="number" min="0" x-model="item.quantity" class="w-20 border rounded text-right" />
                            </td>
                            <td class="border px-4 py-2 text-right">
                                <select x-model="item.unit" class="w-24 border rounded text-right">
                                    <option value="">Unité</option>
                                    <option value="pièce">Pièce</option>
                                    <option value="paquet">Paquet</option>
                                    <option value="boîte">Boîte</option>
                                    <option value="carton">Carton</option>
                                    <option value="bouteille">Bouteille</option>
                                    <option value="litre">Litre</option>
                                    <option value="kilogramme">Kilogramme</option>
                                    <option value="sachet">Sachet</option>
                                    <option value="seau">Seau</option>
                                    <option value="fût">Fût</option>
                                </select>
                            </td>
                            <td class="border px-4 py-2 text-right">
                                <input type="number" min="0" x-model="item.unit_price" class="w-24 border rounded text-right" />
                            </td>
                            <td class="border px-4 py-2 text-right">
                                <input type="number" min="0" x-model="item.min_stock" class="w-24 border rounded text-right" />
                            </td>
                            <td class="border px-4 py-2 text-right font-semibold text-blue-700">
                                <span x-text="(item.quantity && item.unit_price) ? (item.quantity * item.unit_price).toLocaleString() : '0'"></span> FCFA
                            </td>
                            <td class="border px-4 py-2 text-center">
                                <button type="button" @click="removeFromCart(index)" class="bg-red-100 hover:bg-red-200 text-red-600 font-bold rounded px-3 py-1 transition">Supprimer</button>
                            </td>
                        </tr>
                    </template>
                </tbody>
                <tfoot>
                    <tr class="bg-blue-50">
                        <td colspan="3" class="px-4 py-2 text-right font-bold">Total</td>
                        <td class="px-4 py-2 text-right font-extrabold text-green-700">
                            <span x-text="cart.reduce((sum, item) => sum + (parseFloat(item.quantity)||0)*(parseFloat(item.unit_price)||0), 0).toLocaleString()"></span> FCFA
                        </td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
            <form :action="'{{ route('admin.stocks.store') }}'" method="POST" @submit.prevent="submitCart">
                @csrf
                <input type="hidden" name="cart" :value="JSON.stringify(cart)">
                <input type="hidden" name="supplier" :value="supplier">
                <input type="hidden" name="date" :value="date">
                <input type="hidden" name="note" :value="note">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded text-lg shadow">
                    <i class="fas fa-check mr-2"></i>Valider l'approvisionnement
                </button>
            </form>
        </div>
    </div>
</div>

<script>
function approvisionnement() {
    return {
        product_id: '',
        quantity: 0,
        unit_price: 0,
        unit: '',
        min_stock: 0,
        cart: [],
        supplier: '',
        date: new Date().toISOString().substr(0, 10),
        note: '',
        showAddSupplierModal: false,
        modalSupplier: {
            name: '',
            contact_name: '',
            email: '',
            phone: '',
            address: '',
            city: '',
            country: '',
            tax_number: ''
        },
        modalSupplierError: '',
        products: @json($products->mapWithKeys(fn($p) => [$p->id => ['name' => $p->name, 'min_stock' => $p->min_stock, 'sku' => $p->sku]])),
        addToCart() {
            if (!this.product_id || this.quantity <= 0) return;
            
            // Trouver le produit dans la liste des produits
            const product = this.products[this.product_id];
            if (!product) return;
            
            // Vérifier si le produit est déjà dans le panier
            const existingItemIndex = this.cart.findIndex(item => item.product_id == this.product_id);
            
            if (existingItemIndex !== -1) {
                // Mettre à jour la quantité si le produit existe déjà
                this.cart[existingItemIndex].quantity = parseInt(this.cart[existingItemIndex].quantity) + parseInt(this.quantity);
            } else {
                // Ajouter un nouvel item au panier
                this.cart.push({
                    product_id: this.product_id,
                    product_name: `${product.name} (${product.sku})`,
                    quantity: this.quantity,
                    unit_price: this.unit_price,
                    unit: this.unit,
                    min_stock: this.min_stock
                });
            }
            
            this.resetProductForm();
        },
        removeFromCart(index) {
            this.cart.splice(index, 1);
        },
        submitSupplierModal() {
            this.modalSupplierError = '';
            if (!this.modalSupplier.name.trim()) {
                this.modalSupplierError = 'Veuillez saisir un nom de fournisseur.';
                return;
            }
            fetch('/admin/suppliers/ajax-create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(this.modalSupplier)
            })
            .then(res => res.json())
            .then(data => {
                if (data.id && data.name) {
                    let select = document.getElementById('supplier');
                    let option = document.createElement('option');
                    option.value = data.id;
                    option.text = data.name;
                    select.appendChild(option);
                    this.supplier = data.id;
                    this.modalSupplier = { name: '', contact_name: '', email: '', phone: '', address: '', city: '', country: '', tax_number: '' };
                    this.showAddSupplierModal = false;
                    Swal.fire({
                        icon: 'success',
                        title: 'Fournisseur ajouté !',
                        text: 'Le fournisseur a été créé et sélectionné avec succès.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else if (data.error) {
                    this.modalSupplierError = data.error;
                }
            })
            .catch(() => {
                this.modalSupplierError = 'Erreur lors de l\'ajout du fournisseur.';
            });
        },
        submitCart() {
            // Crée un formulaire caché et le soumet
            let form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('admin.stocks.store') }}';
            let token = document.createElement('input');
            token.type = 'hidden';
            token.name = '_token';
            token.value = '{{ csrf_token() }}';
            form.appendChild(token);
            let cartInput = document.createElement('input');
            cartInput.type = 'hidden';
            cartInput.name = 'cart';
            cartInput.value = JSON.stringify(this.cart);
            form.appendChild(cartInput);
            let supplierInput = document.createElement('input');
            supplierInput.type = 'hidden';
            supplierInput.name = 'supplier';
            supplierInput.value = this.supplier;
            form.appendChild(supplierInput);
            let dateInput = document.createElement('input');
            dateInput.type = 'hidden';
            dateInput.name = 'date';
            dateInput.value = this.date;
            form.appendChild(dateInput);
            let noteInput = document.createElement('input');
            noteInput.type = 'hidden';
            noteInput.name = 'note';
            noteInput.value = this.note;
            form.appendChild(noteInput);
            document.body.appendChild(form);
            form.submit();
        },
        resetProductForm() {
            this.product_id = '';
            this.quantity = 0;
            this.unit_price = 0;
            this.unit = '';
            this.min_stock = 0;
        }
    }
}
</script>
@endsection
