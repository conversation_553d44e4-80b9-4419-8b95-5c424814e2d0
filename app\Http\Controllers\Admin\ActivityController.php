<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;
use App\Models\User;
use App\Models\Shop;

class ActivityController extends Controller
{
    public function index()
    {
        $activities = Activity::with(['causer', 'subject'])
            ->latest()
            ->paginate(20);

        $users = User::all();
        $shops = Shop::all();

        return view('admin.activities.index', compact('activities', 'users', 'shops'));
    }
}
