<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Biller\DashboardController;
use App\Http\Controllers\Biller\SaleController;
use App\Http\Controllers\Biller\CustomerController;

Route::middleware(['auth', 'role:biller'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('biller.dashboard');
    
    // Gestion des ventes et factures
    Route::resource('sales', SaleController::class)->names([
        'index' => 'biller.sales.index',
        'create' => 'biller.sales.create',
        'store' => 'biller.sales.store',
        'show' => 'biller.sales.show',
        'edit' => 'biller.sales.edit',
        'update' => 'biller.sales.update',
        'destroy' => 'biller.sales.destroy',
    ]);
    
    // Impression des factures
    Route::get('/sales/{sale}/print', [SaleController::class, 'print'])->name('biller.sales.print');
    Route::get('/sales/{sale}/print-delivery', [SaleController::class, 'printDelivery'])->name('biller.sales.print-delivery');
    
    // Gestion des paiements
    Route::post('/sales/{sale}/payments', [SaleController::class, 'addPayment'])->name('biller.sales.payments.store');
    Route::get('/sales/{sale}/payments/{payment}/receipt', [SaleController::class, 'printReceipt'])->name('biller.sales.payments.receipt');
    
    // Gestion des clients
    Route::resource('customers', CustomerController::class)->names([
        'index' => 'biller.customers.index',
        'create' => 'biller.customers.create',
        'store' => 'biller.customers.store',
        'show' => 'biller.customers.show',
        'edit' => 'biller.customers.edit',
        'update' => 'biller.customers.update',
        'destroy' => 'biller.customers.destroy',
    ]);
    
    // Rapports
    Route::get('/reports/daily-sales', [ReportController::class, 'dailySales'])->name('biller.reports.daily-sales');
    Route::get('/reports/payment-methods', [ReportController::class, 'paymentMethods'])->name('biller.reports.payment-methods');
    Route::get('/reports/customer-sales', [ReportController::class, 'customerSales'])->name('biller.reports.customer-sales');
});
