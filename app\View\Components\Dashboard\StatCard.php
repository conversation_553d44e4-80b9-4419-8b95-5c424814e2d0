<?php

namespace App\View\Components\Dashboard;

use Illuminate\View\Component;

class StatCard extends Component
{
    public $title;
    public $value;
    public $icon;
    public $color;
    public $percentage;
    public $trend;
    public $subtitle;

    public function __construct($title, $value, $icon, $color = 'blue', $percentage = null, $trend = null, $subtitle = null)
    {
        $this->title = $title;
        $this->value = $value;
        $this->icon = $icon;
        $this->color = $color;
        $this->percentage = $percentage;
        $this->trend = $trend;
        $this->subtitle = $subtitle;
    }

    public function render()
    {
        return view('components.dashboard.stat-card');
    }
}
