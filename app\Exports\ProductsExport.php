<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductsExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    public function query()
    {
        return $this->query->with(['category', 'stocks']);
    }

    public function headings(): array
    {
        return [
            'SKU',
            'Nom',
            'Catégorie',
            'Description',
            'Code-barres',
            'Prix d\'achat',
            'Prix de vente',
            'Marge',
            'Stock minimum',
            'Stock total',
            'Valeur du stock',
            'Statut',
            'Date de création'
        ];
    }

    public function map($product): array
    {
        return [
            $product->sku,
            $product->name,
            $product->category->name,
            $product->description,
            $product->barcode,
            $product->unit_price,
            $product->selling_price,
            $product->selling_price - $product->unit_price,
            $product->min_stock,
            $product->stocks->sum('quantity'),
            $product->stocks->sum('quantity') * $product->unit_price,
            $product->status === 'active' ? 'Actif' : 'Inactif',
            $product->created_at->format('d/m/Y')
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'F:K' => ['numberFormat' => ['formatCode' => '#,##0']],
        ];
    }
}
