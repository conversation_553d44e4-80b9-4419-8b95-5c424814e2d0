<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateProfileRequest;
use App\Http\Requests\UpdatePasswordRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    public function show()
    {
        return view('profile.show', [
            'user' => auth()->user()
        ]);
    }

    public function edit()
    {
        return view('profile.edit', [
            'user' => auth()->user()
        ]);
    }

    public function editPassword()
    {
        return view('profile.password', [
            'user' => auth()->user()
        ]);
    }

    public function update(UpdateProfileRequest $request)
    {
        $user = auth()->user();

        // Gérer l'upload de la photo de profil
        if ($request->hasFile('avatar')) {
            // Supprimer l'ancienne photo si elle existe
            if ($user->avatar_path) {
                Storage::disk('public')->delete($user->avatar_path);
            }

            // Déterminer le rôle principal de l'utilisateur
            $role = $user->roles->first() ? strtolower($user->roles->first()->name) : 'user';
            $baseDir = 'upload';
            $roleDir = $role . '_images';
            $storagePath = $baseDir . DIRECTORY_SEPARATOR . $roleDir;

            // Créer le dossier s'il n'existe pas
            if (!\File::exists(public_path($storagePath))) {
                \File::makeDirectory(public_path($storagePath), 0755, true);
            }

            // Générer le nom du fichier : [role]_[nom_utilisateur]_[date]_[rang].extension
            $date = now()->format('Ymd_His');
            $userName = preg_replace('/[^A-Za-z0-9]/', '', strtolower($user->name));
            $rang = 1;
            $extension = $request->file('avatar')->extension();
            do {
                $fileName = $role . '_' . $userName . '_' . $date . '_' . $rang . '.' . $extension;
                $fullPath = public_path($storagePath . DIRECTORY_SEPARATOR . $fileName);
                $rang++;
            } while (file_exists($fullPath));

            // Déplacer le fichier uploadé
            $request->file('avatar')->move(public_path($storagePath), $fileName);

            // Stocker le chemin relatif à partir de 'public/' pour l'avatar_path (toujours avec des /)
            $user->avatar_path = str_replace('\\', '/', $storagePath . '/' . $fileName);
        }

        // Mettre à jour les informations de l'utilisateur
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'language' => $request->language,
            'timezone' => $request->timezone
        ]);

        return redirect()
            ->route('profile.show')
            ->with('success', 'Profil mis à jour avec succès.');
    }

    public function updatePassword(UpdatePasswordRequest $request)
    {
        $user = auth()->user();

        // Vérifier l'ancien mot de passe
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors([
                'current_password' => 'Le mot de passe actuel est incorrect.'
            ]);
        }

        // Mettre à jour le mot de passe
        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()
            ->route('profile.show')
            ->with('success', 'Mot de passe mis à jour avec succès.');
    }

    public function deleteAvatar()
    {
        $user = auth()->user();

        if ($user->avatar_path) {
            Storage::disk('public')->delete($user->avatar_path);
            $user->update(['avatar_path' => null]);
        }

        return redirect()
            ->route('profile.show')
            ->with('success', 'Photo de profil supprimée avec succès.');
    }

    public function notifications()
    {
        return view('profile.notifications', [
            'user' => auth()->user()
        ]);
    }

    public function updateNotifications(Request $request)
    {
        $user = auth()->user();

        $user->notification_preferences()->update([
            'email_notifications' => $request->has('email_notifications'),
            'sales_notifications' => $request->has('sales_notifications'),
            'stock_notifications' => $request->has('stock_notifications'),
            'payment_notifications' => $request->has('payment_notifications')
        ]);

        return redirect()
            ->route('profile.notifications')
            ->with('success', 'Préférences de notification mises à jour avec succès.');
    }
}
