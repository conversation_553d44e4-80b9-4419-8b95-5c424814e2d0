<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Manager\DashboardController;

Route::middleware(['auth', 'role:manager'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('manager.dashboard');
    
    // Gestion des ventes
    Route::resource('sales', SaleController::class);
    
    // Gestion des stocks
    Route::resource('products', ProductController::class);
    Route::get('/products/{product}/stock', [ProductController::class, 'stock'])->name('manager.products.stock');
    Route::post('/products/{product}/stock', [ProductController::class, 'updateStock'])->name('manager.products.stock.update');
    
    // Gestion des livraisons
    Route::resource('deliveries', DeliveryController::class);
    
    // Gestion des clients
    Route::resource('customers', CustomerController::class);
    
    // Rapports de la boutique
    Route::get('/reports/daily', [ReportController::class, 'daily'])->name('manager.reports.daily');
    Route::get('/reports/monthly', [ReportController::class, 'monthly'])->name('manager.reports.monthly');
    Route::get('/reports/inventory', [ReportController::class, 'inventory'])->name('manager.reports.inventory');
});
