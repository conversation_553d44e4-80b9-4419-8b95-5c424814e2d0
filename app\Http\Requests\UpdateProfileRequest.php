<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($this->user()->id),
            ],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:255'],
            'language' => ['required', 'string', 'in:fr,en'],
            'timezone' => ['required', 'string', 'timezone'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Le nom est obligatoire.',
            'name.max' => 'Le nom ne peut pas dépasser 255 caractères.',
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'L\'adresse email doit être valide.',
            'email.unique' => 'Cette adresse email est déjà utilisée.',
            'phone.max' => 'Le numéro de téléphone ne peut pas dépasser 20 caractères.',
            'address.max' => 'L\'adresse ne peut pas dépasser 255 caractères.',
            'language.required' => 'La langue est obligatoire.',
            'language.in' => 'La langue sélectionnée n\'est pas valide.',
            'timezone.required' => 'Le fuseau horaire est obligatoire.',
            'timezone.timezone' => 'Le fuseau horaire n\'est pas valide.',
            'avatar.image' => 'Le fichier doit être une image.',
            'avatar.mimes' => 'L\'image doit être au format JPEG, PNG ou JPG.',
            'avatar.max' => 'L\'image ne doit pas dépasser 2Mo.',
        ];
    }
}
