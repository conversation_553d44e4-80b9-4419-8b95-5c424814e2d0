@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Gestion des Recouvrements</h2>
                    <div>
                        <a href="{{ route('recoveries.dashboard') }}" class="btn btn-info me-2">Tableau de Bord</a>
                        <a href="{{ route('recoveries.create') }}" class="btn btn-primary">Nouveau Recouvrement</a>
                    </div>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($overdueCount > 0)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Il y a {{ $overdueCount }} recouvrement(s) en retard qui nécessitent votre attention.
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Référence</th>
                                    <th>Boutique</th>
                                    <th>Vente</th>
                                    <th>Montant</th>
                                    <th>Reste à payer</th>
                                    <th>Date d'échéance</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recoveries as $recovery)
                                    <tr class="{{ $recovery->isOverdue() ? 'table-danger' : '' }}">
                                        <td>{{ $recovery->reference_number }}</td>
                                        <td>{{ $recovery->shop->name }}</td>
                                        <td>{{ $recovery->sale->id }}</td>
                                        <td>{{ number_format($recovery->amount, 2) }} €</td>
                                        <td>{{ number_format($recovery->remaining_amount, 2) }} €</td>
                                        <td>
                                            {{ $recovery->due_date->format('d/m/Y') }}
                                            @if($recovery->isOverdue())
                                                <span class="badge bg-danger">
                                                    {{ $recovery->days_overdue }} jours de retard
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $recovery->status === 'paid' ? 'success' : ($recovery->status === 'partial' ? 'warning' : 'danger') }}">
                                                {{ ucfirst($recovery->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('recoveries.show', $recovery) }}" class="btn btn-info btn-sm">Voir</a>
                                            <a href="{{ route('recoveries.edit', $recovery) }}" class="btn btn-warning btn-sm">Modifier</a>
                                            <form action="{{ route('recoveries.destroy', $recovery) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce recouvrement ?')">
                                                    Supprimer
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        {{ $recoveries->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
