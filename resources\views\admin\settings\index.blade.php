@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Paramètres du Site</h2>
                <button type="button" 
                        onclick="document.getElementById('clear-cache-form').submit();"
                        class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Vider le cache
                </button>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        @method('PUT')

        <!-- Bouton Enregistrer en haut -->
        <div class="flex justify-end space-x-3 mb-6">
            <button type="submit" class="px-6 py-3 bg-green-600 text-white text-lg font-bold rounded-lg shadow-lg hover:bg-green-700 transition-colors" style="z-index:1000; position:relative;">
                <i class="fas fa-save mr-2"></i>Enregistrer
            </button>
        </div>

        <!-- Informations générales -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informations générales</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nom du site -->
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700">Nom du site</label>
                        <input type="text" name="site_name" id="site_name" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               value="{{ old('site_name', $settings['site_name']) }}" required>
                        @error('site_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email de contact -->
                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-700">Email de contact</label>
                        <input type="email" name="contact_email" id="contact_email" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               value="{{ old('contact_email', $settings['contact_email']) }}" required>
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Téléphone -->
                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                        <input type="tel" name="contact_phone" id="contact_phone" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               value="{{ old('contact_phone', $settings['contact_phone']) }}">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Timezone -->
                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700">Fuseau horaire</label>
                        <select name="timezone" id="timezone" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @foreach(timezone_identifiers_list() as $timezone)
                                <option value="{{ $timezone }}" {{ old('timezone', $settings['timezone']) === $timezone ? 'selected' : '' }}>
                                    {{ $timezone }}
                                </option>
                            @endforeach
                        </select>
                        @error('timezone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Format de date -->
                    <div>
                        <label for="date_format" class="block text-sm font-medium text-gray-700">Format de date</label>
                        <select name="date_format" id="date_format" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="d/m/Y H:i" {{ old('date_format', $settings['date_format']) === 'd/m/Y H:i' ? 'selected' : '' }}>
                                {{ now()->format('d/m/Y H:i') }} (JJ/MM/AAAA HH:MM)
                            </option>
                            <option value="Y-m-d H:i" {{ old('date_format', $settings['date_format']) === 'Y-m-d H:i' ? 'selected' : '' }}>
                                {{ now()->format('Y-m-d H:i') }} (AAAA-MM-JJ HH:MM)
                            </option>
                            <option value="d-m-Y H:i" {{ old('date_format', $settings['date_format']) === 'd-m-Y H:i' ? 'selected' : '' }}>
                                {{ now()->format('d-m-Y H:i') }} (JJ-MM-AAAA HH:MM)
                            </option>
                        </select>
                        @error('date_format')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Éléments par page -->
                    <div>
                        <label for="per_page" class="block text-sm font-medium text-gray-700">Éléments par page</label>
                        <input type="number" name="per_page" id="per_page" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               value="{{ old('per_page', $settings['per_page']) }}" min="5" max="100" required>
                        @error('per_page')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6">
                    <label for="site_description" class="block text-sm font-medium text-gray-700">Description du site</label>
                    <textarea name="site_description" id="site_description" rows="3"
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('site_description', $settings['site_description']) }}</textarea>
                    @error('site_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Adresse -->
                <div class="mt-6">
                    <label for="address" class="block text-sm font-medium text-gray-700">Adresse</label>
                    <textarea name="address" id="address" rows="3"
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('address', $settings['address']) }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Apparence -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Apparence</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Logo -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Logo</label>
                        <div class="mt-1 flex items-center space-x-4">
                            @if(isset($settings['logo']))
                                <img src="{{ Storage::url($settings['logo']) }}" alt="Logo" class="h-12 w-auto">
                            @endif
                            <input type="file" name="logo" id="logo" accept="image/*"
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        </div>
                        @error('logo')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Favicon -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Favicon</label>
                        <div class="mt-1 flex items-center space-x-4">
                            @if(isset($settings['favicon']))
                                <img src="{{ Storage::url($settings['favicon']) }}" alt="Favicon" class="h-8 w-8">
                            @endif
                            <input type="file" name="favicon" id="favicon" accept=".ico,image/*"
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        </div>
                        @error('favicon')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Texte du pied de page -->
                <div class="mt-6">
                    <label for="footer_text" class="block text-sm font-medium text-gray-700">Texte du pied de page</label>
                    <textarea name="footer_text" id="footer_text" rows="2"
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('footer_text', $settings['footer_text']) }}</textarea>
                    @error('footer_text')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Réseaux sociaux -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Réseaux sociaux</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach(['facebook', 'twitter', 'instagram', 'linkedin'] as $social)
                        <div>
                            <label for="social_links_{{ $social }}" class="block text-sm font-medium text-gray-700 capitalize">
                                {{ $social }}
                            </label>
                            <input type="url" name="social_links[{{ $social }}]" id="social_links_{{ $social }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   value="{{ old("social_links.$social", $settings['social_links'][$social] ?? '') }}"
                                   placeholder="https://{{ $social }}.com/votre-page">
                            @error("social_links.$social")
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Maintenance -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Mode maintenance</h3>
                        <p class="text-sm text-gray-500">Activer le mode maintenance rendra le site inaccessible aux utilisateurs non-administrateurs.</p>
                    </div>
                    <div class="ml-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="maintenance_mode" value="1" class="sr-only peer"
                                   {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons -->
        <div class="flex justify-end space-x-3 mt-8">
            <button type="reset" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                Réinitialiser
            </button>
            <button type="submit" class="px-6 py-3 bg-blue-600 text-white text-lg font-bold rounded-lg shadow-lg hover:bg-blue-700 transition-colors" style="z-index:1000; position:relative;">
                <i class="fas fa-save mr-2"></i>Enregistrer les modifications
            </button>
        </div>
    </form>

    <!-- Formulaire pour vider le cache -->
    <form id="clear-cache-form" action="{{ route('admin.settings.clear-cache') }}" method="POST" class="hidden">
        @csrf
    </form>
</div>
@endsection
