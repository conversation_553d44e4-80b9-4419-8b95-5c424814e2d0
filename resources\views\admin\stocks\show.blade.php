@extends('layouts.admin')

@section('title', '<PERSON>é<PERSON> de l\'approvisionnement')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-extrabold text-blue-900 flex items-center gap-2">
                <i class="fas fa-truck-loading text-blue-500"></i>
                Détail de l'approvisionnement
            </h1>
            <a href="{{ route('admin.stocks.index') }}" class="bg-gray-200 hover:bg-gray-300 text-blue-700 font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i> Retour
            </a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
                <div class="mb-2 text-sm text-gray-600">Date</div>
                <div class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-calendar-alt text-blue-400 mr-1"></i>
                    {{ $stock->supply_date ? $stock->supply_date->format('d/m/Y') : '-' }}
                </div>
            </div>
            <div>
                <div class="mb-2 text-sm text-gray-600">Fournisseur</div>
                <div class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-bold shadow">
                    {{ $stock->supplier ? $stock->supplier->name : '-' }}
                </div>
            </div>
            <div>
                <div class="mb-2 text-sm text-gray-600">Boutique</div>
                <div class="inline-block px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm font-bold shadow">
                    {{ $stock->shop ? $stock->shop->name : '-' }}
                </div>
            </div>
            <div>
                <div class="mb-2 text-sm text-gray-600">Note</div>
                <div>
                    @if($stock->notes)
                        <span class="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold">
                            <i class="fas fa-sticky-note mr-1"></i> {{ $stock->notes }}
                        </span>
                    @else
                        <span class="text-gray-400 italic">-</span>
                    @endif
                </div>
            </div>
        </div>
        <div class="mt-8">
            <h2 class="text-xl font-bold text-blue-800 mb-4 flex items-center gap-2">
                <i class="fas fa-boxes text-blue-500"></i> Détails des produits
            </h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-blue-200">
                    <thead class="bg-blue-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Produit</th>
                            <th class="px-6 py-3 text-right text-xs font-bold text-blue-700 uppercase tracking-wider">Quantité</th>
                            <th class="px-6 py-3 text-right text-xs font-bold text-blue-700 uppercase tracking-wider">Unité</th>
                            <th class="px-6 py-3 text-right text-xs font-bold text-blue-700 uppercase tracking-wider">Prix unitaire</th>
                            <th class="px-6 py-3 text-right text-xs font-bold text-blue-700 uppercase tracking-wider">Total</th>
                            <th class="px-6 py-3 text-right text-xs font-bold text-blue-700 uppercase tracking-wider">Stock min.</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-blue-100">
                        @php
                            $totalGeneral = 0;
                        @endphp
                        @foreach($stock->details as $detail)
                        @php
                            $total = $detail->total_price ?? ($detail->quantity * $detail->unit_price);
                            $totalGeneral += $total;
                        @endphp
                        <tr>
                            <td class="px-6 py-4 text-gray-900 font-semibold">{{ $detail->product->name }} ({{ $detail->product->sku }})</td>
                            <td class="px-6 py-4 text-right">{{ $detail->quantity }}</td>
                            <td class="px-6 py-4 text-right">{{ $detail->unit }}</td>
                            <td class="px-6 py-4 text-right">{{ number_format($detail->unit_price, 0, ',', ' ') }} FCFA</td>
                            <td class="px-6 py-4 text-right font-bold text-blue-800">
                                {{ number_format($total, 0, ',', ' ') }} FCFA
                            </td>
                            <td class="px-6 py-4 text-right">{{ $detail->product->min_stock ?? '-' }}</td>
                        </tr>
                        @endforeach
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-right font-bold text-lg text-blue-900 bg-blue-50">Total général</td>
                            <td class="px-6 py-4 text-right font-bold text-lg text-blue-900 bg-blue-50">
                                {{ number_format($totalGeneral, 0, ',', ' ') }} FCFA
                            </td>
                            <td class="bg-blue-50"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
