<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AvatarController extends Controller
{
    /**
     * Mettre à jour l'avatar de l'utilisateur.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'avatar' => ['required', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
        ], [
            'avatar.required' => 'Veuillez sélectionner une image.',
            'avatar.image' => 'Le fichier doit être une image.',
            'avatar.mimes' => 'L\'image doit être au format JPEG, PNG ou JPG.',
            'avatar.max' => 'L\'image ne doit pas dépasser 2Mo.',
        ]);

        $user = auth()->user();

        // Supprimer l'ancien avatar s'il existe
        if ($user->avatar_path) {
            Storage::disk('public')->delete($user->avatar_path);
        }

        // Enregistrer l'image telle quelle (sans redimensionnement)
        $path = 'avatars/' . uniqid() . '.' . $request->file('avatar')->extension();
        
        // Stocker le fichier dans le disque 'public'
        $request->file('avatar')->storeAs('public', $path);
        
        // Mettre à jour le chemin de l'avatar dans la base de données
        // Nous stockons le chemin sans le préfixe 'public/'
        $user->update([
            'avatar_path' => $path
        ]);

        return redirect()
            ->route('profile.show')
            ->with('success', 'Avatar mis à jour avec succès.');
    }

    /**
     * Supprimer l'avatar de l'utilisateur.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy()
    {
        $user = auth()->user();

        // Supprimer l'avatar s'il existe
        if ($user->avatar_path) {
            Storage::disk('public')->delete($user->avatar_path);
        }

        // Mettre à jour la base de données
        $user->update([
            'avatar_path' => null
        ]);

        return redirect()
            ->route('profile.show')
            ->with('success', 'Avatar supprimé avec succès.');
    }
}
