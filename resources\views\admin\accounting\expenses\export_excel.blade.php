<table>
    <thead>
        <tr>
            <th>#</th>
            <th>Description</th>
            <th>Montant</th>
            <th><PERSON><PERSON>gorie</th>
            <th>Date</th>
        </tr>
    </thead>
    <tbody>
        @foreach($expenses as $expense)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $expense->description }}</td>
                <td>{{ number_format($expense->amount, 2, ',', ' ') }}</td>
                <td>{{ $expense->category ? $expense->category->name : '-' }}</td>
                <td>{{ $expense->date ? \Carbon\Carbon::parse($expense->date)->format('d/m/Y') : '-' }}</td>
            </tr>
        @endforeach
    </tbody>
</table>
