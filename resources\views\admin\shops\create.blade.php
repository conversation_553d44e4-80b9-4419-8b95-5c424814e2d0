@extends('layouts.admin')

@section('content')
<div class="max-w-3xl mx-auto py-8">
    <div class="bg-white shadow rounded-xl">
        <div class="flex justify-between items-center px-6 py-4 border-b">
            <h2 class="text-2xl font-bold text-blue-700">Créer une nouvelle boutique</h2>
            <a href="{{ route('admin.shops.index') }}" class="text-blue-600 hover:underline">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
        <form action="{{ route('admin.shops.store') }}" method="POST" class="p-6 space-y-6">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium">Nom *</label>
                    <input type="text" name="name" id="name" placeholder="Ex: Boutique Centrale"
                        class="mt-1 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        value="{{ old('name') }}" required>
                    @error('name') <p class="text-red-600 text-xs">{{ $message }}</p> @enderror
                </div>
                <div>
                    <label for="email" class="block text-sm font-medium">Email *</label>
                    <input type="email" name="email" id="email" placeholder="<EMAIL>"
                        class="mt-1 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        value="{{ old('email') }}" required>
                    @error('email') <p class="text-red-600 text-xs">{{ $message }}</p> @enderror
                </div>
                <div>
                    <label for="phone" class="block text-sm font-medium">Téléphone *</label>
                    <input type="tel" name="phone" id="phone" placeholder="0601020304"
                        class="mt-1 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        value="{{ old('phone') }}" required>
                    @error('phone') <p class="text-red-600 text-xs">{{ $message }}</p> @enderror
                </div>
                <div>
                    <label for="is_active" class="block text-sm font-medium">Statut</label>
                    <select name="is_active" id="is_active"
                        class="mt-1 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                        <option value="1" {{ old('is_active') == '1' ? 'selected' : '' }}>Actif</option>
                        <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>Inactif</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium">Adresse</label>
                    <textarea name="address" id="address" rows="2"
                        class="mt-1 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Adresse complète">{{ old('address') }}</textarea>
                    @error('address') <p class="text-red-600 text-xs">{{ $message }}</p> @enderror
                </div>
            </div>
            <div class="pt-4 flex justify-end">
                <button type="submit"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg shadow">
                    <i class="fas fa-save mr-2"></i>Créer la boutique
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
