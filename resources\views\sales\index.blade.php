@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Gestion des Ventes</h2>
                    <a href="{{ route('sales.create') }}" class="btn btn-primary">Nouvelle Vente</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Boutique</th>
                                    <th>Vendeur</th>
                                    <th>Montant</th>
                                    <th>Méthode de paiement</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sales as $sale)
                                    <tr>
                                        <td>{{ $sale->id }}</td>
                                        <td>{{ $sale->shop->name }}</td>
                                        <td>{{ $sale->user->name }}</td>
                                        <td>{{ number_format($sale->amount, 2) }} €</td>
                                        <td>{{ ucfirst($sale->payment_method) }}</td>
                                        <td>
                                            <span class="badge bg-{{ $sale->status === 'completed' ? 'success' : ($sale->status === 'pending' ? 'warning' : 'danger') }}">
                                                {{ ucfirst($sale->status) }}
                                            </span>
                                        </td>
                                        <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            <a href="{{ route('sales.show', $sale) }}" class="btn btn-info btn-sm">Voir</a>
                                            <a href="{{ route('sales.edit', $sale) }}" class="btn btn-warning btn-sm">Modifier</a>
                                            <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette vente ?')">Supprimer</button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        {{ $sales->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
