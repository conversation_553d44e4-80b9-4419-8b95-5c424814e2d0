<?php

namespace App\Http\Controllers\Storekeeper;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index() { return view('storekeeper.products.index'); }
    public function create() { return view('storekeeper.products.create'); }
    public function store(Request $request) { return redirect()->route('storekeeper.products.index'); }
    public function show($id) { return view('storekeeper.products.show', compact('id')); }
    public function edit($id) { return view('storekeeper.products.edit', compact('id')); }
    public function update(Request $request, $id) { return redirect()->route('storekeeper.products.index'); }
    public function destroy($id) { return redirect()->route('storekeeper.products.index'); }
}
