<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class SaleControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer les rôles nécessaires
        Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'web']);
        
        // Désactive le middleware CSRF
        $this->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class);
        
        // Active le mode sans exception handling
        $this->withoutExceptionHandling();
    }

    /** @test */
    public function it_creates_sale_with_valid_data()
    {
        $shop = \App\Models\Shop::factory()->create();
        $customer = \App\Models\Customer::factory()->create();
        $product = \App\Models\Product::factory()->create(['shop_id' => $shop->id]);
        $user = \App\Models\User::factory()->create();
        
        // Attribuer le bon rôle à l'utilisateur (admin ou manager)
        $user->assignRole('admin');
        
        $response = $this->actingAs($user)
            ->post('/admin/sales', [
                'customer_id' => $customer->id,
                'shop_id' => $shop->id,
                'payment_method' => 'cash',
                'payment_status' => 'paid',
                'amount_paid' => 1000,
                'products' => json_encode([
                    ['id' => $product->id, 'quantity' => 2, 'price' => 100, 'unit' => 'pièce']
                ])
            ]);

        if ($response->status() !== 200) {
            dd($response->getContent());
        }

        $response->assertStatus(200)
            ->assertJson(['success' => true]);
    }
}
