<?php

namespace App\Notifications;

use App\Models\Delivery;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DeliveryNotification extends Notification
{
    use Queueable;

    public $delivery;

    public function __construct(Delivery $delivery)
    {
        $this->delivery = $delivery;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Nouvelle livraison')
            ->line('Une nouvelle livraison a été créée.')
            ->line("Vente: #{$this->delivery->sale->id}")
            ->line("Client: {$this->delivery->sale->user->name}")
            ->line("Boutique: {$this->delivery->sale->shop->name}")
            ->line("Date de livraison: {$this->delivery->delivery_date->format('d/m/Y H:i')}")
            ->line("Adresse: {$this->delivery->delivery_address}")
            ->line("Statut: {$this->delivery->status}")
            ->line('Merci de votre attention !');
    }
}
