<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Shop;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ShopManagementTest extends TestCase
{
    use RefreshDatabase;

    private $admin;
    private $manager;

    protected function setUp(): void
    {
        parent::setUp();

        // Désactiver Vite pour les tests
        $this->withoutVite();

        // Créer les permissions nécessaires
        Permission::create(['name' => 'view shops']);
        Permission::create(['name' => 'create shops']);
        Permission::create(['name' => 'edit shops']);
        Permission::create(['name' => 'delete shops']);

        // Créer les rôles avec leurs permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(['view shops', 'create shops', 'edit shops', 'delete shops']);

        $managerRole = Role::create(['name' => 'manager']);
        $managerRole->givePermissionTo(['view shops']);

        // Créer les utilisateurs de test
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        $this->admin->assignRole('admin');

        $this->manager = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        $this->manager->assignRole('manager');
    }

    public function test_admin_can_view_shops_list()
    {
        $shop = Shop::create([
            'name' => 'Test Shop',
            'code' => 'TEST-001',
            'address' => 'Test Address',
            'phone' => '+123456789'
        ]);

        $response = $this->actingAs($this->admin)->get('/shops');

        $response->assertStatus(200)
                ->assertViewIs('shops.index')
                ->assertSee($shop->name)
                ->assertSee($shop->code);
    }

    public function test_admin_can_create_main_shop()
    {
        $shopData = [
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'email' => '<EMAIL>',
            'is_main_shop' => true
        ];

        $response = $this->actingAs($this->admin)
                        ->post('/shops', $shopData);

        $response->assertRedirect()
                ->assertSessionHas('success');

        $this->assertDatabaseHas('shops', $shopData);
    }

    public function test_admin_can_create_annex_shop()
    {
        $mainShop = Shop::create([
            'name' => 'Boutique Principale',
            'code' => 'MAIN-001',
            'address' => '123 Rue Principale',
            'phone' => '+123456789',
            'is_main_shop' => true
        ]);

        $annexData = [
            'name' => 'Boutique Annexe',
            'code' => 'ANX-001',
            'address' => '456 Rue Secondaire',
            'phone' => '+987654321',
            'is_main_shop' => false,
            'parent_shop_id' => $mainShop->id
        ];

        $response = $this->actingAs($this->admin)
                        ->post('/shops', $annexData);

        $response->assertRedirect()
                ->assertSessionHas('success');

        $this->assertDatabaseHas('shops', $annexData);
    }

    public function test_admin_can_edit_shop()
    {
        $mainShop = Shop::create([
            'name' => 'Main Shop',
            'code' => 'MAIN-001',
            'address' => 'Main Address',
            'phone' => '+111111111',
            'is_main_shop' => true
        ]);

        $shop = Shop::create([
            'name' => 'Old Name',
            'code' => 'OLD-001',
            'address' => 'Old Address',
            'phone' => '+123456789',
            'is_main_shop' => false,
            'parent_shop_id' => $mainShop->id
        ]);

        $updatedData = [
            'name' => 'New Name',
            'code' => 'NEW-001',
            'address' => 'New Address',
            'phone' => '+987654321',
            'is_main_shop' => false,
            'parent_shop_id' => $mainShop->id
        ];

        $response = $this->actingAs($this->admin)
                        ->put("/shops/{$shop->id}", $updatedData);

        $response->assertRedirect()
                ->assertSessionHas('success');

        $this->assertDatabaseHas('shops', $updatedData);
    }

    public function test_admin_cannot_delete_shop_with_users()
    {
        $shop = Shop::create([
            'name' => 'Test Shop',
            'code' => 'TEST-001',
            'address' => 'Test Address',
            'phone' => '+123456789'
        ]);

        User::factory()->create([
            'shop_id' => $shop->id
        ]);

        $response = $this->actingAs($this->admin)
                        ->delete("/shops/{$shop->id}");

        $response->assertRedirect()
                ->assertSessionHas('error');

        $this->assertDatabaseHas('shops', ['id' => $shop->id]);
    }

    public function test_manager_cannot_create_shop()
    {
        $shopData = [
            'name' => 'New Shop',
            'code' => 'NEW-001',
            'address' => 'New Address',
            'phone' => '+123456789'
        ];

        $response = $this->actingAs($this->manager)
                        ->post('/shops', $shopData);

        $response->assertStatus(403);

        $this->assertDatabaseMissing('shops', $shopData);
    }

    public function test_main_shop_cannot_be_child_of_another_shop()
    {
        $mainShop1 = Shop::create([
            'name' => 'Main Shop 1',
            'code' => 'MAIN-001',
            'address' => 'Address 1',
            'phone' => '+111111111',
            'is_main_shop' => true
        ]);

        $mainShop2Data = [
            'name' => 'Main Shop 2',
            'code' => 'MAIN-002',
            'address' => 'Address 2',
            'phone' => '+222222222',
            'is_main_shop' => true,
            'parent_shop_id' => $mainShop1->id
        ];

        $response = $this->actingAs($this->admin)
                        ->post('/shops', $mainShop2Data);

        $response->assertSessionHasErrors('parent_shop_id');
    }
}
