<?php

namespace App\Notifications;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;

class LowStockNotification extends Notification
{
    use Queueable;

    protected $stock;

    /**
     * Créer une nouvelle instance de notification
     */
    public function __construct(Stock $stock)
    {
        $this->stock = $stock;
    }

    /**
     * Obtenir les canaux de livraison de la notification
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Obtenir la représentation mail de la notification
     */
    public function toMail($notifiable)
    {
        $url = route('stocks.show', $this->stock->id);

        return (new MailMessage)
            ->subject('Alerte Stock Bas - ' . $this->stock->product->name)
            ->line('Le stock du produit ' . $this->stock->product->name . ' est bas dans la boutique ' . $this->stock->shop->name)
            ->line('Quantité actuelle : ' . $this->stock->quantity)
            ->line('Seuil minimum : ' . $this->stock->min_stock)
            ->action('Voir le stock', $url)
            ->line('Veuillez prendre les mesures nécessaires pour réapprovisionner ce produit.');
    }

    /**
     * Obtenir la représentation array de la notification
     */
    public function toArray($notifiable)
    {
        return [
            'stock_id' => $this->stock->id,
            'product_name' => $this->stock->product->name,
            'shop_name' => $this->stock->shop->name,
            'current_quantity' => $this->stock->quantity,
            'min_stock' => $this->stock->min_stock,
            'type' => 'low_stock'
        ];
    }
}
