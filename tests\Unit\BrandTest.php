<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Brand;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BrandTest extends TestCase
{
    use RefreshDatabase;

    private $brand;

    protected function setUp(): void
    {
        parent::setUp();

        $this->brand = Brand::factory()->create([
            'name' => 'Test Brand',
            'description' => 'Test brand description',
            'logo_path' => 'brands/test-brand.png',
            'website' => 'https://testbrand.com',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_has_many_products()
    {
        $product = Product::factory()->create([
            'brand_id' => $this->brand->id
        ]);

        $this->assertTrue($this->brand->products->contains($product));
        $this->assertInstanceOf(Product::class, $this->brand->products->first());
    }

    /** @test */
    public function it_generates_slug_from_name()
    {
        $brand = Brand::factory()->create([
            'name' => 'New Test Brand'
        ]);

        $this->assertEquals('new-test-brand', $brand->slug);
    }

    /** @test */
    public function it_has_unique_slug()
    {
        $brand1 = Brand::factory()->create(['name' => 'Same Name']);
        $brand2 = Brand::factory()->create(['name' => 'Same Name']);

        $this->assertNotEquals($brand1->slug, $brand2->slug);
    }

    /** @test */
    public function it_can_determine_if_it_has_products()
    {
        $this->assertFalse($this->brand->hasProducts());

        Product::factory()->create([
            'brand_id' => $this->brand->id
        ]);

        $this->brand->refresh();
        $this->assertTrue($this->brand->hasProducts());
    }

    /** @test */
    public function it_can_be_searched_by_name()
    {
        $searchResults = Brand::search('Test')->get();
        $this->assertTrue($searchResults->contains($this->brand));
    }

    /** @test */
    public function it_can_get_active_brands()
    {
        $inactiveBrand = Brand::factory()->create([
            'status' => 'inactive'
        ]);

        $activeBrands = Brand::active()->get();

        $this->assertTrue($activeBrands->contains($this->brand));
        $this->assertFalse($activeBrands->contains($inactiveBrand));
    }

    /** @test */
    public function it_can_get_brands_with_products_count()
    {
        Product::factory()->count(3)->create([
            'brand_id' => $this->brand->id
        ]);

        $brandWithCount = Brand::withProductsCount()->find($this->brand->id);
        $this->assertEquals(3, $brandWithCount->products_count);
    }

    /** @test */
    public function it_validates_website_format()
    {
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        Brand::factory()->create([
            'website' => 'invalid-url'
        ]);
    }

    /** @test */
    public function it_can_get_logo_url()
    {
        $this->assertEquals(
            asset('storage/' . $this->brand->logo_path),
            $this->brand->logo_url
        );
    }
}
