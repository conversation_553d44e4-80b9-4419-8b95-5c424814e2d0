<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Supprimer les colonnes de stocks si elles existent
        Schema::table('stocks', function (Blueprint $table) {
            if (Schema::hasColumn('stocks', 'quantity')) {
                $table->dropColumn('quantity');
            }
            if (Schema::hasColumn('stocks', 'min_stock')) {
                $table->dropColumn('min_stock');
            }
        });

        // Ajouter min_stock à stock_details
        Schema::table('stock_details', function (Blueprint $table) {
            $table->integer('min_stock')->default(10);
        });
    }

    public function down()
    {
        // Ajouter les colonnes à stocks
        Schema::table('stocks', function (Blueprint $table) {
            $table->integer('quantity')->default(0);
            $table->integer('min_stock')->default(10);
        });

        // Supprimer min_stock de stock_details
        Schema::table('stock_details', function (Blueprint $table) {
            $table->dropColumn('min_stock');
        });
    }
};
