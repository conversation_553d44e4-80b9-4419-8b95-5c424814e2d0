<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Database\Seeders\RolesAndPermissionsSeeder;
use Database\Seeders\CategorySeeder;
use Database\Seeders\ShopSeeder;
use Database\Seeders\AdminSeeder;
use Database\Seeders\StoreSeeder;
use Database\Seeders\AccountSeeder;
use Database\Seeders\JournalEntrySeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolesAndPermissionsSeeder::class,
            CategorySeeder::class,
            ShopSeeder::class,
            AdminSeeder::class,
            //StoreSeeder::class,
        ]);

        // Ajout du plan comptable de base (comptes et sous-comptes)
        $this->call(AccountSeeder::class);
        $this->call(JournalEntrySeeder::class);
    }
}
