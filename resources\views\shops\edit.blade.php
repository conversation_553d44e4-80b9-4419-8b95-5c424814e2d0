@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Modifier la Boutique</h5>
                </div>

                <div class="card-body">
                    <form action="{{ route('shops.update', $shop) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="name" class="form-label">Nom de la boutique</label>
                            <input type="text" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $shop->name) }}" 
                                   required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="code" class="form-label">Code de la boutique</label>
                            <input type="text" 
                                   class="form-control @error('code') is-invalid @enderror" 
                                   id="code" 
                                   name="code" 
                                   value="{{ old('code', $shop->code) }}" 
                                   required>
                            @error('code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Adresse</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" 
                                      name="address" 
                                      rows="3" 
                                      required>{{ old('address', $shop->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Téléphone</label>
                            <input type="text" 
                                   class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ old('phone', $shop->phone) }}" 
                                   required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email', $shop->email) }}">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_main_shop" 
                                       name="is_main_shop" 
                                       value="1" 
                                       {{ old('is_main_shop', $shop->is_main_shop) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_main_shop">
                                    Boutique principale
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="parent_shop_section">
                            <label for="parent_shop_id" class="form-label">Boutique principale de rattachement</label>
                            <select class="form-select @error('parent_shop_id') is-invalid @enderror" 
                                    id="parent_shop_id" 
                                    name="parent_shop_id">
                                <option value="">Sélectionner une boutique principale</option>
                                @foreach($mainShops as $mainShop)
                                    <option value="{{ $mainShop->id }}" 
                                            {{ old('parent_shop_id', $shop->parent_shop_id) == $mainShop->id ? 'selected' : '' }}>
                                        {{ $mainShop->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('parent_shop_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', $shop->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Boutique active
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('shops.show', $shop) }}" class="btn btn-secondary">Annuler</a>
                            <button type="submit" class="btn btn-primary">Mettre à jour</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const isMainShopCheckbox = document.getElementById('is_main_shop');
        const parentShopSection = document.getElementById('parent_shop_section');
        const parentShopSelect = document.getElementById('parent_shop_id');

        function toggleParentShopSection() {
            if (isMainShopCheckbox.checked) {
                parentShopSection.style.display = 'none';
                parentShopSelect.value = '';
            } else {
                parentShopSection.style.display = 'block';
            }
        }

        isMainShopCheckbox.addEventListener('change', toggleParentShopSection);
        toggleParentShopSection();
    });
</script>
@endpush
@endsection
