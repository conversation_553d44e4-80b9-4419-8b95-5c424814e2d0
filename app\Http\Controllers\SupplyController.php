<?php

namespace App\Http\Controllers;

use App\Models\Supply;
use App\Models\Shop;
use App\Models\Stock;
use App\Models\SupplyItem;
use Illuminate\Http\Request;
use App\Http\Requests\SupplyRequest;
use Illuminate\Support\Facades\DB;
use App\Notifications\SupplyValidationNotification;
use App\Notifications\SupplyReceivedNotification;

class SupplyController extends Controller
{
    /**
     * Afficher la liste des approvisionnements
     */
    public function index(Request $request)
    {
        $supplies = Supply::with(['supplier', 'shop'])
            ->when($request->shop_id, function ($query, $shop_id) {
                return $query->where('shop_id', $shop_id);
            })
            ->when($request->status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('supplies.index', compact('supplies'));
    }

    /**
     * Afficher le formulaire de création d'un approvisionnement
     */
    public function create()
    {
        $shops = Shop::all();
        return view('supplies.create', compact('shops'));
    }

    /**
     * Enregistrer un nouvel approvisionnement
     */
    public function store(SupplyRequest $request)
    {
        try {
            DB::beginTransaction();

            $supply = Supply::create([
                'supplier_id' => $request->supplier_id,
                'shop_id' => $request->shop_id,
                'reference_number' => $this->generateReferenceNumber(),
                'order_date' => now(),
                'expected_delivery_date' => $request->expected_delivery_date,
                'status' => Supply::STATUS_PENDING,
                'notes' => $request->notes
            ]);

            foreach ($request->items as $item) {
                $supply->items()->create([
                    'product_id' => $item['product_id'],
                    'ordered_quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['quantity'] * $item['unit_price']
                ]);
            }

            DB::commit();

            return redirect()
                ->route('supplies.show', $supply)
                ->with('success', 'Approvisionnement créé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la création de l\'approvisionnement');
        }
    }

    /**
     * Afficher les détails d'un approvisionnement
     */
    public function show(Supply $supply)
    {
        $supply->load(['supplier', 'shop', 'items.product']);
        return view('supplies.show', compact('supply'));
    }

    /**
     * Valider un approvisionnement
     */
    public function validate(Supply $supply)
    {
        if (!$supply->canBeValidated()) {
            return back()->with('error', 'Cet approvisionnement ne peut pas être validé');
        }

        try {
            DB::beginTransaction();

            $supply->update([
                'status' => Supply::STATUS_VALIDATED,
                'validated_by' => auth()->id()
            ]);

            // Envoyer notification
            $supply->shop->manager->notify(new SupplyValidationNotification($supply));

            DB::commit();

            return back()->with('success', 'Approvisionnement validé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Erreur lors de la validation de l\'approvisionnement');
        }
    }

    /**
     * Réceptionner un approvisionnement
     */
    public function receive(Request $request, Supply $supply)
    {
        if (!$supply->canBeReceived()) {
            return back()->with('error', 'Cet approvisionnement ne peut pas être réceptionné');
        }

        try {
            DB::beginTransaction();

            $allReceived = true;
            foreach ($request->items as $itemData) {
                $item = $supply->items()->find($itemData['id']);
                if (!$item) continue;

                $item->update([
                    'received_quantity' => $itemData['received_quantity'],
                    'notes' => $itemData['notes'] ?? null
                ]);

                // Mettre à jour le stock
                $stock = Stock::firstOrCreate([
                    'product_id' => $item->product_id,
                    'shop_id' => $supply->shop_id
                ]);

                $stock->increment('quantity', $itemData['received_quantity']);

                if ($item->received_quantity < $item->ordered_quantity) {
                    $allReceived = false;
                }
            }

            $supply->update([
                'status' => $allReceived ? Supply::STATUS_RECEIVED : Supply::STATUS_PARTIALLY_RECEIVED,
                'received_date' => now(),
                'received_by' => auth()->id(),
                'notes' => $request->notes
            ]);

            // Envoyer notification
            $supply->validator->notify(new SupplyReceivedNotification($supply));

            DB::commit();

            return back()->with('success', 'Approvisionnement réceptionné avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Erreur lors de la réception de l\'approvisionnement');
        }
    }

    /**
     * Générer un numéro de référence unique
     */
    private function generateReferenceNumber()
    {
        $prefix = 'APP';
        $year = date('Y');
        $month = date('m');
        $lastSupply = Supply::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->latest()
            ->first();

        $sequence = $lastSupply ? intval(substr($lastSupply->reference_number, -4)) + 1 : 1;

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $sequence);
    }
}
