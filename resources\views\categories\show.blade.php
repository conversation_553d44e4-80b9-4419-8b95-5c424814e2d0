@extends('layouts.app')

@section('title', 'Détails de la catégorie')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ $category->name }}</h1>
        <div>
            @can('update', $category)
            <a href="{{ route('categories.edit', $category) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifier
            </a>
            @endcan
            <a href="{{ route('categories.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <!-- Informations de la catégorie -->
    <div class="row">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Nombre de produits
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $category->products->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Produits en stock faible
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $category->products->filter(function($product) {
                                    return $product->hasLowStock();
                                })->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Valeur totale du stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($category->products->sum(function($product) {
                                    return $product->total_stock * $product->unit_price;
                                }), 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des produits -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Produits de la catégorie</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>SKU</th>
                            <th>Nom</th>
                            <th>Prix de vente</th>
                            <th>Stock total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($category->products as $product)
                        <tr>
                            <td>{{ $product->sku }}</td>
                            <td>{{ $product->name }}</td>
                            <td>{{ number_format($product->selling_price, 0, ',', ' ') }} FCFA</td>
                            <td>
                                @php
                                    $totalStock = $product->total_stock;
                                    $stockStatus = $product->hasLowStock() ? 'text-warning' : 
                                                ($totalStock > 0 ? 'text-success' : 'text-danger');
                                @endphp
                                <span class="{{ $stockStatus }}">
                                    {{ $totalStock }} unités
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-{{ $product->status === 'active' ? 'success' : 'danger' }}">
                                    {{ $product->status === 'active' ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ route('products.show', $product) }}" 
                                   class="btn btn-sm btn-info"
                                   title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                @can('update', $product)
                                <a href="{{ route('products.edit', $product) }}" 
                                   class="btn btn-sm btn-primary"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcan
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">Aucun produit dans cette catégorie</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
