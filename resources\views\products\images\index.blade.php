@extends('layouts.app')

@section('title', 'Gestion des images')

@section('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css">
<style>
.image-container {
    position: relative;
    margin-bottom: 20px;
}
.image-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;
}
.image-container:hover .image-actions {
    display: block;
}
.primary-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
}
.sortable-ghost {
    opacity: 0.5;
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Images du produit : {{ $product->name }}</h1>
        <a href="{{ route('products.show', $product) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au produit
        </a>
    </div>

    <!-- Zone de drop des images -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Ajouter des images</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('products.images.store', $product) }}" 
                  class="dropzone"
                  id="imageUpload">
                @csrf
            </form>
            <small class="form-text text-muted mt-2">
                Formats acceptés : JPG, PNG. Taille maximale : 2MB par image. Maximum 5 images à la fois.
            </small>
        </div>
    </div>

    <!-- Liste des images -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Images du produit</h6>
        </div>
        <div class="card-body">
            @if($images->isEmpty())
                <div class="text-center text-muted">
                    <i class="fas fa-images fa-3x mb-3"></i>
                    <p>Aucune image n'a été ajoutée à ce produit.</p>
                </div>
            @else
                <div class="row sortable-images" id="imagesList">
                    @foreach($images as $image)
                    <div class="col-md-4 col-lg-3 image-container" data-id="{{ $image->id }}">
                        <div class="card">
                            @if($image->is_primary)
                                <div class="primary-badge">
                                    <i class="fas fa-star"></i> Image principale
                                </div>
                            @endif
                            
                            <img src="{{ Storage::url($image->medium_path) }}" 
                                 class="card-img-top"
                                 alt="{{ $product->name }}">
                            
                            <div class="image-actions">
                                @if(!$image->is_primary)
                                    <form action="{{ route('products.images.set-primary', [$product, $image]) }}" 
                                          method="POST"
                                          class="d-inline">
                                        @csrf
                                        <button type="submit" 
                                                class="btn btn-sm btn-primary"
                                                title="Définir comme image principale">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    </form>
                                @endif

                                <form action="{{ route('products.images.destroy', [$product, $image]) }}" 
                                      method="POST"
                                      class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="btn btn-sm btn-danger"
                                            title="Supprimer"
                                            onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette image ?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
<script>
Dropzone.options.imageUpload = {
    paramName: "images",
    maxFilesize: 2, // MB
    acceptedFiles: "image/jpeg,image/png,image/jpg",
    maxFiles: 5,
    dictDefaultMessage: "Déposez vos images ici ou cliquez pour sélectionner",
    dictFallbackMessage: "Votre navigateur ne supporte pas le drag'n'drop.",
    dictFileTooBig: "Le fichier est trop volumineux ({{filesize}}MB). Taille maximale: {{maxFilesize}}MB.",
    dictInvalidFileType: "Ce type de fichier n'est pas autorisé.",
    dictMaxFilesExceeded: "Vous ne pouvez pas uploader plus de {{maxFiles}} fichiers.",
    init: function() {
        this.on("complete", function(file) {
            if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0) {
                location.reload();
            }
        });
    }
};

// Tri des images
new Sortable(document.getElementById('imagesList'), {
    animation: 150,
    ghostClass: 'sortable-ghost',
    onEnd: function() {
        let images = [];
        document.querySelectorAll('.image-container').forEach(function(el) {
            images.push(el.dataset.id);
        });

        fetch('{{ route('products.images.reorder', $product) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ images: images })
        });
    }
});
</script>
@endpush
