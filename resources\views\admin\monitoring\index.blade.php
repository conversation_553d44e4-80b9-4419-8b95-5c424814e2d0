@extends('layouts.admin')

@section('content')
<div class="space-y-6">
    <!-- En-tête -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-800">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-semibold text-white">Monitoring en Temps Réel</h2>
                <div class="flex space-x-2">
                    <button onclick="toggleAutoRefresh()" id="refresh-toggle"
                            class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Auto-refresh activé
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistiques globales -->
        <div class="p-6 grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Utilisateurs actifs -->
            <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-lg p-6 text-white">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm opacity-75">Utilisateurs actifs</p>
                        <h3 class="text-3xl font-bold mt-2" id="active-users-count">0</h3>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="opacity-75">Dernière heure:</span>
                    <span class="font-semibold ml-1" id="users-last-hour">0</span>
                </div>
            </div>

            <!-- Ventes en cours -->
            <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg p-6 text-white">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm opacity-75">Ventes en cours</p>
                        <h3 class="text-3xl font-bold mt-2" id="active-sales-count">0</h3>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-shopping-cart text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="opacity-75">Total aujourd'hui:</span>
                    <span class="font-semibold ml-1" id="sales-today">0</span>
                </div>
            </div>

            <!-- Stock critique -->
            <div class="bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg p-6 text-white">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm opacity-75">Produits en stock critique</p>
                        <h3 class="text-3xl font-bold mt-2" id="critical-stock-count">0</h3>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-exclamation-triangle text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="opacity-75">À commander:</span>
                    <span class="font-semibold ml-1" id="to-order-count">0</span>
                </div>
            </div>

            <!-- Alertes système -->
            <div class="bg-gradient-to-br from-red-400 to-red-600 rounded-lg p-6 text-white">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm opacity-75">Alertes actives</p>
                        <h3 class="text-3xl font-bold mt-2" id="active-alerts-count">0</h3>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-bell text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="opacity-75">Non traitées:</span>
                    <span class="font-semibold ml-1" id="unhandled-alerts-count">0</span>
                </div>
            </div>
        </div>

        <!-- Graphiques et tableaux de bord -->
        <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Activité par boutique -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Activité par Boutique</h3>
                <div class="h-64" id="shops-activity-chart"></div>
            </div>

            <!-- Performance des ventes -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance des Ventes</h3>
                <div class="h-64" id="sales-performance-chart"></div>
            </div>
        </div>

        <!-- Activités en temps réel -->
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Activités en Temps Réel</h3>
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Heure
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Utilisateur
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Action
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Boutique
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Statut
                                </th>
                            </tr>
                        </thead>
                        <tbody id="realtime-activities" class="bg-white divide-y divide-gray-200">
                            <!-- Les activités seront injectées ici via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<!-- ApexCharts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    let autoRefreshInterval;
    let isAutoRefreshEnabled = true;

    // Configuration des graphiques
    function initializeCharts() {
        // Graphique d'activité par boutique
        const shopsActivityOptions = {
            chart: {
                type: 'bar',
                height: 250,
                toolbar: {
                    show: false
                }
            },
            series: [{
                name: 'Ventes',
                data: []
            }],
            xaxis: {
                categories: []
            },
            colors: ['#3B82F6']
        };

        const shopsActivityChart = new ApexCharts(
            document.querySelector("#shops-activity-chart"), 
            shopsActivityOptions
        );
        shopsActivityChart.render();

        // Graphique de performance des ventes
        const salesPerformanceOptions = {
            chart: {
                type: 'line',
                height: 250,
                toolbar: {
                    show: false
                }
            },
            series: [{
                name: 'Ventes',
                data: []
            }],
            xaxis: {
                categories: []
            },
            stroke: {
                curve: 'smooth'
            },
            colors: ['#10B981']
        };

        const salesPerformanceChart = new ApexCharts(
            document.querySelector("#sales-performance-chart"), 
            salesPerformanceOptions
        );
        salesPerformanceChart.render();

        return {
            shopsActivityChart,
            salesPerformanceChart
        };
    }

    const charts = initializeCharts();

    // Fonction de mise à jour des données
    function updateData() {
        fetch('/admin/monitoring/data')
            .then(response => response.json())
            .then(data => {
                // Mise à jour des compteurs
                document.getElementById('active-users-count').textContent = data.activeUsers;
                document.getElementById('users-last-hour').textContent = data.usersLastHour;
                document.getElementById('active-sales-count').textContent = data.activeSales;
                document.getElementById('sales-today').textContent = data.salesToday;
                document.getElementById('critical-stock-count').textContent = data.criticalStock;
                document.getElementById('to-order-count').textContent = data.toOrder;
                document.getElementById('active-alerts-count').textContent = data.activeAlerts;
                document.getElementById('unhandled-alerts-count').textContent = data.unhandledAlerts;

                // Mise à jour des graphiques
                charts.shopsActivityChart.updateSeries([{
                    data: data.shopsActivity.values
                }]);
                charts.shopsActivityChart.updateOptions({
                    xaxis: {
                        categories: data.shopsActivity.labels
                    }
                });

                charts.salesPerformanceChart.updateSeries([{
                    data: data.salesPerformance.values
                }]);
                charts.salesPerformanceChart.updateOptions({
                    xaxis: {
                        categories: data.salesPerformance.labels
                    }
                });

                // Mise à jour des activités en temps réel
                const activities = data.realtimeActivities.map(activity => `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${activity.time}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <img class="h-8 w-8 rounded-full" 
                                         src="${activity.user.avatar}" 
                                         alt="${activity.user.name}">
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        ${activity.user.name}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        ${activity.user.role}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${activity.actionClass}">
                                ${activity.action}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${activity.shop}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${activity.statusClass}">
                                ${activity.status}
                            </span>
                        </td>
                    </tr>
                `).join('');

                document.getElementById('realtime-activities').innerHTML = activities;
            });
    }

    // Gestion de l'auto-refresh
    function toggleAutoRefresh() {
        const button = document.getElementById('refresh-toggle');
        isAutoRefreshEnabled = !isAutoRefreshEnabled;

        if (isAutoRefreshEnabled) {
            button.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Auto-refresh activé';
            button.classList.remove('bg-gray-500');
            button.classList.add('bg-white', 'bg-opacity-20');
            autoRefreshInterval = setInterval(updateData, 5000);
        } else {
            button.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Auto-refresh désactivé';
            button.classList.remove('bg-white', 'bg-opacity-20');
            button.classList.add('bg-gray-500');
            clearInterval(autoRefreshInterval);
        }
    }

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
        updateData();
        autoRefreshInterval = setInterval(updateData, 5000);
    });
</script>
@endpush
@endsection
