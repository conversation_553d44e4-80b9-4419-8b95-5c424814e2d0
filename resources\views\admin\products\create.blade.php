@extends('layouts.admin')

@section('content')
<div class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-100 via-white to-pink-100 py-8">
    <!-- Header avec illustration -->
    <div class="w-full max-w-2xl mb-8">
        <div class="flex items-center bg-gradient-to-r from-blue-600 to-pink-500 rounded-t-2xl shadow-xl p-6">
            <div class="flex-shrink-0 mr-4">
                <svg class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7h18M3 12h18M3 17h18" />
                </svg>
            </div>
            <div>
                <h2 class="text-3xl font-bold text-white drop-shadow">Créer un nouveau produit</h2>
                <p class="text-blue-100">Remplis les informations pour ajouter un produit à la boutique</p>
            </div>
            <div class="ml-auto">
                <a href="{{ route('admin.products.index') }}" class="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg shadow hover:bg-blue-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <div class="w-full max-w-2xl bg-white rounded-b-2xl shadow-2xl p-8">
        @if ($errors->any())
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong>Oups ! Il y a des erreurs dans votre saisie :</strong>
                <ul class="mt-2 list-disc list-inside text-sm">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form action="{{ route('admin.products.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Nom (label flottant) -->
                <div class="relative z-0 w-full mb-6 group">
                    <input type="text" name="name" id="name" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 appearance-none focus:outline-none focus:ring-0 focus:border-pink-500 peer" placeholder=" " value="{{ old('name') }}" required />
                    <label for="name" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Nom du produit</label>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Prix (label flottant avec icône) -->
                <div class="relative z-0 w-full mb-6 group">
                    <input type="number" name="price" id="price" step="0.01" min="0" class="block py-2.5 px-14 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 appearance-none focus:outline-none focus:ring-0 focus:border-pink-500 peer" placeholder=" " value="{{ old('price') }}" required />
                    <span class="absolute left-0 top-3 text-gray-400 font-bold">FCFA</span>
                    <label for="price" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 left-14 -z-10 origin-[0] peer-focus:left-14 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Prix</label>
                    @error('price')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Catégorie -->
                <div class="relative z-0 w-full mb-6 group">
                    <select name="category_id" id="category_id" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer" required>
                        <option value="" disabled selected>Choisir une catégorie</option>
                        @foreach($categories as $id => $name)
                            <option value="{{ $id }}" {{ old('category_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                        @endforeach
                    </select>
                    <label for="category_id" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Catégorie</label>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Fournisseurs (visible seulement si catégorie est PAGNE) -->
                <div id="supplierFieldGroup" class="relative z-0 w-full mb-6 group hidden">
                    <div class="flex items-center">
                        <select name="supplier_id" id="supplier_id" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer">
                            <option value="" disabled selected>Choisir un fournisseur</option>
                            @isset($suppliers)
                                @foreach($suppliers as $id => $name)
                                    <option value="{{ $id }}" {{ old('supplier_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            @endisset
                        </select>
                        <a href="#" id="addSupplierBtn" class="ml-2 px-3 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                            <i class="fas fa-plus"></i>
                        </a>
                    </div>
                    <label for="supplier_id" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Fournisseur</label>
                    @error('supplier_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Stock -->
                <div class="relative z-0 w-full mb-6 group">
                    <input type="number" name="stock" id="stock" min="0" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 appearance-none focus:outline-none focus:ring-0 focus:border-pink-500 peer" placeholder=" " value="{{ old('stock', 0) }}" required />
                    <label for="stock" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Stock initial</label>
                    @error('stock')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Stock minimum -->
                <div class="relative z-0 w-full mb-6 group">
                    <input type="number" name="min_stock" id="min_stock" min="0" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 appearance-none focus:outline-none focus:ring-0 focus:border-pink-500 peer" placeholder=" " value="{{ old('min_stock', 1) }}" required />
                    <label for="min_stock" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Stock minimum</label>
                    @error('min_stock')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Unité -->
                <div class="relative z-0 w-full mb-6 group">
                    <select name="unit" id="unit" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer" required>
                        <option value="">Sélectionnez une unité</option>
                        <option value="pièce" {{ old('unit') == 'pièce' ? 'selected' : '' }}>Pièce</option>
                        <option value="paquet" {{ old('unit') == 'paquet' ? 'selected' : '' }}>Paquet</option>
                        <option value="boîte" {{ old('unit') == 'boîte' ? 'selected' : '' }}>Boîte</option>
                        <option value="carton" {{ old('unit') == 'carton' ? 'selected' : '' }}>Carton</option>
                        <option value="bouteille" {{ old('unit') == 'bouteille' ? 'selected' : '' }}>Bouteille</option>
                        <option value="litre" {{ old('unit') == 'litre' ? 'selected' : '' }}>Litre</option>
                        <option value="kilogramme" {{ old('unit') == 'kilogramme' ? 'selected' : '' }}>Kilogramme</option>
                        <option value="sachet" {{ old('unit') == 'sachet' ? 'selected' : '' }}>Sachet</option>
                        <option value="seau" {{ old('unit') == 'seau' ? 'selected' : '' }}>Seau</option>
                        <option value="fût" {{ old('unit') == 'fût' ? 'selected' : '' }}>Fût</option>
                    </select>
                    <label for="unit" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Unité</label>
                    @error('unit')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Boutique -->
                <div class="relative z-0 w-full mb-6 group">
                    <select name="shop_id" id="shop_id" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer" required>
                        <option value="" disabled selected>Choisir une boutique</option>
                        @foreach($shops as $id => $name)
                            <option value="{{ $id }}" {{ old('shop_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                        @endforeach
                    </select>
                    <label for="shop_id" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Boutique</label>
                    @error('shop_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Actif -->
                <div class="relative z-0 w-full mb-6 group flex items-center">
                    <input type="hidden" name="is_active" value="0">
                    <input type="checkbox" name="is_active" id="is_active" value="1" class="mr-2" {{ old('is_active', 1) ? 'checked' : '' }} />
                    <label for="is_active" class="text-md text-gray-700">Activer ce produit</label>
                    @error('is_active')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image avec aperçu -->
                <div class="relative z-0 w-full mb-6 group">
                    <input type="file" name="image" id="image" accept="image/*" class="block w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer" onchange="previewImage(event)" />
                    <label for="image" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Image du produit</label>
                    <div class="mt-3">
                        <img id="imagePreview" src="#" alt="Aperçu de l'image" class="hidden w-24 h-24 object-cover rounded shadow" />
                    </div>
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            <!-- Description -->
            <div class="relative z-0 w-full mb-6 group">
                <textarea name="description" id="description" rows="3" class="block py-2.5 px-0 w-full text-gray-900 bg-transparent border-0 border-b-2 border-blue-400 focus:outline-none focus:ring-0 focus:border-pink-500 peer resize-none" placeholder=" ">{{ old('description') }}</textarea>
                <label for="description" class="absolute text-md text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-pink-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">Description</label>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <!-- Boutons -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('admin.products.index') }}" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors flex items-center"><i class="fas fa-times mr-2"></i>Annuler</a>
                <button type="submit" class="px-6 py-2 bg-gradient-to-r from-pink-500 to-blue-500 text-white font-semibold rounded-lg shadow hover:from-blue-500 hover:to-pink-500 transition-all flex items-center"><i class="fas fa-save mr-2"></i>Enregistrer</button>
            </div>
        </form>
    </div>
</div>

<!-- Modale de création de fournisseur -->
<div id="supplierModal" 
     x-data="supplierModal()"
     x-ref="modal"
     class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
    
    <div class="bg-white rounded-xl shadow-2xl overflow-hidden w-full max-w-lg">
        <!-- En-tête -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6">
            <h2 class="text-xl font-bold text-white">Nouveau Fournisseur</h2>
        </div>
        
        <!-- Corps -->
        <div class="p-6">
            <!-- Champs du formulaire -->
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Nom du fournisseur <span class="text-red-500">*</span></label>
                <input type="text" x-model="supplier.name" class="border rounded w-full px-3 py-2" required>
            </div>
            
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Email</label>
                <input type="email" x-model="supplier.email" class="border rounded w-full px-3 py-2">
            </div>
            
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Téléphone</label>
                <input type="text" x-model="supplier.phone" class="border rounded w-full px-3 py-2">
            </div>
            
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Adresse</label>
                <input type="text" x-model="supplier.address" class="border rounded w-full px-3 py-2">
            </div>
            
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Ville</label>
                <input type="text" x-model="supplier.city" class="border rounded w-full px-3 py-2">
            </div>
            
            <div class="mb-3">
                <label class="block text-gray-700 mb-1">Pays</label>
                <input type="text" x-model="supplier.country" class="border rounded w-full px-3 py-2">
            </div>
            
            <div x-show="error" x-text="error" class="text-red-500 text-xs mb-2"></div>
            
            <div class="flex justify-end gap-2">
                <button @click="$refs.modal.classList.add('hidden')" class="px-4 py-2 border rounded hover:bg-gray-100">
                    Annuler
                </button>
                <button @click="submitSupplier" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Aperçu image JS -->
<script>
function previewImage(event) {
    const input = event.target;
    const preview = document.getElementById('imagePreview');
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.classList.remove('hidden');
        }
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.src = '#';
        preview.classList.add('hidden');
    }
}
</script>

<!-- Script pour gérer l'affichage conditionnel du champ Fournisseurs -->
<script>
    document.getElementById('category_id').addEventListener('change', function() {
        const supplierFieldGroup = document.getElementById('supplierFieldGroup');
        if (this.value && this.options[this.selectedIndex].text === 'PAGNE') {
            supplierFieldGroup.classList.remove('hidden');
        } else {
            supplierFieldGroup.classList.add('hidden');
        }
    });

    // Au chargement de la page, vérifier si la catégorie sélectionnée est PAGNE
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('category_id');
        if (categorySelect.value && categorySelect.options[categorySelect.selectedIndex].text === 'PAGNE') {
            document.getElementById('supplierFieldGroup').classList.remove('hidden');
        }
    });

    // Gestion du bouton d'ajout de fournisseur
    document.getElementById('addSupplierBtn').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('supplierModal').classList.remove('hidden');
    });

    document.getElementById('cancelSupplierBtn').addEventListener('click', function() {
        document.getElementById('supplierModal').classList.add('hidden');
    });

    document.addEventListener('alpine:init', () => {
        console.log('Initialisation Alpine.js - Début');
        
        Alpine.data('supplierModal', function() {
            console.log('Création du composant supplierModal');
            
            return {
                supplier: {
                    name: '',
                    email: '',
                    phone: '',
                    address: '',
                    city: '',
                    country: ''
                },
                error: null,
                
                init() {
                    console.log('Initialisation du composant', this);
                },
                
                handleSubmit() {
                    console.log('Tentative de soumission', this.supplier);
                    
                    try {
                        if(!this.supplier.name) {
                            throw new Error('Le nom est requis');
                        }

                        console.log('Envoi des données au serveur...');
                        
                        fetch('{{ route('admin.suppliers.quickCreate') }}', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.supplier)
                        })
                        .then(response => {
                            console.log('Réponse reçue', response);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Données de réponse', data);
                            
                            if(data.success) {
                                const select = document.getElementById('supplier_id');
                                console.log('Select trouvé:', select);
                                
                                const option = document.createElement('option');
                                option.value = data.supplier.id;
                                option.textContent = data.supplier.name;
                                select.appendChild(option);
                                select.value = data.supplier.id;
                                
                                this.$refs.modal.classList.add('hidden');
                                this.supplier = {};
                                this.error = null;
                                
                                console.log('Fournisseur ajouté avec succès');
                            } else {
                                this.error = data.message || 'Erreur lors de l\'enregistrement';
                                console.error('Erreur du serveur:', data);
                            }
                        })
                        .catch(err => {
                            this.error = 'Erreur de connexion au serveur';
                            console.error('Erreur fetch:', err);
                        });
                    } catch (err) {
                        this.error = err.message;
                        console.error('Erreur dans handleSubmit:', err);
                    }
                }
            }
        });
        
        console.log('Initialisation Alpine.js - Terminée');
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé - Initialisation Alpine');
    
    if (typeof Alpine === 'undefined') {
        console.error('Alpine.js non chargé!');
        return;
    }

    Alpine.data('supplierModal', function() {
        return {
            init() {
                console.log('Modale fournisseur initialisée');
            },
            supplier: {
                name: '',
                email: '',
                phone: '',
                address: '',
                city: '',
                country: ''
            },
            error: null,
            isLoading: false,
            
            submitSupplier() {
                console.log('Tentative d\'enregistrement', this.supplier);
                this.isLoading = true;
                this.error = null;
                
                fetch('{{ route('admin.suppliers.quickCreate') }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.supplier)
                })
                .then(async response => {
                    const data = await response.json();
                    if (!response.ok) {
                        throw new Error(data.message || 'Erreur serveur');
                    }
                    return data;
                })
                .then(data => {
                    console.log('Succès:', data);
                    // Actualiser la liste des fournisseurs
                    const select = document.getElementById('supplier_id');
                    if (select) {
                        const option = new Option(data.supplier.name, data.supplier.id, true, true);
                        select.add(option);
                    }
                    this.$refs.modal.classList.add('hidden');
                    this.supplier = {};
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    this.error = error.message;
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }
        };
    });
});
</script>

<script>
document.addEventListener('alpine:init', () => {
    console.log('Initialisation Alpine.js');
    
    Alpine.data('supplierModal', function() {
        return {
            supplier: {
                name: '',
                email: '',
                phone: '',
                address: '',
                city: '',
                country: ''
            },
            error: null,
            
            submitSupplier() {
                console.log('Tentative d\'envoi:', this.supplier);
                
                fetch('{{ route('admin.suppliers.quickCreate') }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(this.supplier)
                })
                .then(response => {
                    console.log('Réponse brute:', response);
                    if (!response.ok) throw new Error('Erreur HTTP: ' + response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Réponse JSON:', data);
                    if (data.success) {
                        location.reload();
                    } else {
                        this.error = data.message || 'Erreur lors de l\'enregistrement';
                    }
                })
                .catch(error => {
                    console.error('Erreur fetch:', error);
                    this.error = error.message;
                });
            }
        };
    });
});
</script>

@endsection
