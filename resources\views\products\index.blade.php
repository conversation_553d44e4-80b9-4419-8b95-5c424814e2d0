@extends('layouts.app')

@section('title', 'Gestion des produits')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des produits</h1>
        @can('create', App\Models\Product::class)
        <a href="{{ route('products.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouveau produit
        </a>
        @endcan
    </div>

    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('products.index') }}" method="GET" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search">Rechercher</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" 
                           placeholder="Nom, SKU ou code-barres">
                </div>
                
                <div class="col-md-3 mb-3">
                    <label for="category">Catégorie</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">Toutes les catégories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                    {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="stock_status">État du stock</label>
                    <select class="form-control" id="stock_status" name="stock_status">
                        <option value="">Tous les états</option>
                        <option value="available" {{ request('stock_status') == 'available' ? 'selected' : '' }}>
                            En stock
                        </option>
                        <option value="low" {{ request('stock_status') == 'low' ? 'selected' : '' }}>
                            Stock faible
                        </option>
                        <option value="out" {{ request('stock_status') == 'out' ? 'selected' : '' }}>
                            Rupture de stock
                        </option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="sort">Trier par</label>
                    <select class="form-control" id="sort" name="sort">
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Nom</option>
                        <option value="sku" {{ request('sort') == 'sku' ? 'selected' : '' }}>SKU</option>
                        <option value="created_at" {{ request('sort') == 'created_at' ? 'selected' : '' }}>Date de création</option>
                    </select>
                </div>

                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Filtrer
                    </button>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des produits -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>SKU</th>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Prix de vente</th>
                            <th>Stock total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($products as $product)
                        <tr>
                            <td class="text-center">
                                @if($product->image_path)
                                    <img src="{{ Storage::url($product->image_path) }}" 
                                         alt="{{ $product->name }}"
                                         class="img-thumbnail"
                                         style="max-height: 50px;">
                                @else
                                    <i class="fas fa-box fa-2x text-gray-300"></i>
                                @endif
                            </td>
                            <td>{{ $product->sku }}</td>
                            <td>{{ $product->name }}</td>
                            <td>{{ $product->category->name }}</td>
                            <td>{{ number_format($product->selling_price, 0, ',', ' ') }} FCFA</td>
                            <td>
                                @php
                                    $totalStock = $product->stocks->sum('quantity');
                                    $stockStatus = $product->hasLowStock() ? 'text-warning' : 
                                                ($totalStock > 0 ? 'text-success' : 'text-danger');
                                @endphp
                                <span class="{{ $stockStatus }}">
                                    {{ $totalStock }} unités
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-{{ $product->status === 'active' ? 'success' : 'danger' }}">
                                    {{ $product->status === 'active' ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ route('products.show', $product) }}" 
                                   class="btn btn-sm btn-info"
                                   title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                @can('update', $product)
                                <a href="{{ route('products.edit', $product) }}" 
                                   class="btn btn-sm btn-primary"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcan

                                @can('delete', $product)
                                <form action="{{ route('products.destroy', $product) }}" 
                                      method="POST" 
                                      class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="btn btn-sm btn-danger"
                                            title="Supprimer"
                                            onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center">Aucun produit trouvé</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-end">
                {{ $products->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
