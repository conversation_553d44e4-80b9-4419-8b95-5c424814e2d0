<?php

namespace App\Http\Controllers;

use App\Models\Stock;
use App\Models\Product;
use App\Models\Shop;
use App\Models\StockMovement;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\StockMovementRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use App\Notifications\LowStockAlert;
use App\Notifications\StockMovementNotification;

class StockMovementController extends Controller
{
    public function index(Request $request)
    {
        $query = StockMovement::with(['stock.product', 'stock.shop', 'user'])
            ->latest();

        // Filtrer par boutique
        if ($request->has('shop_id')) {
            $query->whereHas('stock', function ($q) use ($request) {
                $q->where('shop_id', $request->shop_id);
            });
        }

        // Filtrer par produit
        if ($request->has('product_id')) {
            $query->whereHas('stock', function ($q) use ($request) {
                $q->where('product_id', $request->product_id);
            });
        }

        // Filtrer par type de mouvement
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filtrer par date
        if ($request->has('date_start')) {
            $query->whereDate('created_at', '>=', $request->date_start);
        }
        if ($request->has('date_end')) {
            $query->whereDate('created_at', '<=', $request->date_end);
        }

        $movements = $query->paginate(20);
        $shops = Shop::orderBy('name')->get();
        $products = Product::orderBy('name')->get();

        return view('stock.movements.index', compact('movements', 'shops', 'products'));
    }

    public function create()
    {
        $shops = Shop::orderBy('name')->get();
        $products = Product::with('stocks')->orderBy('name')->get();
        
        return view('stock.movements.create', compact('shops', 'products'));
    }

    public function store(StockMovementRequest $request)
    {
        try {
            DB::beginTransaction();

            $stock = Stock::where('product_id', $request->product_id)
                         ->where('shop_id', $request->shop_id)
                         ->firstOrFail();

            $oldQuantity = $stock->quantity;

            switch ($request->type) {
                case 'in':
                    $stock->add($request->quantity, auth()->id(), $request->description);
                    $message = 'Entrée de stock enregistrée avec succès.';
                    break;

                case 'out':
                    if ($stock->quantity < $request->quantity) {
                        throw new \Exception('Stock insuffisant pour cette sortie.');
                    }
                    $stock->remove($request->quantity, auth()->id(), $request->description);
                    $message = 'Sortie de stock enregistrée avec succès.';
                    break;

                case 'adjustment':
                    $stock->adjust($request->quantity, auth()->id(), $request->description);
                    $message = 'Ajustement de stock effectué avec succès.';
                    break;
            }

            // Vérifier si le stock est bas après le mouvement
            if ($stock->isLow()) {
                // Notifier les administrateurs et le gérant de la boutique
                $admins = User::role('admin')->get();
                $manager = $stock->shop->manager;
                
                $recipients = $admins->push($manager);
                Notification::send($recipients, new LowStockAlert($stock));
            }

            // Notifier les administrateurs du mouvement de stock
            $notification = new StockMovementNotification($stock, $oldQuantity, $request->type);
            Notification::send(User::role('admin')->get(), $notification);

            DB::commit();

            return redirect()
                ->route('stock.movements.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue : ' . $e->getMessage());
        }
    }

    public function transfer()
    {
        $shops = Shop::orderBy('name')->get();
        $products = Product::with('stocks')->orderBy('name')->get();
        
        return view('stock.movements.transfer', compact('shops', 'products'));
    }

    public function processTransfer(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'from_shop_id' => 'required|exists:shops,id',
            'to_shop_id' => 'required|exists:shops,id|different:from_shop_id',
            'quantity' => 'required|integer|min:1',
            'description' => 'nullable|string|max:255'
        ]);

        try {
            DB::beginTransaction();

            $sourceStock = Stock::where('product_id', $request->product_id)
                               ->where('shop_id', $request->from_shop_id)
                               ->firstOrFail();

            $destinationStock = Stock::firstOrCreate(
                [
                    'product_id' => $request->product_id,
                    'shop_id' => $request->to_shop_id
                ],
                [
                    'quantity' => 0,
                    'min_stock' => $sourceStock->min_stock
                ]
            );

            if ($sourceStock->quantity < $request->quantity) {
                throw new \Exception('Stock insuffisant pour ce transfert.');
            }

            // Effectuer le transfert
            $sourceStock->transfer(
                $request->quantity,
                $destinationStock,
                auth()->id(),
                $request->description ?? 'Transfert entre boutiques'
            );

            // Notifier les gérants des deux boutiques
            $notification = new StockMovementNotification($sourceStock, $sourceStock->quantity + $request->quantity, 'transfer');
            $recipients = collect([$sourceStock->shop->manager, $destinationStock->shop->manager]);
            Notification::send($recipients, $notification);

            DB::commit();

            return redirect()
                ->route('stock.movements.index')
                ->with('success', 'Transfert de stock effectué avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue : ' . $e->getMessage());
        }
    }

    public function history(Stock $stock)
    {
        $movements = $stock->movements()
            ->with('user')
            ->latest()
            ->paginate(20);

        return view('stock.movements.history', compact('stock', 'movements'));
    }
}
