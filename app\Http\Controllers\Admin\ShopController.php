<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ShopController extends Controller
{
    /**
     * Display a listing of the shops.
     */
    public function index()
    {
        $shops = Shop::withCount('users')->paginate(10);
        return view('admin.shops.index', compact('shops'));
    }

    /**
     * Show the form for creating a new shop.
     */
    public function create()
    {
        return view('admin.shops.create');
    }

    /**
     * Store a newly created shop in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:shops',
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255|unique:shops',
            'is_active' => 'required|boolean'
        ]);

        try {
            DB::beginTransaction();

            // Générer le slug à partir du nom
            $validated['slug'] = Str::slug($validated['name']);

            $shop = Shop::create($validated);

            activity()
                ->performedOn($shop)
                ->causedBy(auth()->user())
                ->log('shop_created');

            DB::commit();

            return redirect()
                ->route('admin.shops.index')
                ->with('success', 'Boutique créée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('[ShopController][store] Erreur lors de la création de la boutique : ' . $e->getMessage(), [
                'exception' => $e,
                'input' => $request->all(),
                'user_id' => auth()->id()
            ]);
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de la création de la boutique : ' . $e->getMessage());
        }
    }

    /**
     * Display the specified shop.
     */
    public function show(Shop $shop)
    {
        $shop->load(['users']); // Suppression du withCount('orders') qui causait l'erreur
        
        return view('admin.shops.show', compact('shop'));
    }

    /**
     * Show the form for editing the specified shop.
     */
    public function edit(Shop $shop)
    {
        return view('admin.shops.edit', compact('shop'));
    }

    /**
     * Update the specified shop in storage.
     */
    public function update(Request $request, Shop $shop)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:shops,name,' . $shop->id,
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255|unique:shops,email,' . $shop->id,
            'is_active' => 'required|boolean'
        ]);

        try {
            DB::beginTransaction();

            $shop->update($validated);

            activity()
                ->performedOn($shop)
                ->causedBy(auth()->user())
                ->log('shop_updated');

            DB::commit();

            return redirect()
                ->route('admin.shops.index')
                ->with('success', 'Boutique mise à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de la mise à jour de la boutique.');
        }
    }

    /**
     * Remove the specified shop from storage.
     */
    public function destroy(Shop $shop)
    {
        try {
            if ($shop->users()->count() > 0) {
                return back()->with('error', 'Impossible de supprimer une boutique qui a des utilisateurs.');
            }

            DB::beginTransaction();

            activity()
                ->performedOn($shop)
                ->causedBy(auth()->user())
                ->log('shop_deleted');

            $shop->delete();

            DB::commit();

            return redirect()
                ->route('admin.shops.index')
                ->with('success', 'Boutique supprimée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la boutique.');
        }
    }

    /**
     * Toggle shop status between active and inactive
     */
    public function toggleStatus(Shop $shop)
    {
        try {
            DB::beginTransaction();

            $newStatus = $shop->is_active === 1 ? 0 : 1;
            $shop->is_active = $newStatus;
            $shop->save();

            activity()
                ->performedOn($shop)
                ->causedBy(auth()->user())
                ->withProperties([
                    'old_status' => $newStatus === 1 ? 0 : 1,
                    'new_status' => $newStatus
                ])
                ->log('shop_status_changed');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Le statut de la boutique a été mis à jour avec succès.",
                'status' => $newStatus
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Une erreur est survenue lors de la mise à jour du statut."
            ], 500);
        }
    }
}
