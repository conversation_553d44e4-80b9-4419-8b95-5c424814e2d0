@extends('layouts.app')

@section('title', 'Nouvel Approvisionnement')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Nouvel Approvisionnement</h1>
        <a href="{{ route('supplies.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('supplies.store') }}" method="POST" id="supplyForm">
                @csrf
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="supplier_id" class="form-label required">Fournisseur</label>
                            <select name="supplier_id" id="supplier_id" class="form-select @error('supplier_id') is-invalid @enderror" required>
                                <option value="">Sélectionner un fournisseur</option>
                                @foreach($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}" @selected(old('supplier_id') == $supplier->id)>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('supplier_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="shop_id" class="form-label required">Boutique</label>
                            <select name="shop_id" id="shop_id" class="form-select @error('shop_id') is-invalid @enderror" required>
                                <option value="">Sélectionner une boutique</option>
                                @foreach($shops as $shop)
                                    <option value="{{ $shop->id }}" @selected(old('shop_id') == $shop->id)>
                                        {{ $shop->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('shop_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="expected_delivery_date" class="form-label required">Date de livraison prévue</label>
                            <input type="date" class="form-control @error('expected_delivery_date') is-invalid @enderror" 
                                   id="expected_delivery_date" name="expected_delivery_date" 
                                   value="{{ old('expected_delivery_date') }}" required>
                            @error('expected_delivery_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="4">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <h4 class="mb-3">Produits</h4>
                <div class="table-responsive mb-4">
                    <table class="table" id="products-table">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix Unitaire</th>
                                <th>Total</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr id="empty-row">
                                <td colspan="5" class="text-center">Aucun produit ajouté</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Total</strong></td>
                                <td><span id="grand-total">0.00</span> €</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="mb-4">
                    <button type="button" class="btn btn-outline-primary" id="add-product">
                        <i class="fas fa-plus"></i> Ajouter un produit
                    </button>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer l'approvisionnement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'ajout de produit -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="product_id" class="form-label required">Produit</label>
                    <select class="form-select" id="product_id" required>
                        <option value="">Sélectionner un produit</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="quantity" class="form-label required">Quantité</label>
                    <input type="number" class="form-control" id="quantity" min="1" required>
                </div>
                <div class="mb-3">
                    <label for="unit_price" class="form-label required">Prix unitaire</label>
                    <input type="number" class="form-control" id="unit_price" min="0" step="0.01" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="add-product-confirm">Ajouter</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        let products = [];
        let rowCount = 0;

        // Initialisation de Select2 pour le fournisseur et la boutique
        $('#supplier_id, #shop_id').select2({
            theme: 'bootstrap-5'
        });

        // Chargement des produits lors du changement de fournisseur
        $('#supplier_id').on('change', function() {
            const supplierId = $(this).val();
            if (supplierId) {
                $.get(`/api/suppliers/${supplierId}/products`, function(data) {
                    products = data;
                    updateProductSelect();
                });
            }
        });

        // Mise à jour du select des produits
        function updateProductSelect() {
            const $select = $('#product_id');
            $select.empty().append('<option value="">Sélectionner un produit</option>');
            products.forEach(product => {
                $select.append(`<option value="${product.id}" data-price="${product.pivot.unit_price}">
                    ${product.name}
                </option>`);
            });
        }

        // Événement d'ajout de produit
        $('#add-product').on('click', function() {
            if (!$('#supplier_id').val()) {
                alert('Veuillez d\'abord sélectionner un fournisseur');
                return;
            }
            $('#productModal').modal('show');
        });

        // Mise à jour du prix unitaire lors de la sélection d'un produit
        $('#product_id').on('change', function() {
            const option = $(this).find(':selected');
            if (option.val()) {
                $('#unit_price').val(option.data('price'));
            }
        });

        // Confirmation d'ajout de produit
        $('#add-product-confirm').on('click', function() {
            const productId = $('#product_id').val();
            const quantity = $('#quantity').val();
            const unitPrice = $('#unit_price').val();

            if (!productId || !quantity || !unitPrice) {
                alert('Tous les champs sont obligatoires');
                return;
            }

            const product = products.find(p => p.id == productId);
            const total = quantity * unitPrice;

            addProductRow(product, quantity, unitPrice, total);
            updateGrandTotal();
            $('#productModal').modal('hide');
            $('#product_id, #quantity, #unit_price').val('');
        });

        // Ajout d'une ligne de produit
        function addProductRow(product, quantity, unitPrice, total) {
            $('#empty-row').remove();
            rowCount++;

            const row = `
                <tr>
                    <td>
                        ${product.name}
                        <input type="hidden" name="items[${rowCount}][product_id]" value="${product.id}">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm quantity" 
                               name="items[${rowCount}][quantity]" value="${quantity}" min="1">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm unit-price" 
                               name="items[${rowCount}][unit_price]" value="${unitPrice}" min="0" step="0.01">
                    </td>
                    <td class="row-total">${total.toFixed(2)} €</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-row">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#products-table tbody').append(row);
        }

        // Mise à jour du total
        function updateGrandTotal() {
            let total = 0;
            $('.row-total').each(function() {
                total += parseFloat($(this).text());
            });
            $('#grand-total').text(total.toFixed(2));
        }

        // Suppression d'une ligne
        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
            if ($('#products-table tbody tr').length === 0) {
                $('#products-table tbody').append(`
                    <tr id="empty-row">
                        <td colspan="5" class="text-center">Aucun produit ajouté</td>
                    </tr>
                `);
            }
            updateGrandTotal();
        });

        // Mise à jour des totaux lors de la modification des quantités ou prix
        $(document).on('change', '.quantity, .unit-price', function() {
            const $row = $(this).closest('tr');
            const quantity = parseFloat($row.find('.quantity').val());
            const unitPrice = parseFloat($row.find('.unit-price').val());
            const total = quantity * unitPrice;
            $row.find('.row-total').text(total.toFixed(2) + ' €');
            updateGrandTotal();
        });

        // Validation du formulaire
        $('#supplyForm').on('submit', function(e) {
            if ($('#products-table tbody tr').length === 0 || $('#empty-row').length > 0) {
                e.preventDefault();
                alert('Veuillez ajouter au moins un produit');
            }
        });
    });
</script>
@endpush

@push('styles')
<style>
    .required::after {
        content: " *";
        color: red;
    }
</style>
@endpush
