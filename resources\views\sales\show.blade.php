@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>Détails de la Vente #{{ $sale->id }}</h2>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>Boutique</th>
                                    <td>{{ $sale->shop->name }}</td>
                                </tr>
                                <tr>
                                    <th>Vendeur</th>
                                    <td>{{ $sale->user->name }}</td>
                                </tr>
                                <tr>
                                    <th>Montant</th>
                                    <td>{{ number_format($sale->amount, 2) }} €</td>
                                </tr>
                                <tr>
                                    <th>Méthode de paiement</th>
                                    <td>{{ ucfirst($sale->payment_method) }}</td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        <span class="badge bg-{{ $sale->status === 'completed' ? 'success' : ($sale->status === 'pending' ? 'warning' : 'danger') }}">
                                            {{ ucfirst($sale->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Date de création</th>
                                    <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Dernière modification</th>
                                    <td>{{ $sale->updated_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                @if($sale->notes)
                                <tr>
                                    <th>Notes</th>
                                    <td>{{ $sale->notes }}</td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-between mt-3">
                        <a href="{{ route('sales.index') }}" class="btn btn-secondary">Retour à la liste</a>
                        <div>
                            <a href="{{ route('sales.edit', $sale) }}" class="btn btn-warning">Modifier</a>
                            <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette vente ?')">Supprimer</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
