<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tax_reports', function (Blueprint $table) {
            $table->id();
            $table->string('period'); // ex : 2025-04
            $table->decimal('total_tva_collected', 15, 2)->default(0);
            $table->decimal('total_tva_deductible', 15, 2)->default(0);
            $table->decimal('total_taxes', 15, 2)->default(0);
            $table->enum('status', ['draft', 'submitted', 'paid'])->default('draft');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tax_reports');
    }
};
