<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounting_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shop_id')->constrained()->onDelete('cascade');
            $table->string('transaction_type'); // vente, achat, dépense, etc.
            $table->decimal('amount', 15, 2);
            $table->string('reference_type'); // sale, supply, expense, etc.
            $table->unsignedBigInteger('reference_id');
            $table->string('description');
            $table->date('transaction_date');
            $table->string('payment_method')->nullable();
            $table->string('status')->default('completed');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['reference_type', 'reference_id']);
            $table->index('transaction_date');
        });

        Schema::create('accounting_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // revenu ou dépense
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('accounting_periods', function (Blueprint $table) {
            $table->id();
            $table->date('start_date');
            $table->date('end_date');
            $table->string('status')->default('open');
            $table->json('summary')->nullable();
            $table->timestamps();
        });

        Schema::create('financial_reports', function (Blueprint $table) {
            $table->id();
            $table->string('report_type'); // bilan, compte de résultat, etc.
            $table->foreignId('accounting_period_id')->constrained()->onDelete('cascade');
            $table->foreignId('shop_id')->nullable()->constrained()->onDelete('cascade');
            $table->json('content');
            $table->string('status')->default('draft');
            $table->timestamp('generated_at');
            $table->timestamp('validated_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_reports');
        Schema::dropIfExists('accounting_periods');
        Schema::dropIfExists('accounting_categories');
        Schema::dropIfExists('accounting_transactions');
    }
};
