<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ShopController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\ActivityController;
use App\Http\Controllers\Admin\MonitoringController;
use App\Http\Controllers\Admin\AlertController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\ApprovalController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Admin\StockController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductImageController;
use App\Http\Controllers\Admin\SaleController;
use App\Http\Controllers\Admin\RecoveryController;
use App\Http\Controllers\Admin\AccountingController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\AccountController;
use App\Http\Controllers\Admin\Accounting\ExpenseController;

Route::prefix('admin')->middleware(['auth', 'role:admin'])->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Gestion des boutiques
    Route::prefix('shops')->name('shops.')->group(function () {
        Route::get('/', [ShopController::class, 'index'])->name('index');
        Route::get('/create', [ShopController::class, 'create'])->name('create');
        Route::post('/', [ShopController::class, 'store'])->name('store');
        Route::get('/{shop}', [ShopController::class, 'show'])->name('show');
        Route::get('/{shop}/edit', [ShopController::class, 'edit'])->name('edit');
        Route::put('/{shop}', [ShopController::class, 'update'])->name('update');
        Route::delete('/{shop}', [ShopController::class, 'destroy'])->name('destroy');
        Route::post('/{shop}/toggle-status', [ShopController::class, 'toggleStatus'])->name('toggle-status');
    });
    
    // Gestion des utilisateurs
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/create', [UserController::class, 'create'])->name('create');
        Route::post('/', [UserController::class, 'store'])->name('store');
        Route::get('/{user}', [UserController::class, 'show'])->name('show');
        Route::get('/{user}/edit', [UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [UserController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/bulk-action', [UserController::class, 'bulkAction'])->name('bulk-action');
        Route::get('/{user}/last-login', [UserController::class, 'lastLogin'])->name('last-login');
    });
    
    // Journal d'activités
    Route::get('/activities', [ActivityController::class, 'index'])->name('activities.index');
    
    // Monitoring
    Route::get('/monitoring', [MonitoringController::class, 'index'])->name('monitoring.index');
    Route::get('/monitoring/stats', [MonitoringController::class, 'stats'])->name('monitoring.stats');
    
    // Gestion des stocks
    Route::resource('stocks', StockController::class)->names([
        'index' => 'stocks.index',
        'create' => 'stocks.create',
        'store' => 'stocks.store',
        'show' => 'stocks.show',
        'edit' => 'stocks.edit',
        'update' => 'stocks.update',
        'destroy' => 'stocks.destroy',
    ]);
    
    // Alertes
    Route::get('/alerts', [AlertController::class, 'index'])->name('alerts.index');
    Route::post('/alerts/mark-as-read', [AlertController::class, 'markAsRead'])->name('alerts.mark-as-read');

    // Paramètres
    Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
    Route::put('/settings', [SettingController::class, 'update'])->name('settings.update');
    Route::post('/settings/clear-cache', [SettingController::class, 'clearCache'])->name('settings.clear-cache');

    // Gestion des produits
    Route::resource('products', ProductController::class);
    Route::post('products/{product}/upload', [ProductImageController::class, 'store'])->name('products.upload');
    Route::delete('products/{product}/images/{image}', [ProductImageController::class, 'destroy'])->name('products.images.destroy');

    // Gestion des ventes
    Route::resource('sales', SaleController::class);
    Route::get('sales/{sale}/invoice', [SaleController::class, 'invoice'])->name('sales.invoice');
    Route::post('sales/{sale}/validate', [SaleController::class, 'validateSale'])->name('sales.validate');

    // Comptabilité
    Route::prefix('accounting')->name('accounting.')->group(function () {
        Route::get('/', [AccountingController::class, 'index'])->name('index');
        Route::get('/revenue', [AccountingController::class, 'revenue'])->name('revenue');
        Route::get('/balance', [AccountingController::class, 'balance'])->name('balance');
        Route::get('/reports', [AccountingController::class, 'reports'])->name('reports');
        Route::get('/export', [AccountingController::class, 'export'])->name('export');
        Route::resource('accounts', AccountController::class);
        
        // Dépenses - CRUD (groupe unique, sans doublon)
        Route::prefix('expenses')->name('expenses.')->group(function () {
            Route::get('/', [ExpenseController::class, 'index'])->name('index');
            Route::get('/create', [ExpenseController::class, 'create'])->name('create');
            Route::post('/', [ExpenseController::class, 'store'])->name('store');
            Route::get('/{expense}/edit', [ExpenseController::class, 'edit'])->name('edit');
            Route::put('/{expense}', [ExpenseController::class, 'update'])->name('update');
            Route::delete('/{expense}', [ExpenseController::class, 'destroy'])->name('destroy');
        });
    });

    // Gestion des recouvrements
    Route::get('recoveries/dashboard', [RecoveryController::class, 'dashboard'])->name('recoveries.dashboard');
    Route::resource('recoveries', RecoveryController::class);

    // Approbations
    Route::get('/approvals/credit-sales', [ApprovalController::class, 'creditSales'])->name('approvals.credit-sales');
    Route::post('/approvals/credit-sales/{sale}', [ApprovalController::class, 'approveCreditSale'])->name('approvals.credit-sales.approve');
    
    // Rapports
    Route::get('/reports/sales', [ReportController::class, 'sales'])->name('reports.sales');
    Route::get('/reports/inventory', [ReportController::class, 'inventory'])->name('reports.inventory');
    Route::get('/reports/financial', [ReportController::class, 'financial'])->name('reports.financial');

    // Notifications
    Route::get('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
    Route::get('notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
});
