<?php

namespace App\Http\Controllers\Admin\Accounting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class TaxController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $taxes = \App\Models\Tax::orderBy('name')->get();
        return view('admin.accounting.taxes.index', compact('taxes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.accounting.taxes.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0',
            'type' => 'required|in:TVA,impot,autre',
            'description' => 'nullable|string',
        ]);
        \App\Models\Tax::create($validated);
        return redirect()->route('admin.accounting.taxes.index')->with('success', 'Taxe ajoutée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $tax = \App\Models\Tax::findOrFail($id);
        return view('admin.accounting.taxes.edit', compact('tax'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $tax = \App\Models\Tax::findOrFail($id);
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0',
            'type' => 'required|in:TVA,impot,autre',
            'description' => 'nullable|string',
        ]);
        $tax->update($validated);
        return redirect()->route('admin.accounting.taxes.index')->with('success', 'Taxe modifiée avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $tax = \App\Models\Tax::findOrFail($id);
        $tax->delete();
        return redirect()->route('admin.accounting.taxes.index')->with('success', 'Taxe supprimée.');
    }
}
