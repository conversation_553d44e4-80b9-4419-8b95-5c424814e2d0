@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Dé<PERSON> de la Boutique</h5>
                    @can('edit shops')
                    <a href="{{ route('shops.edit', $shop) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                    @endcan
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Informations Générales</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Code</dt>
                                <dd class="col-sm-8">{{ $shop->code }}</dd>

                                <dt class="col-sm-4">Nom</dt>
                                <dd class="col-sm-8">{{ $shop->name }}</dd>

                                <dt class="col-sm-4">Type</dt>
                                <dd class="col-sm-8">
                                    @if($shop->is_main_shop)
                                        <span class="badge bg-primary">Principale</span>
                                    @else
                                        <span class="badge bg-secondary">Annexe</span>
                                    @endif
                                </dd>

                                <dt class="col-sm-4">Statut</dt>
                                <dd class="col-sm-8">
                                    @if($shop->is_active)
                                        <span class="badge bg-success">Actif</span>
                                    @else
                                        <span class="badge bg-danger">Inactif</span>
                                    @endif
                                </dd>
                            </dl>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-muted">Contact</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Adresse</dt>
                                <dd class="col-sm-8">{{ $shop->address }}</dd>

                                <dt class="col-sm-4">Téléphone</dt>
                                <dd class="col-sm-8">{{ $shop->phone }}</dd>

                                <dt class="col-sm-4">Email</dt>
                                <dd class="col-sm-8">{{ $shop->email ?? '-' }}</dd>
                            </dl>
                        </div>
                    </div>

                    @if(!$shop->is_main_shop)
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted">Boutique Principale</h6>
                            <p>
                                @if($shop->parentShop)
                                    <a href="{{ route('shops.show', $shop->parentShop) }}">
                                        {{ $shop->parentShop->name }}
                                    </a>
                                @else
                                    <span class="text-muted">Non définie</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    @endif

                    @if($shop->is_main_shop)
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted">Boutiques Annexes</h6>
                            @if($shop->childShops->count() > 0)
                                <div class="list-group">
                                    @foreach($shop->childShops as $annexe)
                                        <a href="{{ route('shops.show', $annexe) }}" 
                                           class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            {{ $annexe->name }}
                                            <span class="badge bg-primary rounded-pill">
                                                {{ $annexe->code }}
                                            </span>
                                        </a>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted">Aucune boutique annexe</p>
                            @endif
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">Utilisateurs Affectés</h6>
                            @if($shop->users->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>Email</th>
                                                <th>Rôle</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($shop->users as $user)
                                                <tr>
                                                    <td>{{ $user->name }}</td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>
                                                        @foreach($user->roles as $role)
                                                            <span class="badge bg-info">{{ $role->name }}</span>
                                                        @endforeach
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-muted">Aucun utilisateur affecté</p>
                            @endif
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('shops.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
