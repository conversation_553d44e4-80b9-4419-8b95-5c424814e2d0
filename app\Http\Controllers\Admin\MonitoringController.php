<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Stock;
use App\Models\User;
use App\Models\Alert;
use App\Models\Shop;
use App\Models\Product;
use Carbon\Carbon;

class MonitoringController extends Controller
{
    public function index()
    {
        return view('admin.monitoring.index');
    }

    /**
     * Fournit les données pour le dashboard de monitoring en temps réel
     */
    public function data()
    {
        // Utilisateurs actifs
        $activeUsers = User::where('last_seen_at', '>=', Carbon::now()->subMinutes(5))->count();
        $usersLastHour = User::where('last_seen_at', '>=', Carbon::now()->subHour())->count();

        // Ventes
        $activeSales = Sale::whereIn('status', ['pending', 'processing'])->count();
        $salesToday = Sale::whereDate('created_at', Carbon::today())->count();

        // Stock
        $criticalStock = Stock::whereColumn('quantity', '<=', 'min_stock')->count();
        $toOrder = Stock::whereColumn('quantity', '<=', 'min_stock')->where('ordered', false)->count();

        // Alertes
        $activeAlerts = Alert::whereNull('read_at')->count();
        $unhandledAlerts = Alert::whereNull('read_at')->where('level', 'danger')->count();

        // Activité par boutique
        $shopsActivity = $this->getShopsActivity();

        // Performance des ventes
        $salesPerformance = $this->getSalesPerformance();

        // Activités en temps réel
        $realtimeActivities = $this->getRealtimeActivities();

        return response()->json([
            'activeUsers' => $activeUsers,
            'usersLastHour' => $usersLastHour,
            'activeSales' => $activeSales,
            'salesToday' => $salesToday,
            'criticalStock' => $criticalStock,
            'toOrder' => $toOrder,
            'activeAlerts' => $activeAlerts,
            'unhandledAlerts' => $unhandledAlerts,
            'shopsActivity' => $shopsActivity,
            'salesPerformance' => $salesPerformance,
            'realtimeActivities' => $realtimeActivities
        ]);
    }

    /**
     * Récupère les données d'activité par boutique
     */
    private function getShopsActivity()
    {
        $shops = Shop::all();
        $labels = $shops->pluck('name')->toArray();
        
        $values = $shops->map(function ($shop) {
            return Sale::where('shop_id', $shop->id)
                ->whereDate('created_at', '>=', Carbon::now()->subDays(7))
                ->count();
        })->toArray();

        return [
            'labels' => $labels,
            'values' => $values
        ];
    }

    /**
     * Récupère les données de performance des ventes sur les 7 derniers jours
     */
    private function getSalesPerformance()
    {
        $days = collect(range(6, 0))->map(function ($day) {
            return Carbon::now()->subDays($day)->format('Y-m-d');
        });

        $labels = $days->map(function ($day) {
            return Carbon::parse($day)->format('d/m');
        })->toArray();

        $sales = Sale::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereDate('created_at', '>=', Carbon::now()->subDays(7))
            ->groupBy('date')
            ->pluck('count', 'date')
            ->toArray();

        $values = $days->map(function ($day) use ($sales) {
            return $sales[$day] ?? 0;
        })->toArray();

        return [
            'labels' => $labels,
            'values' => $values
        ];
    }

    /**
     * Récupère les activités en temps réel pour le tableau
     */
    private function getRealtimeActivities()
    {
        // Récupérer les dernières ventes
        $sales = Sale::with('user', 'shop')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($sale) {
                $statusClass = $this->getStatusClass($sale->status);
                $actionClass = 'bg-blue-100 text-blue-800';
                
                return [
                    'time' => $sale->created_at->format('H:i'),
                    'user' => [
                        'name' => optional($sale->user)->name ?? 'Système',
                        'avatar' => optional($sale->user)->avatar_url ?? '/img/default-avatar.png',
                        'role' => optional($sale->user)->role ?? 'Système'
                    ],
                    'action' => 'Vente #' . $sale->id,
                    'actionClass' => $actionClass,
                    'shop' => optional($sale->shop)->name ?? 'Boutique principale',
                    'status' => ucfirst($sale->status),
                    'statusClass' => $statusClass
                ];
            });

        // Récupérer les derniers mouvements de stock
        $stocks = Stock::with('product', 'user')
            ->orderBy('updated_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($stock) {
                $statusClass = $stock->quantity <= $stock->min_stock 
                    ? 'bg-red-100 text-red-800' 
                    : 'bg-green-100 text-green-800';
                $actionClass = 'bg-yellow-100 text-yellow-800';
                $status = $stock->quantity <= $stock->min_stock ? 'Stock critique' : 'Stock OK';
                
                return [
                    'time' => $stock->updated_at->format('H:i'),
                    'user' => [
                        'name' => optional($stock->user)->name ?? 'Système',
                        'avatar' => optional($stock->user)->avatar_url ?? '/img/default-avatar.png',
                        'role' => optional($stock->user)->role ?? 'Système'
                    ],
                    'action' => 'Stock ' . optional($stock->product)->name,
                    'actionClass' => $actionClass,
                    'shop' => optional($stock->shop)->name ?? 'Boutique principale',
                    'status' => $status,
                    'statusClass' => $statusClass
                ];
            });

        // Récupérer les dernières connexions utilisateurs
        $users = User::whereNotNull('last_seen_at')
            ->orderBy('last_seen_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($user) {
                $statusClass = 'bg-green-100 text-green-800';
                $actionClass = 'bg-purple-100 text-purple-800';
                
                return [
                    'time' => optional($user->last_seen_at)->format('H:i'),
                    'user' => [
                        'name' => $user->name,
                        'avatar' => $user->avatar_url ?? '/img/default-avatar.png',
                        'role' => $user->role
                    ],
                    'action' => 'Connexion',
                    'actionClass' => $actionClass,
                    'shop' => $user->shop->name ?? 'Administration',
                    'status' => 'En ligne',
                    'statusClass' => $statusClass
                ];
            });

        // Fusionner et trier par heure
        return $sales->merge($stocks)->merge($users)
            ->sortByDesc('time')
            ->values()
            ->take(10)
            ->toArray();
    }

    /**
     * Retourne la classe CSS pour un statut donné
     */
    private function getStatusClass($status)
    {
        switch ($status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'processing':
                return 'bg-blue-100 text-blue-800';
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }
}
