@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h2>Tableau de Bord Comptable</h2>
                </div>

                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Revenus Totaux</h5>
                                    <h3>{{ number_format($summary['total_revenue'], 2) }} FCFA</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Dépenses Totales</h5>
                                    <h3>{{ number_format($summary['total_expenses'], 2) }} FCFA</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card {{ $summary['net_income'] >= 0 ? 'bg-info' : 'bg-warning' }} text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Résultat Net</h5>
                                    <h3>{{ number_format($summary['net_income'], 2) }} FCFA</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Transactions</h5>
                                    <h3>{{ $summary['transaction_count'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Transactions Récentes</h5>
                                    <a href="{{ route('accounting.transactions.create') }}" class="btn btn-primary">
                                        Nouvelle Transaction
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Type</th>
                                                    <th>Description</th>
                                                    <th>Montant</th>
                                                    <th>Magasin</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($recentTransactions as $transaction)
                                                    <tr>
                                                        <td>{{ $transaction->transaction_date->format('d/m/Y') }}</td>
                                                        <td>
                                                            <span class="badge {{ $transaction->transaction_type == 'revenue' ? 'bg-success' : 'bg-danger' }}">
                                                                {{ $transaction->transaction_type == 'revenue' ? 'Revenu' : 'Dépense' }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $transaction->description }}</td>
                                                        <td>{{ number_format($transaction->amount, 2) }} FCFA</td>
                                                        <td>{{ $transaction->shop->name }}</td>
                                                        <td>
                                                            <span class="badge {{ $transaction->status == 'completed' ? 'bg-success' : 'bg-warning' }}">
                                                                {{ $transaction->status }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Générer un Rapport</h5>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('accounting.reports.generate') }}" method="POST">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="report_type" class="form-label">Type de Rapport</label>
                                            <select name="report_type" class="form-control" required>
                                                <option value="income_statement">Compte de Résultat</option>
                                                <option value="balance_sheet">Bilan</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Date de Début</label>
                                            <input type="date" name="start_date" class="form-control" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">Date de Fin</label>
                                            <input type="date" name="end_date" class="form-control" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="shop_id" class="form-label">Magasin (optionnel)</label>
                                            <select name="shop_id" class="form-control">
                                                <option value="">Tous les magasins</option>
                                                @foreach(App\Models\Shop::all() as $shop)
                                                    <option value="{{ $shop->id }}">{{ $shop->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Générer le Rapport</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Période Comptable Actuelle</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Début:</strong> {{ $currentPeriod->start_date->format('d/m/Y') }}</p>
                                    <p><strong>Fin:</strong> {{ $currentPeriod->end_date->format('d/m/Y') }}</p>
                                    <p><strong>Statut:</strong> 
                                        <span class="badge {{ $currentPeriod->status == 'open' ? 'bg-success' : 'bg-danger' }}">
                                            {{ $currentPeriod->status }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
