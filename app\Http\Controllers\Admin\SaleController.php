<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Shop;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class SaleController extends Controller
{
    /**
     * Display a listing of the sales.
     */
    public function index()
    {
        $sales = Sale::with(['shop', 'products'])
            ->latest()
            ->paginate(10);

        return view('admin.sales.index', compact('sales'));
    }

    /**
     * Show the form for creating a new sale.
     */
    public function create()
    {
        $shops = Shop::where('is_active', true)->pluck('name', 'id');
        $products = Product::where('is_active', true)
            ->with('stockDetails')
            ->get()
            ->map(function ($product) {
                $stock = $product->stockDetails->sum('quantity');
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'price' => $product->price,
                    'stock' => $stock,
                    'unit' => $product->unit,
                ];
            });
        $customers = Customer::orderBy('name')->pluck('name', 'id');
        return view('admin.sales.create', compact('shops', 'products', 'customers'));
    }

    /**
     * Store a newly created sale in storage.
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // Validation des données
            $validated = $this->validateSaleData($request);
            
            // Décodage des produits
            $products = $this->parseProducts($validated['products']);
            
            // Vérification du stock
            $this->checkStock($products);
            
            // Création de la vente
            $sale = $this->createSale($validated);
            
            // Association des produits
            $this->attachProducts($sale, $products);
            
            // Mise à jour des stocks
            $this->updateStocks($products);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Vente enregistrée avec succès',
                'sale_id' => $sale->id
            ]);
            
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'error' => $e->getMessage(),
                'errors' => $e->errors()
            ], 422);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur création vente: '.$e->getMessage());
            return response()->json([
                'error' => 'Une erreur est survenue: '.$e->getMessage()
            ], 500);
        }
    }

    protected function validateSaleData(Request $request)
    {
        return $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'shop_id' => 'required|exists:shops,id',
            'payment_method' => 'required|in:cash,card,transfer,mobile',
            'payment_status' => 'required|in:pending,paid,cancelled',
            'amount_paid' => 'required|numeric|min:0',
            'products' => 'required|json'
        ]);
    }

    protected function parseProducts($productsJson)
    {
        $products = json_decode($productsJson, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Format JSON des produits invalide');
        }
        
        return $products;
    }

    protected function checkStock($products)
    {
        foreach ($products as $product) {
            $dbProduct = Product::findOrFail($product['id']);
            
            if ($dbProduct->stock < $product['quantity']) {
                throw new \Exception('Stock insuffisant pour le produit: '.$dbProduct->name);
            }
        }
    }

    protected function createSale($data)
    {
        // Générer le numéro de facture
        $saleCount = Sale::count() + 1;
        $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($saleCount, 5, '0', STR_PAD_LEFT);

        return Sale::create([
            'customer_id' => $data['customer_id'],
            'shop_id' => $data['shop_id'],
            'payment_method' => $data['payment_method'],
            'payment_status' => $data['payment_status'],
            'amount_paid' => $data['amount_paid'],
            'user_id' => auth()->id(),
            'invoice_number' => $invoiceNumber
        ]);
    }

    protected function attachProducts($sale, $products)
    {
        $productsToAttach = [];
        
        foreach ($products as $product) {
            $productsToAttach[$product['id']] = [
                'quantity' => $product['quantity'],
                'price' => $product['price'],
                'unit' => $product['unit'] ?? 'pièce'
            ];
        }
        
        $sale->products()->attach($productsToAttach);
    }

    protected function updateStocks($products)
    {
        foreach ($products as $product) {
            $dbProduct = Product::findOrFail($product['id']);
            $dbProduct->stock -= $product['quantity'];
            $dbProduct->save();
        }
    }

    /**
     * Display the specified sale.
     */
    public function show(Sale $sale)
    {
        $sale->load(['shop', 'products', 'user', 'customer']);
        return view('admin.sales.show', compact('sale'));
    }

    /**
     * Affichage de la facture thermique 80mm pour une vente.
     */
    public function invoice(Sale $sale)
    {
        $sale->load(['shop', 'customer', 'items.product']);
        $shop = $sale->shop;
        $items = $sale->items;
        return view('admin.sales.invoice', compact('sale', 'shop', 'items'));
    }

    /**
     * Update the specified sale status.
     */
    public function updateStatus(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'payment_status' => 'required|in:pending,paid,cancelled'
        ]);

        try {
            DB::beginTransaction();

            $oldStatus = $sale->payment_status;
            $sale->payment_status = $validated['payment_status'];
            $sale->save();

            activity()
                ->performedOn($sale)
                ->causedBy(auth()->user())
                ->withProperties([
                    'old_status' => $oldStatus,
                    'new_status' => $sale->payment_status
                ])
                ->log('sale_status_updated');

            DB::commit();

            return back()->with('success', 'Statut de la vente mis à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour du statut.');
        }
    }
}
