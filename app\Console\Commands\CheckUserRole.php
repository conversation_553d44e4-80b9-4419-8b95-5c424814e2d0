<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckUserRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:check-role {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check roles for a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = \App\Models\User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found!");
            return;
        }

        $this->info("User found: {$user->name}");
        $this->info("Roles:");
        foreach ($user->roles as $role) {
            $this->info("- {$role->name}");
        }

        $this->info("\nPermissions:");
        foreach ($user->getAllPermissions() as $permission) {
            $this->info("- {$permission->name}");
        }
    }
}
