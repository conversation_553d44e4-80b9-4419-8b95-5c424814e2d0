<?php

namespace App\Http\Controllers;

use App\Models\AccountingTransaction;
use App\Models\AccountingCategory;
use App\Models\AccountingPeriod;
use App\Models\FinancialReport;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AccountingController extends Controller
{
    public function dashboard()
    {
        $currentPeriod = AccountingPeriod::where('status', 'open')
            ->latest()
            ->first();

        if (!$currentPeriod) {
            $currentPeriod = AccountingPeriod::create([
                'start_date' => Carbon::now()->startOfMonth(),
                'end_date' => Carbon::now()->endOfMonth(),
                'status' => 'open'
            ]);
        }

        $transactions = AccountingTransaction::with(['shop'])
            ->whereBetween('transaction_date', [$currentPeriod->start_date, $currentPeriod->end_date])
            ->latest()
            ->get();

        $summary = [
            'total_revenue' => $transactions->where('transaction_type', 'revenue')->sum('amount'),
            'total_expenses' => $transactions->where('transaction_type', 'expense')->sum('amount'),
            'net_income' => $transactions->where('transaction_type', 'revenue')->sum('amount') - 
                          $transactions->where('transaction_type', 'expense')->sum('amount'),
            'transaction_count' => $transactions->count()
        ];

        $recentTransactions = $transactions->take(10);

        return view('accounting.dashboard', compact('currentPeriod', 'summary', 'recentTransactions'));
    }

    public function transactions()
    {
        $transactions = AccountingTransaction::with(['shop'])
            ->latest()
            ->paginate(15);

        return view('accounting.transactions.index', compact('transactions'));
    }

    public function createTransaction()
    {
        $categories = AccountingCategory::all();
        $shops = Shop::all();

        return view('accounting.transactions.create', compact('categories', 'shops'));
    }

    public function storeTransaction(Request $request)
    {
        $validated = $request->validate([
            'shop_id' => 'required|exists:shops,id',
            'transaction_type' => 'required|in:revenue,expense',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string',
            'transaction_date' => 'required|date',
            'payment_method' => 'required|string',
            'category_id' => 'required|exists:accounting_categories,id'
        ]);

        DB::transaction(function () use ($validated) {
            AccountingTransaction::create($validated);
        });

        return redirect()->route('accounting.transactions')
            ->with('success', 'Transaction enregistrée avec succès');
    }

    public function generateReport(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'report_type' => 'required|in:income_statement,balance_sheet',
            'shop_id' => 'nullable|exists:shops,id'
        ]);

        $transactions = AccountingTransaction::with(['shop'])
            ->when($validated['shop_id'], function ($query) use ($validated) {
                return $query->where('shop_id', $validated['shop_id']);
            })
            ->whereBetween('transaction_date', [$validated['start_date'], $validated['end_date']])
            ->get();

        $report = [
            'period' => [
                'start_date' => $validated['start_date'],
                'end_date' => $validated['end_date']
            ],
            'summary' => [
                'total_revenue' => $transactions->where('transaction_type', 'revenue')->sum('amount'),
                'total_expenses' => $transactions->where('transaction_type', 'expense')->sum('amount'),
                'net_income' => $transactions->where('transaction_type', 'revenue')->sum('amount') - 
                              $transactions->where('transaction_type', 'expense')->sum('amount')
            ],
            'details' => [
                'revenues' => $transactions->where('transaction_type', 'revenue')
                    ->groupBy('category_id')
                    ->map(function ($group) {
                        return [
                            'category' => $group->first()->category->name,
                            'amount' => $group->sum('amount')
                        ];
                    }),
                'expenses' => $transactions->where('transaction_type', 'expense')
                    ->groupBy('category_id')
                    ->map(function ($group) {
                        return [
                            'category' => $group->first()->category->name,
                            'amount' => $group->sum('amount')
                        ];
                    })
            ]
        ];

        $financialReport = FinancialReport::create([
            'report_type' => $validated['report_type'],
            'accounting_period_id' => AccountingPeriod::where('status', 'open')->first()->id,
            'shop_id' => $validated['shop_id'],
            'content' => $report,
            'status' => 'draft',
            'generated_at' => now()
        ]);

        return view('accounting.reports.show', compact('financialReport'));
    }

    public function reports()
    {
        $reports = FinancialReport::with(['shop', 'period'])
            ->latest()
            ->paginate(15);

        return view('accounting.reports.index', compact('reports'));
    }

    public function validateReport(FinancialReport $report)
    {
        $report->validate();

        return redirect()->route('accounting.reports')
            ->with('success', 'Rapport validé avec succès');
    }
}
