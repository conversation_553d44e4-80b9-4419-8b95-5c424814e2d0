@extends('layouts.admin')

@section('title', 'Ajouter une taxe')

@section('content')
<div class="container mx-auto px-6 py-10">
    <div class="max-w-xl mx-auto bg-white rounded-2xl shadow-lg p-8 mt-8">
        <div class="flex items-center gap-3 mb-6">
            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100">
                <i class="fas fa-plus text-blue-500 text-2xl"></i>
            </div>
            <h2 class="text-2xl font-extrabold text-blue-900">Ajouter une taxe</h2>
        </div>
        <form action="{{ route('admin.accounting.taxes.store') }}" method="POST" class="space-y-5">
            @csrf
            <div>
                <label for="name" class="block text-sm font-bold text-blue-700 mb-1">Nom</label>
                <input type="text" name="name" id="name" class="w-full rounded-lg border border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 px-4 py-2 text-gray-900 shadow-sm transition" required>
            </div>
            <div>
                <label for="rate" class="block text-sm font-bold text-blue-700 mb-1">Taux (%)</label>
                <input type="number" step="0.01" name="rate" id="rate" class="w-full rounded-lg border border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 px-4 py-2 text-gray-900 shadow-sm transition" required>
            </div>
            <div>
                <label for="type" class="block text-sm font-bold text-blue-700 mb-1">Type</label>
                <select name="type" id="type" class="w-full rounded-lg border border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 px-4 py-2 text-gray-900 shadow-sm transition" required>
                    <option value="TVA">TVA</option>
                    <option value="impot">Impôt</option>
                    <option value="autre">Autre</option>
                </select>
            </div>
            <div>
                <label for="description" class="block text-sm font-bold text-blue-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3" class="w-full rounded-lg border border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 px-4 py-2 text-gray-900 shadow-sm transition"></textarea>
            </div>
            <div class="flex gap-3 mt-6">
                <button type="submit" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-400 text-white rounded-lg shadow hover:scale-105 transition-transform font-semibold">
                    <i class="fas fa-save mr-2"></i> Enregistrer
                </button>
                <a href="{{ route('admin.accounting.taxes.index') }}" class="inline-flex items-center px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg shadow transition font-semibold">
                    <i class="fas fa-arrow-left mr-2"></i> Annuler
                </a>
            </div>
        </form>
    </div>
</div>
<!-- FontAwesome CDN (si pas déjà inclus) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"/>
@endsection
