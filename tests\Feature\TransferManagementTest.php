<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use App\Models\Product;
use App\Models\Stock;
use App\Models\Transfer;
use App\Models\TransferItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class TransferManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $admin;
    private $mainShop;
    private $annexShop;
    private $product;
    private $stock;

    protected function setUp(): void
    {
        parent::setUp();

        // Création d'un administrateur
        $this->admin = User::factory()->create(['role' => 'admin']);

        // Création des boutiques
        $this->mainShop = Shop::factory()->create(['is_main' => true]);
        $this->annexShop = Shop::factory()->create(['is_main' => false]);

        // Création d'un produit avec stock
        $this->product = Product::factory()->create();
        $this->stock = Stock::factory()->create([
            'product_id' => $this->product->id,
            'shop_id' => $this->mainShop->id,
            'quantity' => 100
        ]);
    }

    /** @test */
    public function an_admin_can_create_a_transfer()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('transfers.store'), [
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'transfer_date' => now()->format('Y-m-d'),
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 50
                ]
            ]
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('transfers', [
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function it_validates_stock_availability_before_transfer()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('transfers.store'), [
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'transfer_date' => now()->format('Y-m-d'),
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 150 // Plus que le stock disponible
                ]
            ]
        ]);

        $response->assertSessionHasErrors('items.0.quantity');
        $this->assertDatabaseMissing('transfers', [
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id
        ]);
    }

    /** @test */
    public function an_admin_can_validate_a_transfer()
    {
        $this->actingAs($this->admin);

        $transfer = Transfer::factory()->create([
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'pending'
        ]);

        $transferItem = TransferItem::factory()->create([
            'transfer_id' => $transfer->id,
            'product_id' => $this->product->id,
            'quantity' => 50
        ]);

        $response = $this->put(route('transfers.validate', $transfer));

        $response->assertRedirect();
        $this->assertDatabaseHas('transfers', [
            'id' => $transfer->id,
            'status' => 'validated',
            'validated_by' => $this->admin->id
        ]);
    }

    /** @test */
    public function receiving_a_transfer_updates_stock_automatically()
    {
        $this->actingAs($this->admin);

        $transfer = Transfer::factory()->create([
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'validated'
        ]);

        $transferItem = TransferItem::factory()->create([
            'transfer_id' => $transfer->id,
            'product_id' => $this->product->id,
            'quantity' => 50
        ]);

        $response = $this->put(route('transfers.receive', $transfer), [
            'items' => [
                [
                    'id' => $transferItem->id,
                    'received_quantity' => 50
                ]
            ]
        ]);

        $response->assertRedirect();

        // Vérifier que le stock a été mis à jour dans les deux boutiques
        $this->assertDatabaseHas('stocks', [
            'shop_id' => $this->mainShop->id,
            'product_id' => $this->product->id,
            'quantity' => 50 // 100 - 50
        ]);

        $this->assertDatabaseHas('stocks', [
            'shop_id' => $this->annexShop->id,
            'product_id' => $this->product->id,
            'quantity' => 50
        ]);
    }

    /** @test */
    public function it_handles_partial_transfer_reception()
    {
        $this->actingAs($this->admin);

        $transfer = Transfer::factory()->create([
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'validated'
        ]);

        $transferItem = TransferItem::factory()->create([
            'transfer_id' => $transfer->id,
            'product_id' => $this->product->id,
            'quantity' => 50
        ]);

        $response = $this->put(route('transfers.receive', $transfer), [
            'items' => [
                [
                    'id' => $transferItem->id,
                    'received_quantity' => 30,
                    'notes' => 'Réception partielle'
                ]
            ]
        ]);

        $response->assertRedirect();

        // Vérifier les stocks
        $this->assertDatabaseHas('stocks', [
            'shop_id' => $this->mainShop->id,
            'product_id' => $this->product->id,
            'quantity' => 70 // 100 - 30
        ]);

        $this->assertDatabaseHas('stocks', [
            'shop_id' => $this->annexShop->id,
            'product_id' => $this->product->id,
            'quantity' => 30
        ]);

        // Vérifier le statut du transfert
        $this->assertDatabaseHas('transfers', [
            'id' => $transfer->id,
            'status' => 'partially_received'
        ]);
    }

    /** @test */
    public function an_admin_can_cancel_a_pending_transfer()
    {
        $this->actingAs($this->admin);

        $transfer = Transfer::factory()->create([
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'pending'
        ]);

        $transferItem = TransferItem::factory()->create([
            'transfer_id' => $transfer->id,
            'product_id' => $this->product->id,
            'quantity' => 50
        ]);

        $response = $this->put(route('transfers.cancel', $transfer));

        $response->assertRedirect();
        $this->assertDatabaseHas('transfers', [
            'id' => $transfer->id,
            'status' => 'cancelled'
        ]);

        // Vérifier que le stock n'a pas été modifié
        $this->assertDatabaseHas('stocks', [
            'shop_id' => $this->mainShop->id,
            'product_id' => $this->product->id,
            'quantity' => 100
        ]);
    }

    /** @test */
    public function it_prevents_transfer_between_same_shops()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('transfers.store'), [
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->mainShop->id,
            'transfer_date' => now()->format('Y-m-d'),
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 50
                ]
            ]
        ]);

        $response->assertSessionHasErrors('to_shop_id');
    }

    /** @test */
    public function it_prevents_modifying_completed_transfers()
    {
        $this->actingAs($this->admin);

        $transfer = Transfer::factory()->create([
            'from_shop_id' => $this->mainShop->id,
            'to_shop_id' => $this->annexShop->id,
            'status' => 'completed'
        ]);

        $response = $this->put(route('transfers.update', $transfer), [
            'notes' => 'Tentative de modification'
        ]);

        $response->assertStatus(403);
    }
}
