<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupplyItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'supply_id',
        'product_id',
        'ordered_quantity',
        'received_quantity',
        'unit_price',
        'total_price',
        'notes'
    ];

    protected $casts = [
        'ordered_quantity' => 'integer',
        'received_quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2'
    ];

    /**
     * Obtenir l'approvisionnement parent
     */
    public function supply()
    {
        return $this->belongsTo(Supply::class);
    }

    /**
     * Obtenir le produit concerné
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Calculer le total de la ligne
     */
    public function calculateTotal()
    {
        return $this->ordered_quantity * $this->unit_price;
    }

    /**
     * Vérifier si la ligne est complètement reçue
     */
    public function isFullyReceived()
    {
        return $this->received_quantity >= $this->ordered_quantity;
    }
}
