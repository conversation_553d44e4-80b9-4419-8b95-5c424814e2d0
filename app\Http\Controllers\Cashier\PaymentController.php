<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\CashRegister;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        $payments = Payment::with(['sale.customer'])
            ->where('shop_id', auth()->user()->shop_id)
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('cashier.payments.index', compact('payments'));
    }

    public function create(Request $request)
    {
        // Vérifier si la caisse est ouverte
        $currentRegister = CashRegister::where('shop_id', auth()->user()->shop_id)
            ->where('user_id', auth()->id())
            ->whereNull('closed_at')
            ->first();

        if (!$currentRegister) {
            return redirect()->route('cashier.dashboard')
                ->with('error', 'Vous devez d\'abord ouvrir la caisse.');
        }

        $sale = null;
        if ($request->has('sale')) {
            $sale = Sale::with('customer')
                ->where('shop_id', auth()->user()->shop_id)
                ->findOrFail($request->sale);
        }

        $pendingSales = Sale::with('customer')
            ->where('shop_id', auth()->user()->shop_id)
            ->where('payment_status', 'pending')
            ->get();

        return view('cashier.payments.create', compact('sale', 'pendingSales'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:cash,check,bank_transfer',
            'reference' => 'required_if:payment_method,check,bank_transfer',
            'check_number' => 'required_if:payment_method,check',
            'bank_name' => 'required_if:payment_method,check'
        ]);

        // Vérifier si la caisse est ouverte
        $currentRegister = CashRegister::where('shop_id', auth()->user()->shop_id)
            ->where('user_id', auth()->id())
            ->whereNull('closed_at')
            ->first();

        if (!$currentRegister) {
            return back()->with('error', 'La caisse est fermée.');
        }

        try {
            DB::beginTransaction();

            $sale = Sale::findOrFail($request->sale_id);
            
            // Vérifier que le montant ne dépasse pas le reste à payer
            $remainingAmount = $sale->total_amount - $sale->payments()->where('status', 'completed')->sum('amount');
            if ($request->amount > $remainingAmount) {
                throw new \Exception('Le montant du paiement dépasse le reste à payer.');
            }

            // Créer le paiement
            $payment = $sale->payments()->create([
                'shop_id' => auth()->user()->shop_id,
                'user_id' => auth()->id(),
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'check_number' => $request->check_number,
                'bank_name' => $request->bank_name,
                'status' => $request->payment_method === 'check' ? 'pending' : 'completed'
            ]);

            // Mettre à jour le statut de la vente si nécessaire
            $totalPaid = $sale->payments()->where('status', 'completed')->sum('amount');
            if ($totalPaid >= $sale->total_amount) {
                $sale->update(['payment_status' => 'completed']);
            }

            DB::commit();

            return redirect()->route('cashier.payments.receipt', $payment)
                ->with('success', 'Paiement enregistré avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }

    public function printReceipt(Payment $payment)
    {
        $payment->load(['sale.customer', 'sale.items.product', 'user']);
        return view('cashier.payments.receipt', compact('payment'));
    }

    public function dailyReport()
    {
        $date = request('date', today());
        
        $payments = Payment::with(['sale.customer', 'user'])
            ->where('shop_id', auth()->user()->shop_id)
            ->whereDate('created_at', $date)
            ->get();

        $summary = [
            'total' => $payments->where('status', 'completed')->sum('amount'),
            'cash' => $payments->where('status', 'completed')
                            ->where('payment_method', 'cash')
                            ->sum('amount'),
            'check' => $payments->where('status', 'completed')
                            ->where('payment_method', 'check')
                            ->sum('amount'),
            'bank_transfer' => $payments->where('status', 'completed')
                            ->where('payment_method', 'bank_transfer')
                            ->sum('amount')
        ];

        return view('cashier.reports.daily', compact('payments', 'summary', 'date'));
    }

    public function registerReport(Request $request)
    {
        $register = CashRegister::with(['user', 'payments' => function($query) {
                $query->where('status', 'completed');
            }])
            ->where('shop_id', auth()->user()->shop_id)
            ->findOrFail($request->register);

        return view('cashier.reports.register', compact('register'));
    }
}
