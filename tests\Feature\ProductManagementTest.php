<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Shop;
use App\Models\Stock;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ProductManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;
    protected $shop;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer un utilisateur admin
        $this->admin = User::factory()->create()->assignRole('admin');

        // Créer une catégorie
        $this->category = Category::factory()->create();

        // Créer une boutique
        $this->shop = Shop::factory()->create();
    }

    /** @test */
    public function admin_can_view_products_list()
    {
        $products = Product::factory(5)->create([
            'category_id' => $this->category->id
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.index'));

        $response->assertStatus(200)
            ->assertViewIs('products.index')
            ->assertViewHas('products')
            ->assertSee($products->first()->name);
    }

    /** @test */
    public function admin_can_create_new_product()
    {
        Storage::fake('public');

        $productData = [
            'name' => 'Test Product',
            'category_id' => $this->category->id,
            'sku' => 'TEST-001',
            'description' => 'Test description',
            'barcode' => '1234567890123',
            'unit_price' => 1000,
            'selling_price' => 1500,
            'min_stock' => 10,
            'status' => 'active',
            'image' => UploadedFile::fake()->image('product.jpg'),
            'stocks' => [
                $this->shop->id => 20
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('products.store'), $productData);

        $response->assertRedirect(route('products.index'));
        
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'sku' => 'TEST-001'
        ]);

        $product = Product::where('sku', 'TEST-001')->first();
        
        $this->assertDatabaseHas('stocks', [
            'product_id' => $product->id,
            'shop_id' => $this->shop->id,
            'quantity' => 20
        ]);

        Storage::disk('public')->assertExists($product->image_path);
    }

    /** @test */
    public function admin_can_update_product()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $updateData = [
            'name' => 'Updated Product',
            'category_id' => $this->category->id,
            'sku' => $product->sku,
            'description' => 'Updated description',
            'unit_price' => 2000,
            'selling_price' => 3000,
            'min_stock' => 15,
            'status' => 'active'
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('products.update', $product), $updateData);

        $response->assertRedirect(route('products.index'));
        
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'name' => 'Updated Product',
            'unit_price' => 2000
        ]);
    }

    /** @test */
    public function admin_can_delete_product()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('products.destroy', $product));

        $response->assertRedirect(route('products.index'));
        
        $this->assertDatabaseMissing('products', [
            'id' => $product->id
        ]);
    }

    /** @test */
    public function product_requires_valid_data()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('products.store'), []);

        $response->assertSessionHasErrors([
            'name',
            'category_id',
            'sku',
            'unit_price',
            'selling_price',
            'min_stock'
        ]);
    }

    /** @test */
    public function selling_price_must_be_greater_than_unit_price()
    {
        $productData = [
            'name' => 'Test Product',
            'category_id' => $this->category->id,
            'sku' => 'TEST-001',
            'unit_price' => 2000,
            'selling_price' => 1000,
            'min_stock' => 10,
            'status' => 'active'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('products.store'), $productData);

        $response->assertSessionHasErrors('selling_price');
    }

    /** @test */
    public function product_sku_must_be_unique()
    {
        $existingProduct = Product::factory()->create([
            'category_id' => $this->category->id,
            'sku' => 'UNIQUE-001'
        ]);

        $productData = [
            'name' => 'Test Product',
            'category_id' => $this->category->id,
            'sku' => 'UNIQUE-001',
            'unit_price' => 1000,
            'selling_price' => 1500,
            'min_stock' => 10,
            'status' => 'active'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('products.store'), $productData);

        $response->assertSessionHasErrors('sku');
    }

    /** @test */
    public function can_filter_products_by_category()
    {
        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $product1 = Product::factory()->create(['category_id' => $category1->id]);
        $product2 = Product::factory()->create(['category_id' => $category2->id]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.index', ['category' => $category1->id]));

        $response->assertSee($product1->name)
            ->assertDontSee($product2->name);
    }

    /** @test */
    public function can_search_products()
    {
        $product1 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Unique Product Name'
        ]);
        
        $product2 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Different Product'
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('products.index', ['search' => 'Unique']));

        $response->assertSee($product1->name)
            ->assertDontSee($product2->name);
    }
}
