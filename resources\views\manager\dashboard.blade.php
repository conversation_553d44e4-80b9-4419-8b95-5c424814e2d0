@extends('layouts.manager')

@section('title', 'Tableau de bord gérant')

@section('content')
<div class="container-fluid">
    <!-- Statistiques de la boutique -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Ventes du jour</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['daily_sales'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Ventes du mois</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['monthly_sales'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Livraisons en attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['pending_deliveries'] }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Produits en stock critique</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['low_stock_count'] }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Ventes du jour -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Ventes du jour</h6>
                    <a href="{{ route('manager.sales.create') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle vente
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Heure</th>
                                    <th>Client</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($todaySales as $sale)
                                <tr>
                                    <td>{{ $sale->created_at->format('H:i') }}</td>
                                    <td>{{ $sale->customer->name }}</td>
                                    <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                    <td>
                                        <span class="badge badge-{{ $sale->status_color }}">
                                            {{ $sale->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('manager.sales.show', $sale) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">Aucune vente aujourd'hui</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock critique et livraisons -->
        <div class="col-xl-4 col-lg-5">
            <!-- Stock critique -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Produits en stock critique</h6>
                </div>
                <div class="card-body">
                    @if($lowStockProducts->isEmpty())
                        <p class="text-center">Aucun produit en stock critique</p>
                    @else
                        <div class="list-group">
                            @foreach($lowStockProducts as $product)
                            <a href="{{ route('manager.products.show', $product) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                    <small class="text-danger">
                                        {{ $product->stocks->first()->quantity }} unités
                                    </small>
                                </div>
                                <small>Stock minimum : {{ $product->min_stock }}</small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Livraisons en attente -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Livraisons en attente</h6>
                </div>
                <div class="card-body">
                    @if($pendingDeliveries->isEmpty())
                        <p class="text-center">Aucune livraison en attente</p>
                    @else
                        <div class="list-group">
                            @foreach($pendingDeliveries as $delivery)
                            <a href="{{ route('manager.deliveries.show', $delivery) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $delivery->sale->customer->name }}</h6>
                                    <small>{{ $delivery->delivery_date->format('d/m/Y') }}</small>
                                </div>
                                <small>{{ $delivery->delivery_address }}</small>
                            </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
