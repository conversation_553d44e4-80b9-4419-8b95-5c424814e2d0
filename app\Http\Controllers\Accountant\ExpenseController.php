<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExpenseController extends Controller
{
    public function index()
    {
        $expenses = Expense::with(['category', 'user'])
            ->orderBy('date', 'desc')
            ->paginate(20);

        return view('accountant.expenses.index', compact('expenses'));
    }

    public function create()
    {
        $categories = ExpenseCategory::orderBy('name')->get();
        return view('accountant.expenses.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,check,bank_transfer',
            'reference' => 'required_if:payment_method,check,bank_transfer',
            'description' => 'required|string',
            'receipt' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048'
        ]);

        try {
            DB::beginTransaction();

            $expense = Expense::create([
                'date' => $request->date,
                'category_id' => $request->category_id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'description' => $request->description,
                'user_id' => auth()->id()
            ]);

            // Gérer le fichier de reçu
            if ($request->hasFile('receipt')) {
                $path = $request->file('receipt')->store('expenses/receipts', 'public');
                $expense->update(['receipt_path' => $path]);
            }

            DB::commit();

            return redirect()
                ->route('accountant.expenses.index')
                ->with('success', 'Dépense enregistrée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de l\'enregistrement de la dépense.');
        }
    }

    public function show(Expense $expense)
    {
        $expense->load(['category', 'user']);
        return view('accountant.expenses.show', compact('expense'));
    }

    public function edit(Expense $expense)
    {
        $categories = ExpenseCategory::orderBy('name')->get();
        return view('accountant.expenses.edit', compact('expense', 'categories'));
    }

    public function update(Request $request, Expense $expense)
    {
        $request->validate([
            'date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,check,bank_transfer',
            'reference' => 'required_if:payment_method,check,bank_transfer',
            'description' => 'required|string',
            'receipt' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048'
        ]);

        try {
            DB::beginTransaction();

            $expense->update([
                'date' => $request->date,
                'category_id' => $request->category_id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'description' => $request->description
            ]);

            // Gérer le fichier de reçu
            if ($request->hasFile('receipt')) {
                // Supprimer l'ancien reçu s'il existe
                if ($expense->receipt_path) {
                    Storage::disk('public')->delete($expense->receipt_path);
                }
                
                $path = $request->file('receipt')->store('expenses/receipts', 'public');
                $expense->update(['receipt_path' => $path]);
            }

            DB::commit();

            return redirect()
                ->route('accountant.expenses.index')
                ->with('success', 'Dépense mise à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour de la dépense.');
        }
    }

    public function destroy(Expense $expense)
    {
        try {
            // Supprimer le reçu s'il existe
            if ($expense->receipt_path) {
                Storage::disk('public')->delete($expense->receipt_path);
            }

            $expense->delete();

            return redirect()
                ->route('accountant.expenses.index')
                ->with('success', 'Dépense supprimée avec succès.');

        } catch (\Exception $e) {
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la dépense.');
        }
    }
}
