<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use App\Http\Requests\CategoryRequest;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    public function index()
    {
        $categories = Category::withCount('products')
            ->withCount(['products as low_stock_count' => function ($query) {
                $query->whereHas('stockDetails', function ($q) {
                    $q->whereColumn('quantity', '<=', 'min_stock');
                });
            }])
            ->orderBy('name')
            ->paginate(20);

        // Ajout pour le filtre parent : on propose toutes les catégories parentes (parent_id null)
        $parents = Category::whereNull('parent_id')->orderBy('name')->get();

        return view('categories.index', compact('categories', 'parents'));
    }

    public function create()
    {
        $categories = Category::orderBy('name')->get();
        return view('categories.create', compact('categories'));
    }

    public function store(CategoryRequest $request)
    {
        try {
            DB::beginTransaction();

            $category = Category::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'parent_id' => $request->parent_id,
                'status' => $request->status
            ]);

            // Gérer l'icône de la catégorie
            if ($request->hasFile('icon')) {
                $path = $request->file('icon')->store('categories', 'public');
                $category->update(['icon_path' => $path]);
            }

            DB::commit();

            return redirect()
                ->route('categories.index')
                ->with('success', 'Catégorie créée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    public function edit(Category $category)
    {
        $categories = Category::where('id', '!=', $category->id)
            ->where('parent_id', '!=', $category->id)
            ->orderBy('name')
            ->get();

        return view('categories.edit', compact('category', 'categories'));
    }

    public function update(CategoryRequest $request, Category $category)
    {
        try {
            DB::beginTransaction();

            $category->update([
                'name' => $request->name,
                'description' => $request->description,
                'parent_id' => $request->parent_id,
                'status' => $request->status
            ]);

            // Gérer l'icône de la catégorie
            if ($request->hasFile('icon')) {
                // Supprimer l'ancienne icône
                if ($category->icon_path) {
                    Storage::disk('public')->delete($category->icon_path);
                }
                
                $path = $request->file('icon')->store('categories', 'public');
                $category->update(['icon_path' => $path]);
            }

            DB::commit();

            return redirect()
                ->route('categories.index')
                ->with('success', 'Catégorie mise à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de la mise à jour de la catégorie.');
        }
    }

    public function destroy(Category $category)
    {
        try {
            // Vérifier si la catégorie a des produits
            if ($category->products()->exists()) {
                return back()->with('error', 'Cette catégorie ne peut pas être supprimée car elle contient des produits.');
            }

            // Vérifier si la catégorie a des sous-catégories
            if ($category->children()->exists()) {
                return back()->with('error', 'Cette catégorie ne peut pas être supprimée car elle contient des sous-catégories.');
            }

            DB::beginTransaction();

            // Supprimer l'icône
            if ($category->icon_path) {
                Storage::disk('public')->delete($category->icon_path);
            }

            $category->delete();

            DB::commit();

            return redirect()
                ->route('categories.index')
                ->with('success', 'Catégorie supprimée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            // Affichage temporaire du message d'erreur détaillé
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la catégorie : ' . $e->getMessage());
        }
    }

    public function show(Category $category)
    {
        $category->load(['products' => function ($query) {
            $query->withCount(['stocks as total_stock' => function ($q) {
                $q->select(DB::raw('SUM(quantity)'));
            }]);
        }]);

        return view('categories.show', compact('category'));
    }
}
