<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'contact_name',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'tax_number',
        'payment_terms',
        'status'
    ];

    protected $casts = [
        'payment_terms' => 'json',
        'status' => 'boolean'
    ];

    /**
     * Obtenir tous les approvisionnements de ce fournisseur
     */
    public function supplies()
    {
        return $this->hasMany(Supply::class);
    }

    /**
     * Obtenir les produits fournis par ce fournisseur
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'supplier_products')
            ->withPivot('reference_number', 'unit_price', 'minimum_order_quantity')
            ->withTimestamps();
    }
}
