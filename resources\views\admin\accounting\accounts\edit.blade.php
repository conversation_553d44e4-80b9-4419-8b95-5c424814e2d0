@extends('layouts.admin')

@section('title', 'Modifier le compte')

@section('content')
<div class="container mx-auto py-10 max-w-2xl">
    <div class="bg-white shadow-2xl rounded-2xl p-8">
        <div class="flex items-center mb-6">
            <div class="bg-blue-100 text-blue-700 rounded-full p-3 mr-4">
                <i class="fas fa-edit text-2xl animate-pulse"></i>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-blue-900">Modifier le compte</h1>
                <p class="text-gray-500 mt-1">Mettez à jour les informations du compte de façon simple et sécurisée.</p>
            </div>
        </div>

        @if ($errors->any())
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded mb-4">
                <ul class="list-disc pl-5">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('admin.accounting.accounts.update', $account->id) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Code du compte</label>
                    <input type="text" name="code" value="{{ old('code', $account->code) }}" class="form-control rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 w-full" required>
                </div>
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Nom du compte</label>
                    <input type="text" name="name" value="{{ old('name', $account->name) }}" class="form-control rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 w-full" required>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Type de compte</label>
                    <select name="type" class="form-control rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 w-full" required>
                        <option value="">Sélectionner</option>
                        @foreach($types as $type)
                            <option value="{{ $type }}" {{ old('type', $account->type) == $type ? 'selected' : '' }}>{{ ucfirst($type) }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Compte parent</label>
                    <select name="parent_id" class="form-control rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 w-full">
                        <option value="">Aucun</option>
                        @foreach($parents as $parent)
                            <option value="{{ $parent->id }}" {{ old('parent_id', $account->parent_id) == $parent->id ? 'selected' : '' }}>{{ $parent->code }} - {{ $parent->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div>
                <label class="block text-gray-700 font-semibold mb-2">Description</label>
                <textarea name="description" rows="2" class="form-control rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 w-full">{{ old('description', $account->description) }}</textarea>
            </div>

            <div class="flex justify-between mt-8">
                <a href="{{ route('admin.accounting.accounts.index') }}" class="inline-flex items-center px-5 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition">
                    <i class="fas fa-arrow-left mr-2"></i> Retour
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 text-white font-bold shadow-lg hover:from-blue-600 hover:to-blue-800 transition-all text-lg">
                    <i class="fas fa-save mr-2"></i> Enregistrer
                </button>
            </div>
        </form>
    </div>
</div>

@push('styles')
<style>
.form-control {
    border: 1px solid #e5e7eb;
    background: #f9fafb;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: border 0.2s;
}
.form-control:focus {
    border-color: #2563eb;
    background: #fff;
    outline: none;
}
</style>
@endpush

@endsection
