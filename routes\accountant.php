<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Accountant\DashboardController;
use App\Http\Controllers\Accountant\CheckController;
use App\Http\Controllers\Accountant\ExpenseController;
use App\Http\Controllers\Accountant\InvoiceController;
use App\Http\Controllers\Accountant\ReportController;

Route::middleware(['auth', 'role:accountant'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('accountant.dashboard');
    
    // Gestion des chèques
    Route::get('/checks', [CheckController::class, 'index'])->name('accountant.checks.index');
    Route::get('/checks/{payment}', [CheckController::class, 'show'])->name('accountant.checks.show');
    Route::post('/checks/{payment}/process', [CheckController::class, 'process'])->name('accountant.checks.process');
    Route::post('/checks/{payment}/reject', [CheckController::class, 'reject'])->name('accountant.checks.reject');
    
    // Gestion des dépenses
    Route::resource('expenses', ExpenseController::class)->names([
        'index' => 'accountant.expenses.index',
        'create' => 'accountant.expenses.create',
        'store' => 'accountant.expenses.store',
        'show' => 'accountant.expenses.show',
        'edit' => 'accountant.expenses.edit',
        'update' => 'accountant.expenses.update',
        'destroy' => 'accountant.expenses.destroy',
    ]);
    
    // Gestion des factures fournisseurs
    Route::get('/invoices', [InvoiceController::class, 'index'])->name('accountant.invoices.index');
    Route::get('/invoices/{supply}', [InvoiceController::class, 'show'])->name('accountant.invoices.show');
    Route::post('/invoices/{supply}/pay', [InvoiceController::class, 'pay'])->name('accountant.invoices.pay');
    
    // Rapports financiers
    Route::get('/reports/revenue', [ReportController::class, 'revenue'])->name('accountant.reports.revenue');
    Route::get('/reports/expenses', [ReportController::class, 'expenses'])->name('accountant.reports.expenses');
    Route::get('/reports/profit-loss', [ReportController::class, 'profitLoss'])->name('accountant.reports.profit-loss');
    Route::get('/reports/tax', [ReportController::class, 'tax'])->name('accountant.reports.tax');
    
    // Export des rapports
    Route::get('/reports/export/revenue', [ReportController::class, 'exportRevenue'])->name('accountant.reports.export.revenue');
    Route::get('/reports/export/expenses', [ReportController::class, 'exportExpenses'])->name('accountant.reports.export.expenses');
    Route::get('/reports/export/profit-loss', [ReportController::class, 'exportProfitLoss'])->name('accountant.reports.export.profit-loss');
});
